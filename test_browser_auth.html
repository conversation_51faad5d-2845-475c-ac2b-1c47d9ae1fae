<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Azure Entra ID Authentication Test</title>
    <script src="https://alcdn.msauth.net/browser/2.38.0/js/msal-browser.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            cursor: pointer;
            background-color: #0078d4;
            color: white;
            border: none;
            border-radius: 4px;
        }
        button:hover {
            background-color: #106ebe;
        }
        pre {
            background-color: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .result {
            margin-top: 20px;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Azure Entra ID Authentication Test</h1>

        <div>
            <h2>Authentication Configuration</h2>
            <p>Client ID: <span id="clientId">92e443a6-5081-4e5c-aea4-7d8df64d050d</span></p>
            <p>Tenant ID: <span id="tenantId">ee78877a-c63a-405d-85d6-8914358aa533</span></p>
            <p>API URL: <span id="apiUrl">http://localhost:50508</span></p>
        </div>

        <div>
            <h2>Authentication Actions</h2>
            <button id="loginButton">Login with Entra ID</button>
            <button id="logoutButton">Logout</button>
            <button id="getTokenButton">Get Access Token</button>
        </div>

        <div>
            <h2>API Tests</h2>
            <button id="testRegionsButton">Test /api/rbac/regions</button>
            <button id="testUsersButton">Test /api/rbac/users</button>
            <button id="testProjectsButton">Test /api/rbac/projects</button>
            <button id="testMeButton">Test /api/rbac/me</button>
        </div>

        <div class="result">
            <h2>Results</h2>
            <pre id="resultOutput">No results yet.</pre>
        </div>
    </div>

    <script>
        // MSAL configuration
        const msalConfig = {
            auth: {
                clientId: "92e443a6-5081-4e5c-aea4-7d8df64d050d",
                authority: "https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533",
                redirectUri: "http://localhost:8000/test_browser_auth.html", // Using the test page as redirect URI
            },
            cache: {
                cacheLocation: "localStorage", // Use localStorage for better persistence
                storeAuthStateInCookie: true, // Enable cookies for better cross-page authentication
            },
            system: {
                allowRedirectInIframe: true, // Allow redirects in iframe scenarios
                loggerOptions: {
                    loggerCallback: (level, message, containsPii) => {
                        if (!containsPii) {
                            console.log(`MSAL - ${level}: ${message}`);
                        }
                    },
                    logLevel: 3, // Verbose logging for debugging
                    piiLoggingEnabled: false
                }
            }
        };

        // API configuration
        const apiConfig = {
            baseUrl: "http://localhost:50508",
            scopes: ["User.Read"] // Only use specific scopes, not .default
        };

        // Initialize MSAL
        const msalInstance = new msal.PublicClientApplication(msalConfig);

        // Elements
        const resultOutput = document.getElementById('resultOutput');
        const loginButton = document.getElementById('loginButton');
        const logoutButton = document.getElementById('logoutButton');
        const getTokenButton = document.getElementById('getTokenButton');
        const testRegionsButton = document.getElementById('testRegionsButton');
        const testUsersButton = document.getElementById('testUsersButton');
        const testProjectsButton = document.getElementById('testProjectsButton');
        const testMeButton = document.getElementById('testMeButton');

        // Helper function to display results
        function displayResult(result, isError = false) {
            if (typeof result === 'object') {
                result = JSON.stringify(result, null, 2);
            }
            resultOutput.textContent = result;
            resultOutput.className = isError ? 'error' : 'success';
        }

        // Login function
        async function login() {
            try {
                // Check if there's an interaction in progress
                if (msalInstance.getActiveAccount()) {
                    displayResult("Already logged in. Getting token...", false);
                    await getToken();
                    return;
                }

                // Clear any existing interaction
                try {
                    await msalInstance.handleRedirectPromise();
                } catch (e) {
                    console.log("No redirect promise to handle");
                }

                // Try to get accounts first
                const accounts = msalInstance.getAllAccounts();
                if (accounts.length > 0) {
                    displayResult("User is already logged in. Getting token...", false);
                    msalInstance.setActiveAccount(accounts[0]);
                    await getToken();
                    return accounts[0];
                }

                // Proceed with login
                const loginResponse = await msalInstance.loginPopup({
                    scopes: apiConfig.scopes,
                    prompt: "select_account"
                });

                displayResult("Login successful", false);
                console.log("Login successful", loginResponse);
                return loginResponse;
            } catch (error) {
                if (error.message && error.message.includes("interaction_in_progress")) {
                    displayResult("Login already in progress. Please complete the login in the popup window or try again in a few seconds.", true);
                    // Wait a bit and try to get accounts
                    setTimeout(async () => {
                        try {
                            const accounts = msalInstance.getAllAccounts();
                            if (accounts.length > 0) {
                                displayResult("User is now logged in. Getting token...", false);
                                msalInstance.setActiveAccount(accounts[0]);
                                await getToken();
                            }
                        } catch (e) {
                            console.error("Error checking accounts after delay", e);
                        }
                    }, 3000);
                } else {
                    displayResult("Login failed: " + error.message, true);
                    console.error("Login failed", error);
                }
                return null;
            }
        }

        // Logout function
        async function logout() {
            try {
                await msalInstance.logoutPopup();
                displayResult("Logout successful", false);
            } catch (error) {
                displayResult("Logout failed: " + error.message, true);
                console.error("Logout failed", error);
            }
        }

        // Get token function
        async function getToken() {
            try {
                // Try to get the active account first
                let account = msalInstance.getActiveAccount();

                // If no active account, try to get all accounts
                if (!account) {
                    const accounts = msalInstance.getAllAccounts();
                    if (accounts.length === 0) {
                        throw new Error("No accounts found. Please login first.");
                    }
                    account = accounts[0];
                    msalInstance.setActiveAccount(account);
                }

                const silentRequest = {
                    scopes: apiConfig.scopes,
                    account: account
                };

                try {
                    // Try to acquire token silently
                    const tokenResponse = await msalInstance.acquireTokenSilent(silentRequest);
                    displayResult("Token acquired successfully:\n" + tokenResponse.accessToken.substring(0, 20) + "...", false);
                    return tokenResponse.accessToken;
                } catch (silentError) {
                    // If silent acquisition fails, try popup
                    if (silentError instanceof msal.InteractionRequiredAuthError) {
                        console.log("Silent token acquisition failed, acquiring token using popup");
                        try {
                            const tokenResponse = await msalInstance.acquireTokenPopup({
                                scopes: apiConfig.scopes
                            });
                            displayResult("Token acquired successfully:\n" + tokenResponse.accessToken.substring(0, 20) + "...", false);
                            return tokenResponse.accessToken;
                        } catch (popupError) {
                            if (popupError.message && popupError.message.includes("interaction_in_progress")) {
                                displayResult("Token acquisition already in progress. Please complete the process in the popup window or try again in a few seconds.", true);
                                return null;
                            } else {
                                throw popupError;
                            }
                        }
                    } else {
                        throw silentError;
                    }
                }
            } catch (error) {
                displayResult("Failed to get token: " + error.message, true);
                console.error("Failed to get token", error);
                return null;
            }
        }

        // API call function
        async function callApi(endpoint) {
            try {
                // First check if we're logged in
                const accounts = msalInstance.getAllAccounts();
                if (accounts.length === 0) {
                    // Try to login first
                    displayResult("Not logged in. Attempting to login...", false);
                    await login();
                }

                // Now try to get a token
                const token = await getToken();
                if (!token) {
                    throw new Error("No token available. Please login and get a token first.");
                }

                displayResult(`Calling API endpoint: ${endpoint}...`, false);

                const response = await fetch(`${apiConfig.baseUrl}${endpoint}`, {
                    headers: {
                        "Authorization": `Bearer ${token}`
                    }
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`API call failed with status: ${response.status} - ${errorText}`);
                }

                try {
                    const data = await response.json();
                    displayResult(data, false);
                    return data;
                } catch (jsonError) {
                    // If response is not JSON
                    const text = await response.text();
                    displayResult(`API returned non-JSON response: ${text}`, false);
                    return text;
                }
            } catch (error) {
                displayResult("API call failed: " + error.message, true);
                console.error("API call failed", error);
                return null;
            }
        }

        // Event listeners
        loginButton.addEventListener('click', login);
        logoutButton.addEventListener('click', logout);
        getTokenButton.addEventListener('click', getToken);
        testRegionsButton.addEventListener('click', () => callApi('/api/rbac/regions'));
        testUsersButton.addEventListener('click', () => callApi('/api/rbac/users'));
        testProjectsButton.addEventListener('click', () => callApi('/api/rbac/projects'));
        testMeButton.addEventListener('click', () => callApi('/api/rbac/me'));

        // Initialize and check authentication state
        window.addEventListener('load', async () => {
            try {
                // Handle redirect promise first
                await msalInstance.handleRedirectPromise()
                    .then((response) => {
                        if (response) {
                            displayResult("Successfully logged in after redirect", false);
                        }
                    })
                    .catch(error => {
                        console.log("No redirect response to handle or error occurred", error);
                    });

                // Check if user is already logged in
                const accounts = msalInstance.getAllAccounts();
                if (accounts.length > 0) {
                    msalInstance.setActiveAccount(accounts[0]);
                    displayResult(`User is already logged in as ${accounts[0].username}`, false);

                    // Try to get a token silently
                    try {
                        await getToken();
                    } catch (e) {
                        console.log("Could not get token silently", e);
                    }
                } else {
                    displayResult("Not logged in. Please click 'Login with Entra ID' to authenticate.", false);
                }
            } catch (error) {
                console.error("Error during initialization", error);
                displayResult("Error during initialization: " + error.message, true);
            }
        });
    </script>
</body>
</html>
