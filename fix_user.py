import os
import asyncio
from dotenv import load_dotenv
from backend.rbac.cosmosdb_rbac_service import CosmosRbacClient

async def fix_user():
    load_dotenv()
    cosmos_endpoint = f'https://{os.environ.get("AZURE_COSMOSDB_ACCOUNT")}.documents.azure.com:443/'
    cosmos_client = CosmosRbacClient(
        cosmosdb_endpoint=cosmos_endpoint, 
        credential=os.environ.get('AZURE_COSMOSDB_ACCOUNT_KEY'), 
        database_name=os.environ.get('AZURE_COSMOSDB_DATABASE')
    )
    
    print('Initializing CosmosDB client...')
    init_success = await cosmos_client.initialize()
    print(f'Initialization success: {init_success}')
    
    if init_success:
        print('Testing connection...')
        success, message = await cosmos_client.ensure()
        print(f'Connection test: {success}, Message: {message}')
        
        if success:
            # Get the user from Microsoft Graph API
            entra_user_id = '7e43ed9b-4998-468e-bcd1-9b78f7c82977'
            entra_user_name = '<PERSON>'
            entra_user_email = '<PERSON>.<PERSON>Mevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com'
            
            # Check if the user already exists in CosmosDB
            existing_user = await cosmos_client.get_user(entra_user_id)
            
            if existing_user:
                print(f'User already exists in CosmosDB: {existing_user}')
                print('Updating user role to SUPER_ADMIN...')
                
                # Update the user's role to SUPER_ADMIN
                existing_user['role'] = 'SUPER_ADMIN'
                await cosmos_client.update_user(existing_user)
                print('User updated successfully')
            else:
                print(f'User does not exist in CosmosDB. Creating new user...')
                
                # Create a new user with the Entra ID
                new_user = {
                    'id': entra_user_id,
                    'name': entra_user_name,
                    'email': entra_user_email,
                    'role': 'SUPER_ADMIN',
                    'type': 'user'
                }
                
                await cosmos_client.create_user(new_user)
                print('User created successfully')
            
            # Verify the user now exists and has the correct role
            updated_user = await cosmos_client.get_user(entra_user_id)
            print(f'Updated user: {updated_user}')
            
    await cosmos_client.close()

if __name__ == "__main__":
    asyncio.run(fix_user())
