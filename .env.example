# Azure Storage Configuration
AZURE_STORAGE_CONTAINER_NAME=uploads-app3

# Entra ID Configuration for Authentication
AZURE_CLIENT_ID=your-client-id-here
AZURE_TENANT_ID=your-tenant-id-here
AZURE_APP_SECRET=your-app-secret-here

# Frontend Environment Variables (prefixed with VITE_)
VITE_AZURE_CLIENT_ID=${AZURE_CLIENT_ID}
VITE_AZURE_TENANT_ID=${AZURE_TENANT_ID}

# Authentication Configuration
# Set to "true" to use Entra ID authentication for local development
USE_ENTRA_AUTH=false

# Development Mode
# Set to "true" to enable development mode features
DEVELOPMENT_MODE=true
