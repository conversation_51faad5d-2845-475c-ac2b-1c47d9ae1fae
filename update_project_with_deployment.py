#!/usr/bin/env python3
"""
Update a project in CosmosDB with deployment summary.
This script demonstrates how to use the deployment summary to update a project in CosmosDB.
"""

import os
import sys
import json
import logging
import argparse
from datetime import datetime, timezone
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def update_project_with_deployment(project_id, deployment_summary):
    """
    Update a project in CosmosDB with deployment summary.

    Args:
        project_id (str): The ID of the project
        deployment_summary (dict): Dictionary with deployment summary

    Returns:
        bool: True if update was successful, False otherwise
    """
    logger.info(f"Updating project {project_id} with deployment summary")

    try:
        # Log the deployment summary
        logger.info(f"Deployment summary: {json.dumps(deployment_summary, indent=2)}")

        # Import the CosmosClient
        try:
            from azure.cosmos import CosmosClient
        except ImportError:
            logger.error("azure-cosmos package not installed. Install it with: pip install azure-cosmos")
            return False

        # Get CosmosDB connection details from environment variables
        # For projects, we use the conversation history database but with the 'projects' container
        cosmos_account = os.environ.get("AZURE_COSMOSDB_ACCOUNT", os.environ.get("COSMOSDB_ACCOUNT"))
        cosmos_key = os.environ.get("AZURE_COSMOSDB_ACCOUNT_KEY", os.environ.get("COSMOSDB_KEY"))
        cosmos_database = os.environ.get("AZURE_COSMOSDB_DATABASE", os.environ.get("COSMOSDB_DATABASE"))
        cosmos_container = "projects"  # Always use 'projects' container

        # Check if all required environment variables are set
        if not cosmos_account:
            logger.error("AZURE_COSMOSDB_ACCOUNT or COSMOSDB_ACCOUNT environment variable not set")
            return False
        if not cosmos_key:
            logger.error("AZURE_COSMOSDB_ACCOUNT_KEY or COSMOSDB_KEY environment variable not set")
            return False
        if not cosmos_database:
            logger.error("AZURE_COSMOSDB_DATABASE or COSMOSDB_DATABASE environment variable not set")
            return False

        logger.info(f"Using CosmosDB account: {cosmos_account}, database: {cosmos_database}, container: {cosmos_container}")

        # Create a CosmosClient
        endpoint = f"https://{cosmos_account}.documents.azure.com:443/"
        client = CosmosClient(endpoint, cosmos_key)
        logger.info(f"Created CosmosClient for {endpoint}")

        # Get the database
        database = client.get_database_client(cosmos_database)
        logger.info(f"Got database client for {cosmos_database}")

        # Get the projects container
        projects_container = database.get_container_client(cosmos_container)
        logger.info(f"Got container client for {cosmos_container}")

        # Query for the project
        query = "SELECT * FROM c WHERE c.id = @projectId AND c.type = 'project'"
        parameters = [{"name": "@projectId", "value": project_id}]

        # Execute the query
        projects = list(projects_container.query_items(
            query=query,
            parameters=parameters,
            enable_cross_partition_query=True
        ))

        if not projects:
            logger.warning(f"Project {project_id} not found in CosmosDB")
            # Return False to indicate that the project was not found
            # The calling code can then decide to create the project if needed
            return False

        # Get the project document
        project_doc = projects[0]
        logger.info(f"Found project {project_id}")

        # Extract resource data from deployment summary
        resources = deployment_summary.get("resources", {})

        # Map deployment summary fields to project document fields
        field_mapping = {
            "storage_account_name": "storage_account_name",
            "uploads_container": "storage_container_uploads",
            "input_container": "storage_container_input",
            "output_container": "storage_container_output",
            "search_service_name": "search_service_name",
            "search_index_name": "search_index_name",
            "search_indexer_name": "search_indexer_name",
            "search_datasource_name": "search_datasource_name",
            "function_app_name": "function_app_name",
            "function_app_url": "function_app_url",
            "event_grid_system_topic_name": "event_grid_system_topic_name",
            "event_grid_subscription_name": "event_grid_subscription_name"
        }

        # Update the project document with resource data
        updated = False
        for src_field, dest_field in field_mapping.items():
            if src_field in resources and resources[src_field]:
                project_doc[dest_field] = resources[src_field]
                updated = True
                logger.info(f"Updated {dest_field} to {resources[src_field]}")

        # Initialize or get the environment object
        if "environment" not in project_doc:
            project_doc["environment"] = {}

        # Extract environment variables from resources
        env_vars = {
            "AZURE_SEARCH_KEY": resources.get("search_key"),  # Handle both naming conventions
            "AZURE_SEARCH_SEMANTIC_SEARCH_CONFIG": resources.get("azure_search_semantic_search_config"),
            "STORAGE_ACCOUNT_SAS_TOKEN": resources.get("storage_account_sas_token"),
            "FUNCTION_KEY_MATURITY": resources.get("function_key_maturity"),
            "FUNCTION_KEY_EXECUTIVE_SUMMARY": resources.get("function_key_executive_summary"),
            "FUNCTION_KEY_POWERPOINT": resources.get("function_key_powerpoint"),
            "AZURE_FUNCTION_MATURITY_ASSESSMENT_URL": resources.get("azure_function_maturity_assessment_url"),
            "AZURE_FUNCTION_EXECUTIVE_SUMMARY_URL": resources.get("azure_function_executive_summary_url")
        }

        # Ensure we have the correct storage and search service names in the environment
        if resources.get("storage_account_name"):
            env_vars["STORAGE_ACCOUNT_NAME"] = resources.get("storage_account_name")

        # Add container names to environment variables
        if resources.get("uploads_container"):
            env_vars["STORAGE_CONTAINER_UPLOADS"] = resources.get("uploads_container")

        if resources.get("input_container"):
            env_vars["STORAGE_CONTAINER_INPUT"] = resources.get("input_container")

        if resources.get("output_container"):
            env_vars["STORAGE_CONTAINER_OUTPUT"] = resources.get("output_container")

        if resources.get("search_service_name"):
            env_vars["SEARCH_SERVICE_NAME"] = resources.get("search_service_name")

        if resources.get("search_index_name"):
            env_vars["SEARCH_INDEX"] = resources.get("search_index_name")

        # Update environment variables in the project document
        for key, value in env_vars.items():
            if value:
                project_doc["environment"][key] = value
                updated = True
                logger.info(f"Updated environment variable {key}")

        # If there are environment variables directly in the deployment summary, use those too
        if "environment" in deployment_summary and deployment_summary["environment"]:
            for key, value in deployment_summary["environment"].items():
                if value:
                    project_doc["environment"][key] = value
                    updated = True
                    logger.info(f"Updated environment variable {key} from deployment summary")

        # Update the timestamp
        if updated:
            project_doc["updated_at"] = datetime.now(timezone.utc).isoformat()

            # Update deployment status
            if "deployment_status" not in project_doc:
                project_doc["deployment_status"] = {}

            project_doc["deployment_status"]["status"] = deployment_summary.get("status", "success")
            project_doc["deployment_status"]["message"] = "Project deployment completed"
            project_doc["deployment_status"]["updated_at"] = datetime.now(timezone.utc).isoformat()

            # Update the project document
            projects_container.replace_item(
                item=project_id,
                body=project_doc
            )
            logger.info(f"Successfully updated project {project_id} in CosmosDB")

            # Notify the WebSocket service to update the project configuration
            try:
                import requests

                # Get the WebSocket service URL from environment or use default
                websocket_service_url = os.environ.get("WEBSOCKET_SERVICE_URL", "http://localhost:50505")
                project_update_url = f"{websocket_service_url}/api/project-update"

                # Call the project-update endpoint
                logger.info(f"Notifying WebSocket service to update project {project_id}")
                response = requests.post(
                    project_update_url,
                    json={"project_id": project_id, "user_id": "anonymous"}
                )

                if response.status_code == 200:
                    logger.info(f"Successfully notified WebSocket service: {response.json()}")
                else:
                    logger.warning(f"Failed to notify WebSocket service: {response.status_code} - {response.text}")
            except Exception as e:
                logger.warning(f"Error notifying WebSocket service: {e}")

            return True
        else:
            logger.info("No changes needed to project document")
            return True

    except Exception as e:
        logger.error(f"Error updating project with deployment summary: {e}")
        return False

def create_dummy_deployment_summary(project_id, project_name="test-project"):
    """
    Create a dummy deployment summary for testing.

    Args:
        project_id (str): The ID of the project
        project_name (str): The name of the project

    Returns:
        dict: Dummy deployment summary
    """
    return {
        "project_id": project_id,
        "project_name": project_name,
        "region_id": "a50e18f3-178d-169a-70a50-a51a52a53a54a55a56",
        "resources": {
            "storage_account_name": f"st{project_name.lower().replace('-', '').replace('_', '')}01",
            "storage_account_sas_token": "?sv=2021-06-08&ss=bfqt&srt=sco&sp=rwdlacupitfx&se=2023-06-01T00:00:00Z&st=2022-06-01T00:00:00Z&spr=https&sig=DUMMY_SAS_TOKEN",
            "uploads_container": f"uploads-{project_name.lower()}-01",
            "input_container": f"input-{project_name.lower()}-01",
            "output_container": f"output-{project_name.lower()}-01",
            "search_service_name": f"search-{project_name.lower()}-01",
            "search_index_name": f"project-{project_name.lower()}-index",
            "search_indexer_name": f"project-{project_name.lower()}-indexer",
            "search_key": "DUMMY_SEARCH_API_KEY_00000000000000000000000000000000",  # Use consistent naming with actual deployment summaries
            "azure_search_semantic_search_config": "default",
            "search_datasource_name": f"project-{project_name.lower()}-ds",
            "function_app_name": f"func-{project_name.lower()}-01",
            "function_app_url": f"https://func-{project_name.lower()}-01.azurewebsites.net",
            "function_key_maturity": "DUMMY_FUNCTION_KEY_MATURITY",
            "function_key_executive_summary": "DUMMY_FUNCTION_KEY_EXECUTIVE_SUMMARY",
            "function_key_powerpoint": "DUMMY_FUNCTION_KEY_POWERPOINT",
            "azure_function_maturity_assessment_url": "https://func-dummy-test.azurewebsites.net/api/HttpTriggerAppMaturityAssessment",
            "azure_function_executive_summary_url": "https://func-dummy-test.azurewebsites.net/api/HttpTriggerAppExecutiveSummary",
            "event_grid_system_topic_name": f"evgt-{project_name.lower()}-01",
            "event_grid_subscription_name": f"evgs-{project_name.lower()}-01"
        },
        "status": "success",
        "deployment_time": "5s",
        "timestamp": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
    }

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Update a project in CosmosDB with deployment summary")
    parser.add_argument("project_id", help="The UUID of the project")
    parser.add_argument("--summary-file", help="Path to the deployment summary JSON file")
    parser.add_argument("--generate-dummy", action="store_true", help="Generate a dummy deployment summary for testing")

    args = parser.parse_args()

    # Get the deployment summary
    deployment_summary = None

    if args.summary_file:
        # Read from file
        try:
            with open(args.summary_file, 'r') as f:
                deployment_summary = json.load(f)
            logger.info(f"Read deployment summary from {args.summary_file}")
        except Exception as e:
            logger.error(f"Error reading deployment summary file: {e}")
            return 1
    elif args.generate_dummy:
        # Generate a dummy deployment summary
        try:
            # Generate the dummy summary using our local function
            project_name = f"test-{datetime.now().strftime('%m%d%H%M')}"
            deployment_summary = create_dummy_deployment_summary(args.project_id, project_name)
            logger.info(f"Generated dummy deployment summary for project {args.project_id}")

            # Save the dummy summary to a file for reference
            dummy_file = f"dummy_deployment_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(dummy_file, 'w') as f:
                json.dump(deployment_summary, f, indent=2)
            logger.info(f"Saved dummy deployment summary to {dummy_file}")
        except Exception as e:
            logger.error(f"Error generating dummy deployment summary: {e}")
            return 1
    else:
        logger.error("No deployment summary provided. Use --summary-file or --generate-dummy")
        return 1

    # Update the project
    success = update_project_with_deployment(args.project_id, deployment_summary)

    if success:
        logger.info(f"Successfully updated project {args.project_id} with deployment summary")
        return 0
    else:
        logger.error(f"Failed to update project {args.project_id} with deployment summary")
        return 1

if __name__ == "__main__":
    sys.exit(main())
