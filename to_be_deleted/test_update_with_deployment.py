#!/usr/bin/env python3
"""
Test script to update a project in CosmosDB with deployment summary.
This script is used to test the update_project_with_deployment function.
If the project doesn't exist, it will create it first.
"""

import os
import sys
import json
import logging
import uuid
from datetime import datetime, timezone
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# Import the update_project_with_deployment function
try:
    from update_project_with_deployment import update_project_with_deployment
except ImportError:
    logger.error("Failed to import update_project_with_deployment. Make sure the file exists and is in the Python path.")
    sys.exit(1)

# Import CosmosDB client
try:
    from azure.cosmos import CosmosClient, exceptions
except ImportError:
    logger.error("azure-cosmos package not installed. Install it with: pip install azure-cosmos")
    sys.exit(1)

def create_project(project_id, project_name, region_id, deployment_summary=None):
    """
    Create a new project in CosmosDB.

    Args:
        project_id (str): The ID of the project
        project_name (str): The name of the project
        region_id (str): The ID of the region
        deployment_summary (dict, optional): Dictionary with deployment summary

    Returns:
        bool: True if creation was successful, False otherwise
    """
    logger.info(f"Creating new project {project_id} with name {project_name} in region {region_id}")

    try:
        # Get CosmosDB connection details from environment variables
        cosmos_account = os.environ.get("AZURE_COSMOSDB_ACCOUNT", os.environ.get("COSMOSDB_ACCOUNT"))
        cosmos_key = os.environ.get("AZURE_COSMOSDB_ACCOUNT_KEY", os.environ.get("COSMOSDB_KEY"))
        cosmos_database = os.environ.get("AZURE_COSMOSDB_DATABASE", os.environ.get("COSMOSDB_DATABASE"))
        cosmos_container = os.environ.get("AZURE_COSMOSDB_PROJECTS_CONTAINER", "projects")

        # Check if all required environment variables are set
        if not cosmos_account:
            logger.error("AZURE_COSMOSDB_ACCOUNT or COSMOSDB_ACCOUNT environment variable not set")
            return False
        if not cosmos_key:
            logger.error("AZURE_COSMOSDB_ACCOUNT_KEY or COSMOSDB_KEY environment variable not set")
            return False
        if not cosmos_database:
            logger.error("AZURE_COSMOSDB_DATABASE or COSMOSDB_DATABASE environment variable not set")
            return False

        # Create a CosmosClient
        endpoint = f"https://{cosmos_account}.documents.azure.com:443/"
        client = CosmosClient(endpoint, cosmos_key)
        logger.info(f"Created CosmosClient for {endpoint}")

        # Get the database
        database = client.get_database_client(cosmos_database)
        logger.info(f"Got database client for {cosmos_database}")

        # Get the projects container
        projects_container = database.get_container_client(cosmos_container)
        logger.info(f"Got container client for {cosmos_container}")

        # Create the project document
        now = datetime.now(timezone.utc).isoformat()

        # Initialize the project document
        project_doc = {
            "id": project_id,
            "type": "project",
            "name": project_name,
            "region": region_id,
            "created_at": now,
            "updated_at": now,
            "status": "active",
            "deployment_status": {
                "status": "pending",
                "message": "Project created, awaiting deployment",
                "updated_at": now
            }
        }

        # If deployment summary is provided, extract resource data
        if deployment_summary:
            resources = deployment_summary.get("resources", {})

            # Add resource information to the project document
            if "storage_account_name" in resources:
                project_doc["storage_account_name"] = resources["storage_account_name"]
            if "uploads_container" in resources:
                project_doc["storage_container_uploads"] = resources["uploads_container"]
            if "input_container" in resources:
                project_doc["storage_container_input"] = resources["input_container"]
            if "output_container" in resources:
                project_doc["storage_container_output"] = resources["output_container"]
            if "search_service_name" in resources:
                project_doc["search_service_name"] = resources["search_service_name"]
            if "search_index_name" in resources:
                project_doc["search_index_name"] = resources["search_index_name"]
            if "search_indexer_name" in resources:
                project_doc["search_indexer_name"] = resources["search_indexer_name"]
            if "search_datasource_name" in resources:
                project_doc["search_datasource_name"] = resources["search_datasource_name"]
            if "function_app_name" in resources:
                project_doc["function_app_name"] = resources["function_app_name"]
            if "function_app_url" in resources:
                project_doc["function_app_url"] = resources["function_app_url"]

            # Initialize or get the environment object
            project_doc["environment"] = {}

            # Extract environment variables from resources
            env_vars = {
                "AZURE_SEARCH_KEY": resources.get("azure_search_key"),
                "AZURE_SEARCH_SEMANTIC_SEARCH_CONFIG": resources.get("azure_search_semantic_search_config"),
                "STORAGE_ACCOUNT_SAS_TOKEN": resources.get("storage_account_sas_token"),
                "FUNCTION_KEY_MATURITY": resources.get("function_key_maturity"),
                "FUNCTION_KEY_EXECUTIVE_SUMMARY": resources.get("function_key_executive_summary"),
                "FUNCTION_KEY_POWERPOINT": resources.get("function_key_powerpoint"),
                "AZURE_FUNCTION_MATURITY_ASSESSMENT_URL": resources.get("azure_function_maturity_assessment_url"),
                "AZURE_FUNCTION_EXECUTIVE_SUMMARY_URL": resources.get("azure_function_executive_summary_url")
            }

            # Ensure we have the correct storage and search service names in the environment
            if resources.get("storage_account_name"):
                env_vars["STORAGE_ACCOUNT_NAME"] = resources.get("storage_account_name")

            # Add container names to environment variables
            if resources.get("uploads_container"):
                env_vars["STORAGE_CONTAINER_UPLOADS"] = resources.get("uploads_container")

            if resources.get("input_container"):
                env_vars["STORAGE_CONTAINER_INPUT"] = resources.get("input_container")

            if resources.get("output_container"):
                env_vars["STORAGE_CONTAINER_OUTPUT"] = resources.get("output_container")

            if resources.get("search_service_name"):
                env_vars["SEARCH_SERVICE_NAME"] = resources.get("search_service_name")

            if resources.get("search_index_name"):
                env_vars["SEARCH_INDEX"] = resources.get("search_index_name")

            # Update environment variables in the project document
            for key, value in env_vars.items():
                if value:
                    project_doc["environment"][key] = value

            # Update deployment status
            project_doc["deployment_status"]["status"] = deployment_summary.get("status", "success")
            project_doc["deployment_status"]["message"] = "Project deployment completed"
            project_doc["deployment_status"]["updated_at"] = now

        # Create the project document in CosmosDB
        try:
            created_doc = projects_container.create_item(body=project_doc)
            logger.info(f"Successfully created project {project_id} in CosmosDB")
            return True
        except exceptions.CosmosResourceExistsError:
            logger.warning(f"Project {project_id} already exists in CosmosDB")
            return False
        except Exception as e:
            logger.error(f"Error creating project in CosmosDB: {e}")
            return False

    except Exception as e:
        logger.error(f"Error creating project: {e}")
        return False

def main():
    """Main entry point for the script."""
    if len(sys.argv) < 2:
        print("Usage: python test_update_with_deployment.py <project_id> [deployment_summary_file] [project_name] [region_id]")
        print("Example: python test_update_with_deployment.py 5c4a937a-50b1-4509-8f2e-ed2c0ba57d60 deployment_summary_5c4a937a-50b1-4509-8f2e-ed2c0ba57d60.json 'My Project' westeurope")
        return 1

    project_id = sys.argv[1]

    # Default values for project name and region
    project_name = "Default Project"
    region_id = "westeurope"

    # Check if project name and region were provided
    if len(sys.argv) >= 4:
        project_name = sys.argv[3]
    if len(sys.argv) >= 5:
        region_id = sys.argv[4]

    # Check if a deployment summary file was provided
    if len(sys.argv) >= 3:
        deployment_summary_file = sys.argv[2]
        try:
            with open(deployment_summary_file, 'r') as f:
                deployment_summary = json.load(f)
            logger.info(f"Loaded deployment summary from {deployment_summary_file}")
        except Exception as e:
            logger.error(f"Error loading deployment summary file: {e}")
            return 1
    else:
        # Look for a deployment summary file with the project ID
        deployment_summary_file = f"deployment_summary_{project_id}.json"
        try:
            with open(deployment_summary_file, 'r') as f:
                deployment_summary = json.load(f)
            logger.info(f"Loaded deployment summary from {deployment_summary_file}")
        except FileNotFoundError:
            logger.error(f"Deployment summary file not found: {deployment_summary_file}")
            logger.error("Please provide a deployment summary file or create one first.")
            return 1
        except Exception as e:
            logger.error(f"Error loading deployment summary file: {e}")
            return 1

    # Extract project name and region from deployment summary if not provided
    if project_name == "Default Project" and "project_name" in deployment_summary:
        project_name = deployment_summary["project_name"]
        logger.info(f"Using project name from deployment summary: {project_name}")

    if region_id == "westeurope" and "region_id" in deployment_summary:
        region_id = deployment_summary["region_id"]
        logger.info(f"Using region ID from deployment summary: {region_id}")

    # Print the CosmosDB connection details
    cosmos_account = os.environ.get("AZURE_COSMOSDB_ACCOUNT", os.environ.get("COSMOSDB_ACCOUNT"))
    cosmos_database = os.environ.get("AZURE_COSMOSDB_DATABASE", os.environ.get("COSMOSDB_DATABASE"))
    cosmos_container = os.environ.get("AZURE_COSMOSDB_PROJECTS_CONTAINER", "projects")

    logger.info(f"Using CosmosDB account: {cosmos_account}")
    logger.info(f"Using CosmosDB database: {cosmos_database}")
    logger.info(f"Using CosmosDB container: {cosmos_container}")

    # Update the project with the deployment summary
    success = update_project_with_deployment(project_id, deployment_summary)

    if success:
        logger.info(f"Successfully updated project {project_id} with deployment summary")
        return 0
    else:
        logger.warning(f"Failed to update project {project_id} with deployment summary. Attempting to create it...")

        # Try to create the project
        create_success = create_project(project_id, project_name, region_id, deployment_summary)

        if create_success:
            logger.info(f"Successfully created project {project_id} with deployment summary")
            return 0
        else:
            logger.error(f"Failed to create project {project_id}")
            return 1

if __name__ == "__main__":
    sys.exit(main())
