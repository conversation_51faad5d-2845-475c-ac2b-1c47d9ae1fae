[{"name": "REACT_APP_API_BASE_URL", "value": "http://localhost:8000/api ", "slotSetting": false}, {"name": "AZURE_FUNCTION_MATURITY_ASSESSMENT_KEY", "value": "l82oi1xb6av7aEP_SEkV8mOH_lwiloe4hdvmekHDQmINAzFug1wq8g==", "slotSetting": false}, {"name": "AZURE_FUNCTION_MATURITY_ASSESSMENT_URL", "value": "https://FunctionWebapp3.azurewebsites.net/api/HttpTriggerAppMaturityAssessment", "slotSetting": false}, {"name": "AZURE_FUNCTION_EXECUTIVE_SUMMARY_KEY", "value": "tEP22s-qj5Ps6rnUp7mRcLAKDeLwrFEhp0zG5oAsGWekAzFuiqvjJQ==", "slotSetting": false}, {"name": "AZURE_FUNCTION_EXECUTIVE_SUMMARY_URL", "value": "https://functionwebapp4.azurewebsites.net/api/execSummary?", "slotSetting": false}, {"name": "AZURE_STORAGE_CONTAINER_NAME", "value": "uploads-app3", "slotSetting": false}, {"name": "AZURE_STORAGE_CONTAINER_NAME_INPUT", "value": "maturity-input-container-app3", "slotSetting": false}, {"name": "AZURE_STORAGE_CONTAINER_NAME_OUTPUT", "value": "maturity-output-container-app3", "slotSetting": false}, {"name": "AZURE_STORAGE_ACCOUNT_NAME", "value": "rgin<PERSON><PERSON><PERSON>", "slotSetting": false}, {"name": "AZURE_STORAGE_CONTAINER_SAS_TOKEN", "value": "?sv=2022-11-02&ss=bfqt&srt=sco&sp=rwdlacupiytfx&se=2026-01-13T21:17:48Z&st=2025-03-12T13:17:48Z&sip=**************&spr=https,http&sig=2ej%2FCUl35IQS74o%2BhIyGEHMYnU9LBEmtg5NPRPGCRNI%3D", "slotSetting": false}, {"name": "DEBUG", "value": "True", "slotSetting": false}, {"name": "AZURE_OPENAI_RESOURCE", "value": "ai-scope-openai", "slotSetting": false}, {"name": "AZURE_OPENAI_MODEL", "value": "gpt-4o-ai-scope", "slotSetting": false}, {"name": "AZURE_OPENAI_KEY", "value": "2b84bd8e5c1749789f4ba5cccb84cfdf", "slotSetting": false}, {"name": "AZURE_OPENAI_MODEL_NAME", "value": "gpt-4o", "slotSetting": false}, {"name": "AZURE_OPENAI_TEMPERATURE", "value": "0", "slotSetting": false}, {"name": "AZURE_OPENAI_TOP_P", "value": "1.0", "slotSetting": false}, {"name": "AZURE_OPENAI_MAX_TOKENS", "value": "1000", "slotSetting": false}, {"name": "AZURE_OPENAI_STOP_SEQUENCE", "value": "", "slotSetting": false}, {"name": "AZURE_OPENAI_SEED", "value": "", "slotSetting": false}, {"name": "AZURE_OPENAI_CHOICES_COUNT", "value": "1", "slotSetting": false}, {"name": "AZURE_OPENAI_PRESENCE_PENALTY", "value": "0.0", "slotSetting": false}, {"name": "AZURE_OPENAI_FREQUENCY_PENALTY", "value": "0.0", "slotSetting": false}, {"name": "AZURE_OPENAI_LOGIT_BIAS", "value": "", "slotSetting": false}, {"name": "AZURE_OPENAI_USER", "value": "", "slotSetting": false}, {"name": "AZURE_OPENAI_TOOLS", "value": "", "slotSetting": false}, {"name": "AZURE_OPENAI_TOOL_CHOICE", "value": "", "slotSetting": false}, {"name": "AZURE_OPENAI_SYSTEM_MESSAGE", "value": "You are an AI IT sales asssistant, helping keyrus analyse companies", "slotSetting": false}, {"name": "AZURE_OPENAI_PREVIEW_API_VERSION", "value": "2024-05-01-preview", "slotSetting": false}, {"name": "AZURE_OPENAI_STREAM", "value": "True", "slotSetting": false}, {"name": "AZURE_OPENAI_ENDPOINT", "value": "https://ai-scope-openai.openai.azure.com/", "slotSetting": false}, {"name": "AZURE_OPENAI_EMBEDDING_NAME", "value": "text-embedding-ada-002-ai-scope", "slotSetting": false}, {"name": "AZURE_OPENAI_EMBEDDING_ENDPOINT", "value": "https://ai-scope-openai.openai.azure.com/openai/deployments/text-embedding-ada-002-ai-scope/embeddings?api-version=2023-05-15", "slotSetting": false}, {"name": "AZURE_OPENAI_EMBEDDING_KEY", "value": "2b84bd8e5c1749789f4ba5cccb84cfdf", "slotSetting": false}, {"name": "UI_TITLE", "value": "AI scoping assistant", "slotSetting": false}, {"name": "UI_LOGO", "value": "https://images.ctfassets.net/te2janzw7nut/1k38fRLGCdPTDBILanEe0k/3da972954bd6a651b19d0af1b4bc758b/logo-keyrus.svg", "slotSetting": false}, {"name": "UI_CHAT_LOGO", "value": "https://images.ctfassets.net/te2janzw7nut/1k38fRLGCdPTDBILanEe0k/3da972954bd6a651b19d0af1b4bc758b/logo-keyrus.svg", "slotSetting": false}, {"name": "UI_CHAT_TITLE", "value": "Explore Your Data", "slotSetting": false}, {"name": "UI_CHAT_DESCRIPTION", "value": "", "slotSetting": false}, {"name": "UI_FAVICON", "value": "https://images.ctfassets.net/te2janzw7nut/1k38fRLGCdPTDBILanEe0k/3da972954bd6a651b19d0af1b4bc758b/logo-keyrus.svg", "slotSetting": false}, {"name": "AZURE_COSMOSDB_ACCOUNT", "value": "internal-ai-conversation-history-db", "slotSetting": false}, {"name": "AZURE_COSMOSDB_DATABASE", "value": "db_conversation_history", "slotSetting": false}, {"name": "AZURE_COSMOSDB_CONVERSATIONS_CONTAINER", "value": "conversations", "slotSetting": false}, {"name": "AZURE_COSMOSDB_ACCOUNT_KEY", "value": "pNgnkPf6Sr24BPimwFURmrcPX7dWDlbagPXbdgfvVyv0ms7fFyK3FcLgIhVDa07pslzHK3MBEoHEACDbfEEfNg==", "slotSetting": false}, {"name": "AZURE_COSMOSDB_ENABLE_FEEDBACK", "value": "False", "slotSetting": false}, {"name": "DATASOURCE_TYPE", "value": "AzureCognitiveSearch", "slotSetting": false}, {"name": "SEARCH_TOP_K", "value": "5", "slotSetting": false}, {"name": "SEARCH_STRICTNESS", "value": "3", "slotSetting": false}, {"name": "SEARCH_ENABLE_IN_DOMAIN", "value": "True", "slotSetting": false}, {"name": "AZURE_SEARCH_API_VERSION", "value": "2021-04-30-Preview", "slotSetting": false}, {"name": "AZURE_SEARCH_INDEXER", "value": "ai-scope-app3-indexer", "slotSetting": false}, {"name": "AZURE_SEARCH_SERVICE", "value": "ai-scope-ai-search", "slotSetting": false}, {"name": "AZURE_SEARCH_INDEX", "value": "ai-scope-app3", "slotSetting": false}, {"name": "AZURE_SEARCH_KEY", "value": "****************************************************", "slotSetting": false}, {"name": "AZURE_SEARCH_USE_SEMANTIC_SEARCH", "value": "True", "slotSetting": false}, {"name": "AZURE_SEARCH_SEMANTIC_SEARCH_CONFIG", "value": "ai-scope-app3-semantic-configuration", "slotSetting": false}, {"name": "AZURE_SEARCH_INDEX_IS_PRECHUNKED", "value": "False", "slotSetting": false}, {"name": "AZURE_SEARCH_TOP_K", "value": "5", "slotSetting": false}, {"name": "AZURE_SEARCH_ENABLE_IN_DOMAIN", "value": "False", "slotSetting": false}, {"name": "AZURE_SEARCH_CONTENT_COLUMNS", "value": "", "slotSetting": false}, {"name": "AZURE_SEARCH_FILENAME_COLUMN", "value": "", "slotSetting": false}, {"name": "AZURE_SEARCH_TITLE_COLUMN", "value": "", "slotSetting": false}, {"name": "AZURE_SEARCH_URL_COLUMN", "value": "", "slotSetting": false}, {"name": "AZURE_SEARCH_VECTOR_COLUMNS", "value": "", "slotSetting": false}, {"name": "AZURE_SEARCH_QUERY_TYPE", "value": "vectorSemanticHybrid", "slotSetting": false}, {"name": "AZURE_SEARCH_PERMITTED_GROUPS_COLUMN", "value": "", "slotSetting": false}, {"name": "AZURE_SEARCH_STRICTNESS", "value": "3", "slotSetting": false}, {"name": "AZURE_COSMOSDB_MONGO_VCORE_CONNECTION_STRING", "value": "", "slotSetting": false}, {"name": "AZURE_COSMOSDB_MONGO_VCORE_DATABASE", "value": "", "slotSetting": false}, {"name": "AZURE_COSMOSDB_MONGO_VCORE_CONTAINER", "value": "", "slotSetting": false}, {"name": "AZURE_COSMOSDB_MONGO_VCORE_INDEX", "value": "", "slotSetting": false}, {"name": "AZURE_COSMOSDB_MONGO_VCORE_INDEX", "value": "", "slotSetting": false}, {"name": "AZURE_COSMOSDB_MONGO_VCORE_TOP_K", "value": "", "slotSetting": false}, {"name": "AZURE_COSMOSDB_MONGO_VCORE_STRICTNESS", "value": "", "slotSetting": false}, {"name": "AZURE_COSMOSDB_MONGO_VCORE_ENABLE_IN_DOMAIN", "value": "", "slotSetting": false}, {"name": "AZURE_COSMOSDB_MONGO_VCORE_CONTENT_COLUMNS", "value": "", "slotSetting": false}, {"name": "AZURE_COSMOSDB_MONGO_VCORE_FILENAME_COLUMN", "value": "", "slotSetting": false}, {"name": "AZURE_COSMOSDB_MONGO_VCORE_TITLE_COLUMN", "value": "", "slotSetting": false}, {"name": "AZURE_COSMOSDB_MONGO_VCORE_URL_COLUMN", "value": "", "slotSetting": false}, {"name": "AZURE_COSMOSDB_MONGO_VCORE_VECTOR_COLUMNS", "value": "", "slotSetting": false}, {"name": "ELASTICSEARCH_ENDPOINT", "value": "", "slotSetting": false}, {"name": "ELASTICSEARCH_ENCODED_API_KEY", "value": "", "slotSetting": false}, {"name": "ELASTICSEARCH_INDEX", "value": "", "slotSetting": false}, {"name": "ELASTICSEARCH_QUERY_TYPE", "value": "", "slotSetting": false}, {"name": "ELASTICSEARCH_TOP_K", "value": "", "slotSetting": false}, {"name": "ELASTICSEARCH_ENABLE_IN_DOMAIN", "value": " ", "slotSetting": false}, {"name": "ELASTICSEARCH_CONTENT_COLUMNS", "value": " ", "slotSetting": false}, {"name": "ELASTICSEARCH_FILENAME_COLUMN", "value": "", "slotSetting": false}, {"name": "ELASTICSEARCH_TITLE_COLUMN", "value": "", "slotSetting": false}, {"name": "ELASTICSEARCH_URL_COLUMN", "value": "", "slotSetting": false}, {"name": "ELASTICSEARCH_VECTOR_COLUMNS", "value": "", "slotSetting": false}, {"name": "ELASTICSEARCH_STRICTNESS", "value": "", "slotSetting": false}, {"name": "ELASTICSEARCH_EMBEDDING_MODEL_ID", "value": " ", "slotSetting": false}, {"name": "PINECONE_ENVIRONMENT", "value": "", "slotSetting": false}, {"name": "PINECONE_API_KEY", "value": "", "slotSetting": false}, {"name": "PINECONE_INDEX_NAME", "value": "", "slotSetting": false}, {"name": "PINECONE_TOP_K", "value": "", "slotSetting": false}, {"name": "PINECONE_STRICTNESS", "value": "", "slotSetting": false}, {"name": "PINECONE_ENABLE_IN_DOMAIN", "value": "", "slotSetting": false}, {"name": "PINECONE_CONTENT_COLUMNS", "value": "", "slotSetting": false}, {"name": "PINECONE_FILENAME_COLUMN", "value": "", "slotSetting": false}, {"name": "PINECONE_TITLE_COLUMN", "value": "", "slotSetting": false}, {"name": "PINECONE_URL_COLUMN", "value": "", "slotSetting": false}, {"name": "PINECONE_VECTOR_COLUMNS", "value": " ", "slotSetting": false}, {"name": "AZURE_MLINDEX_NAME", "value": "", "slotSetting": false}, {"name": "AZURE_MLINDEX_VERSION", "value": "", "slotSetting": false}, {"name": "AZURE_ML_PROJECT_RESOURCE_ID", "value": "", "slotSetting": false}, {"name": "AZURE_MLINDEX_TOP_K", "value": " ", "slotSetting": false}, {"name": "AZURE_MLINDEX_STRICTNESS", "value": " ", "slotSetting": false}, {"name": "AZURE_MLINDEX_ENABLE_IN_DOMAIN", "value": "", "slotSetting": false}, {"name": "AZURE_MLINDEX_CONTENT_COLUMNS", "value": " ", "slotSetting": false}, {"name": "AZURE_MLINDEX_FILENAME_COLUMN", "value": "", "slotSetting": false}, {"name": "AZURE_MLINDEX_TITLE_COLUMN", "value": " ", "slotSetting": false}, {"name": "AZURE_MLINDEX_URL_COLUMN", "value": " ", "slotSetting": false}, {"name": "AZURE_MLINDEX_VECTOR_COLUMNS", "value": "", "slotSetting": false}, {"name": "AZURE_MLINDEX_QUERY_TYPE", "value": "", "slotSetting": false}, {"name": "USE_PROMPTFLOW", "value": "False", "slotSetting": false}, {"name": "PROMPTFLOW_ENDPOINT", "value": "", "slotSetting": false}, {"name": "PROMPTFLOW_API_KEY", "value": "", "slotSetting": false}, {"name": "PROMPTFLOW_RESPONSE_TIMEOUT", "value": "120", "slotSetting": false}, {"name": "PROMPTFLOW_REQUEST_FIELD_NAME", "value": "query", "slotSetting": false}, {"name": "PROMPTFLOW_RESPONSE_FIELD_NAME", "value": "reply", "slotSetting": false}, {"name": "PROMPTFLOW_CITATIONS_FIELD_NAME", "value": "documents", "slotSetting": false}, {"name": "MONGODB_ENDPOINT", "value": "", "slotSetting": false}, {"name": "MONGODB_USERNAME", "value": "", "slotSetting": false}, {"name": "MONGODB_PASSWORD", "value": "", "slotSetting": false}, {"name": "MONGODB_DATABASE_NAME", "value": "", "slotSetting": false}, {"name": "MONGODB_COLLECTION_NAME", "value": "", "slotSetting": false}, {"name": "MONGODB_APP_NAME", "value": "", "slotSetting": false}, {"name": "MONGODB_INDEX_NAME", "value": "", "slotSetting": false}, {"name": "MONGODB_TOP_K", "value": "", "slotSetting": false}, {"name": "MONGODB_STRICTNESS", "value": "", "slotSetting": false}, {"name": "MONGODB_ENABLE_IN_DOMAIN", "value": "", "slotSetting": false}, {"name": "MONGODB_CONTENT_COLUMNS", "value": "", "slotSetting": false}, {"name": "MONGODB_FILENAME_COLUMN", "value": "", "slotSetting": false}, {"name": "MONGODB_TITLE_COLUMN", "value": "", "slotSetting": false}, {"name": "MONGODB_URL_COLUMN", "value": "", "slotSetting": false}, {"name": "MONGODB_VECTOR_COLUMNS", "value": "", "slotSetting": false}, {"name": "ALLOWED_ORIGINS", "value": "http://localhost:50505,http://127.0.0.1:50505,http://localhost:50506,http://127.0.0.1:50506,https://ai-scope-app3.azurewebsites.net,https://ai-scope-app3.azurewebsites.net", "slotSetting": false}, {"name": "API_PORT", "value": "8000", "slotSetting": false}]