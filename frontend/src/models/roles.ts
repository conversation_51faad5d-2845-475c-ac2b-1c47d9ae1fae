// Role definitions for the application
export enum UserRole {
  SUPER_ADMIN = 'SUPER_ADMIN',
  REGIONAL_ADMIN = 'REGIONAL_ADMIN',
  REGULAR_USER = 'REGULAR_USER'
}

// Forward declaration or import for UserContextData's accessibleResources structure
// This avoids circular dependencies if UserContextData is also defined here or imported elsewhere.
// For simplicity, defining a local type that matches the structure.
interface AccessibleResourcesStructure {
  projects: any[]; // Consider defining a more specific Project type if available
  teams: any[];    // Consider defining a Team type
  regions: any[];  // Consider defining a Region type
  users: any[];    // Consider defining a User type
}

// User profile interface
export interface UserProfile {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  region?: string; // Optional for Super Admin, required for Regional Admin and Regular User
  avatar?: string; // Optional profile picture URL
  permissions?: RolePermissions; // Permissions from the backend
  accessibleResources?: AccessibleResourcesStructure; // Added to hold resources
}

// Project permissions based on role
export interface RolePermissions {
  canCreateProject: boolean;
  canEditProject: boolean;
  canDeleteProject: boolean;
  canAssignUsers: boolean;
  canSetCostLimits: boolean;
  canAccessAdminPanel: boolean;
  canManageUsers: boolean;
  canManageGlobalSettings: boolean;
  canCreateTeams: boolean;
  canAssignProjects: boolean;
  canAssignTeams: boolean;
  canSetupRegionalAdmins: boolean;
  canTagUsers: boolean;
  canViewAllRegions: boolean;
}

// Get permissions based on user role
export function getRolePermissions(role: UserRole): RolePermissions {
  switch (role) {
    case UserRole.SUPER_ADMIN:
      return {
        canCreateProject: true,
        canEditProject: true,
        canDeleteProject: true,
        canAssignUsers: true,
        canSetCostLimits: true,
        canAccessAdminPanel: true,
        canManageUsers: true,
        canManageGlobalSettings: true,
        canCreateTeams: true,
        canAssignProjects: true,
        canAssignTeams: true,
        canSetupRegionalAdmins: true,
        canTagUsers: true,
        canViewAllRegions: true
      };
    case UserRole.REGIONAL_ADMIN:
      return {
        canCreateProject: true,
        canEditProject: true,
        canDeleteProject: true,
        canAssignUsers: true,
        canSetCostLimits: false, // Regional admins cannot set cost limits
        canAccessAdminPanel: true,
        canManageUsers: true,
        canManageGlobalSettings: false, // Regional admins cannot access global settings
        canCreateTeams: true, // Regional admins can create teams
        canAssignProjects: true, // Regional admins can assign projects to users/teams
        canAssignTeams: true, // Regional admins can assign teams to users
        canSetupRegionalAdmins: false, // Only super admins can set up regional admins
        canTagUsers: true, // Regional admins can tag users
        canViewAllRegions: false // Regional admins can only see their region
      };
    case UserRole.REGULAR_USER:
    default:
      return {
        canCreateProject: false,
        canEditProject: false,
        canDeleteProject: false,
        canAssignUsers: false,
        canSetCostLimits: false,
        canAccessAdminPanel: false,
        canManageUsers: false,
        canManageGlobalSettings: false,
        canCreateTeams: false,
        canAssignProjects: false,
        canAssignTeams: false,
        canSetupRegionalAdmins: false,
        canTagUsers: false,
        canViewAllRegions: false
      };
  }
}

// Region interface
export interface Region {
  id: string;
  name: string;
  description?: string;
  costLimit?: number; // Cost limit set by Super Admin for the region
}

// Team interface
export interface Team {
  id: string;
  name: string;
  description?: string;
  region: string; // Region this team belongs to
  projects: string[]; // Array of project IDs assigned to this team
  members: string[]; // Array of user IDs who are members of this team
  createdBy: string; // ID of the user who created the team
  createdAt: string; // Creation date
}

// User Tag interface
export interface UserTag {
  id: string;
  name: string;
  color: string;
  createdBy: string; // ID of the user who created the tag
}

// Project-specific permissions interface
export interface ProjectPermissions {
  canViewProject: boolean;
  canEditProject: boolean;
  canDeleteProject: boolean;
  canManageProjectUsers: boolean;
  canManageProjectTeams: boolean;
  canUploadFiles: boolean;
  canDownloadFiles: boolean;
  canRunAnalysis: boolean;
  canViewAnalysisResults: boolean;
  projectRole: string | null;
}
