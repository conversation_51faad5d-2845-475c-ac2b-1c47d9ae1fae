import React, { createContext, useState, useEffect, ReactNode } from 'react';
import { UserRole } from '../models/roles';

// Define the User interface
export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  region?: string;
  regionName?: string;
}

// Define the context interface
interface UserContextType {
  currentUser: User | null;
  setCurrentUser: (user: User | null) => void;
  isLoading: boolean;
  error: string | null;
}

// Create the context with default values
export const UserContext = createContext<UserContextType>({
  currentUser: null,
  setCurrentUser: () => {},
  isLoading: false,
  error: null
});

// Props for the provider component
interface UserProviderProps {
  children: ReactNode;
}

// Mock user for development
const mockUser: User = {
  id: '1',
  name: '<PERSON>',
  email: '<EMAIL>',
  role: UserRole.SUPER_ADMIN,
  region: 'region-1',
  regionName: 'West US 2'
};

// Create the provider component
export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch the current user on component mount
  useEffect(() => {
    const fetchCurrentUser = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // In a real app, this would be an API call
        // For now, use the mock user
        setTimeout(() => {
          setCurrentUser(mockUser);
          setIsLoading(false);
        }, 500);
      } catch (err) {
        setError('Failed to fetch user data');
        setIsLoading(false);
        console.error('Error fetching user data:', err);
      }
    };

    fetchCurrentUser();
  }, []);

  return (
    <UserContext.Provider value={{ currentUser, setCurrentUser, isLoading, error }}>
      {children}
    </UserContext.Provider>
  );
};

// Custom hook for using the user context
export const useUser = () => {
  const context = React.useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};
