import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMsal } from '@azure/msal-react';
import { InteractionStatus, EventType, EventMessage } from '@azure/msal-browser';
import { authService } from '../services/authService';
import { navigateToProjects, navigateToLogin } from '../utils/navigation';
import axios from 'axios';

// Fixed port configuration
const FIXED_PORT = '50508';

// Define the redirect URI that matches what's configured in Azure Portal
const REDIRECT_URI = `http://localhost:${FIXED_PORT}/auth/callback`;

/**
 * Auth Callback Component
 *
 * This component handles the redirect from Entra ID authentication.
 * It manages token exchange, storage, and backend communication.
 */
const AuthCallback: React.FC = () => {
  const { instance, accounts, inProgress } = useMsal();
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    const handleCallback = async () => {
      try {
        console.log('AuthCallback - Starting authentication callback handling');
        console.log('MSAL accounts:', accounts);
        console.log('MSAL inProgress:', inProgress);

        // Get the active account
        const activeAccount = instance.getActiveAccount();
        if (!activeAccount) {
          throw new Error('No active account found');
        }

        // Get access token with required scopes
        const token = await instance.acquireTokenSilent({
          account: activeAccount,
          scopes: ['User.Read', 'openid', 'profile', 'offline_access']
        });

        if (!token?.accessToken) {
          throw new Error('Failed to acquire access token');
        }

        // Store token in localStorage and secure cookie
        localStorage.setItem('accessToken', token.accessToken);
        document.cookie = `msalToken=${token.accessToken}; Secure; SameSite=Strict; Path=/`;

        // Verify token with backend
        try {
          const backendResponse = await axios.get(`${REDIRECT_URI}/verify-token`, {
            headers: {
              'Authorization': `Bearer ${token.accessToken}`
            }
          });

          if (backendResponse.status === 200) {
            console.log('Token verified with backend');
            navigateToProjects();
          } else {
            throw new Error('Backend token verification failed');
          }
        } catch (verifyError) {
          console.error('Token verification failed:', verifyError);
          setError('Failed to verify token with backend. Please try logging in again.');
          navigateToLogin();
        }
      } catch (error) {
        console.error('Authentication error:', error);
        setError(`Authentication error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        navigateToLogin();
      } finally {
        setLoading(false);
      }
    };

    handleCallback();
  }, [instance, accounts]);

  const renderContent = () => {
    if (error) {
      return (
        <div style={{ color: 'red', marginBottom: '20px' }}>
          <h2>Authentication Error</h2>
          <p>{error}</p>
          <p>Redirecting to login page...</p>
        </div>
      );
    }

    if (loading) {
      return (
        <>
          <h2>Completing Authentication</h2>
          <p>Please wait while we complete the authentication process...</p>
          <div style={{
            width: '50px',
            height: '50px',
            border: '5px solid #f3f3f3',
            borderTop: '5px solid #3498db',
            borderRadius: '50%',
            animation: 'spin 2s linear infinite',
            marginTop: '20px'
          }} />
          <style>
            {`
              @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
              }
            `}
          </style>
        </>
      );
    }

    return null;
  };

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100vh',
      padding: '20px',
      textAlign: 'center'
    }}>
      {renderContent()}
    </div>
  );
};

export default AuthCallback;
