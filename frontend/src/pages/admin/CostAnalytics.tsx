import React, { useState, useEffect } from 'react';
import {
  Stack,
  Text,
  Dropdown,
  IDropdownOption,
  Spinner,
  SpinnerSize,
  MessageBar,
  MessageBarType,
  IStackTokens,
  Label,
  Toggle,
  ChoiceGroup,
  IChoiceGroupOption,
  SearchBox,
  DetailsList,
  DetailsListLayoutMode,
  SelectionMode,
  IColumn
} from '@fluentui/react';
import { <PERSON>, Pie, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
  ChartEvent
} from 'chart.js';
import costService, {
  CostData,
  ProjectCost,
  ServiceCost,
  RegionCost,
  ResourceCost,
  ContainerCost,
  IndexerCost,
  CostFilterOptions
} from '../../services/costService';
import { UserRole } from '../../models/roles';
import { useUser } from '../../state/UserProvider';

// Function to create the percentage labels plugin with access to costData
const createBudgetThresholdPlugin = (currentCostData: CostData | null) => ({
  id: 'budgetThresholdPlugin',
  afterDraw(chart: any) {
    const {ctx, data, chartArea, scales} = chart;

    // Draw percentage labels on top of each bar
    if (data.datasets.length > 0 && currentCostData) {
      const dataset = data.datasets[0];
      const meta = chart.getDatasetMeta(0);

      for (let i = 0; i < meta.data.length; i++) {
        const project = currentCostData.projectCosts[i];
        if (!project) continue;

        const percentUsed = (project.cost / project.budget) * 100;
        let color;
        if (percentUsed >= 100) {
          color = RED_400; // Red for over budget
        } else if (percentUsed >= 80) {
          color = ORANGE_400; // Orange for approaching budget
        } else {
          color = CYAN_400; // Cyan for under budget
        }

        const bar = meta.data[i];
        const {x, y, width} = bar;

        ctx.save();
        ctx.textAlign = 'center';
        ctx.textBaseline = 'bottom';
        ctx.font = 'bold 14px Arial';
        ctx.fillStyle = color;

        // Position the text above the bar
        ctx.fillText(`${percentUsed.toFixed(0)}%`, x, y - 10);

        // If over budget, draw a horizontal line at the budget level
        if (percentUsed > 100) {
          const budgetHeight = project.budget / project.cost * (bar.base - bar.y);
          const budgetY = bar.base - budgetHeight;

          ctx.beginPath();
          ctx.moveTo(x - width/2, budgetY);
          ctx.lineTo(x + width/2, budgetY);
          ctx.lineWidth = 2;
          ctx.strokeStyle = '#000';
          ctx.stroke();
        }

        ctx.restore();
      }
    }
  }
});

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement
);

// Define stack tokens for spacing
const stackTokens: IStackTokens = { childrenGap: 20 };
const chartContainerTokens: IStackTokens = { childrenGap: 15 };

// Time range options
const timeRangeOptions: IDropdownOption[] = [
  { key: 'week', text: 'Last 7 Days' },
  { key: 'month', text: 'This Month' },
  { key: 'quarter', text: 'Last 3 Months' },
  { key: 'year', text: 'Year to Date' }
];

// Resource type options
const resourceTypeOptions: IChoiceGroupOption[] = [
  { key: 'all', text: 'All Resources' },
  { key: 'storage', text: 'Storage Accounts' },
  { key: 'search', text: 'Search Services' },
  { key: 'function', text: 'Function Apps' },
  { key: 'shared', text: 'Shared Resources' }
];

// Color constants
const CYAN_400 = '#0EA5E9';
const ORANGE_400 = '#F97316';
const RED_400 = '#EF4444';
const BLUE_PALETTE = [
  'rgba(14, 165, 233, 1)',    // darker blue
  'rgba(56, 189, 248, 1)',    // medium blue
  'rgba(125, 211, 252, 1)',   // lighter blue
  'rgba(186, 230, 253, 1)',   // very light blue
  'rgba(224, 242, 254, 1)',   // lightest blue
];

const CostAnalytics: React.FC = () => {
  const { currentUser, isLoading: isUserLoading } = useUser();

  const userRole = currentUser?.role ?? UserRole.REGULAR_USER;
  const userRegionId = currentUser?.region;

  // State for cost data and filters
  const [costData, setCostData] = useState<CostData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Filter states
  const [selectedTimeRange, setSelectedTimeRange] = useState<string>('month');
  const [selectedRegionId, setSelectedRegionId] = useState<string | undefined>(undefined);
  const [selectedProjectId, setSelectedProjectId] = useState<string | undefined>(undefined);
  const [selectedResourceType, setSelectedResourceType] = useState<string>('all');
  const [showSharedResources, setShowSharedResources] = useState<boolean>(true);

  // View states
  const [activeView, setActiveView] = useState<string>('overview');
  const [searchQuery, setSearchQuery] = useState<string>('');

  // Region options for dropdown (derived from cost data)
  const [regionOptions, setRegionOptions] = useState<IDropdownOption[]>([]);
  const [projectOptions, setProjectOptions] = useState<IDropdownOption[]>([]);

  // Load cost data on component mount and when filters change
  useEffect(() => {
    if (isUserLoading || !currentUser) {
      return;
    }

    const loadCostData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // For Regional Admins, always filter by their region
        const effectiveRegionId = userRole === UserRole.REGIONAL_ADMIN
          ? userRegionId
          : selectedRegionId;

        // Apply all filters
        const filters: Partial<CostFilterOptions> = {
          timeRange: selectedTimeRange,
          regionId: effectiveRegionId,
          projectId: selectedProjectId,
          resourceType: selectedResourceType !== 'all' ? selectedResourceType : undefined,
          showSharedResources: showSharedResources
        };

        // Get cost data with user role-based filtering
        const data = await costService.getCostData(
          selectedTimeRange,
          userRole,
          userRegionId,
          filters
        );

        setCostData(data);

        // Update dropdown options based on the data
        if (data) {
          // Build region options (only for Super Admins)
          if (userRole === UserRole.SUPER_ADMIN) {
            const regions = data.regionCosts.map(r => ({
              key: r.regionId,
              text: r.region
            }));
            setRegionOptions([{ key: 'all', text: 'All Regions' }, ...regions]);
          }

          // Build project options
          const projects = data.projectCosts.map(p => ({
            key: p.projectId,
            text: p.project
          }));
          setProjectOptions([{ key: 'all', text: 'All Projects' }, ...projects]);
        }
      } catch (err) {
        setError('Failed to load cost data. Please try again later.');
        console.error('Error loading cost data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadCostData();
  }, [
    selectedTimeRange,
    // Only include selectedRegionId in the dependency array for Super Admins
    ...(userRole === UserRole.SUPER_ADMIN ? [selectedRegionId] : []),
    selectedProjectId,
    selectedResourceType,
    showSharedResources,
    userRole,
    userRegionId,
    isUserLoading,
    currentUser
  ]);

  // Handle filter changes
  const handleTimeRangeChange = (event: React.FormEvent<HTMLDivElement>, option?: IDropdownOption) => {
    if (option) {
      setSelectedTimeRange(option.key as string);
    }
  };

  const handleRegionChange = (event: React.FormEvent<HTMLDivElement>, option?: IDropdownOption) => {
    if (option) {
      setSelectedRegionId(option.key === 'all' ? undefined : option.key as string);
    }
  };

  const handleProjectChange = (event: React.FormEvent<HTMLDivElement>, option?: IDropdownOption) => {
    if (option) {
      setSelectedProjectId(option.key === 'all' ? undefined : option.key as string);
    }
  };

  const handleResourceTypeChange = (ev?: React.FormEvent<HTMLElement>, option?: IChoiceGroupOption) => {
    if (option) {
      setSelectedResourceType(option.key);
    }
  };

  const handleSharedResourcesToggle = (ev: React.MouseEvent<HTMLElement>, checked?: boolean) => {
    setShowSharedResources(!!checked);
  };

  const handleSearchChange = (ev?: React.ChangeEvent<HTMLInputElement>, newValue?: string) => {
    setSearchQuery(newValue || '');
  };

  const handleViewChange = (view: string) => {
    setActiveView(view);
  };

  // Prepare data for Cost per Project chart
  const getProjectCostChartData = () => {
    if (!costData || costData.projectCosts.length === 0) return null;

    return {
      labels: costData.projectCosts.map(p => p.project),
      datasets: [
        {
          label: 'Cost',
          data: costData.projectCosts.map(p => p.cost),
          backgroundColor: costData.projectCosts.map(p => {
            const percentUsed = (p.cost / p.budget) * 100;
            if (percentUsed >= 100) {
              return RED_400; // Red for over budget
            } else if (percentUsed >= 80) {
              return ORANGE_400; // Orange for approaching budget
            } else {
              return CYAN_400; // Cyan for under budget
            }
          }),
          borderColor: 'rgba(0, 0, 0, 0.1)',
          borderWidth: 1
        }
      ]
    };
  };

  // Prepare data for Cost by Service chart
  const getServiceCostChartData = () => {
    if (!costData || costData.serviceCosts.length === 0) return null;

    return {
      labels: costData.serviceCosts.map(s => `${s.service}${s.isShared ? ' (Shared)' : ''}`),
      datasets: [
        {
          label: 'Cost ($)',
          data: costData.serviceCosts.map(s => s.cost),
          backgroundColor: BLUE_PALETTE.slice(0, costData.serviceCosts.length),
          borderColor: 'rgba(255, 255, 255, 0.8)',
          borderWidth: 2,
          cutout: '70%',  // This makes it a donut chart
        }
      ]
    };
  };

  // Prepare data for Cost by Region chart
  const getRegionCostChartData = () => {
    if (!costData || costData.regionCosts.length === 0) return null;

    return {
      labels: costData.regionCosts.map(r => r.region),
      datasets: [
        {
          label: 'Cost ($)',
          data: costData.regionCosts.map(r => r.cost),
          backgroundColor: BLUE_PALETTE.slice(0, costData.regionCosts.length),
          borderColor: 'rgba(0, 0, 0, 0.1)',
          borderWidth: 1,
        }
      ]
    };
  };

  // Prepare data for Resource Costs chart
  const getResourceCostChartData = () => {
    if (!costData || costData.resourceCosts.length === 0) return null;

    // Filter resources by type if needed
    let filteredResources = costData.resourceCosts;
    if (selectedResourceType !== 'all' && selectedResourceType !== 'shared') {
      filteredResources = filteredResources.filter(r => r.resourceType === selectedResourceType);
    } else if (selectedResourceType === 'shared') {
      filteredResources = filteredResources.filter(r => r.isShared);
    }

    // Sort by cost (highest first)
    filteredResources = [...filteredResources].sort((a, b) => b.cost - a.cost);

    // Take top 10 for readability
    const topResources = filteredResources.slice(0, 10);

    return {
      labels: topResources.map(r => `${r.name}${r.isShared ? ' (Shared)' : ''}`),
      datasets: [
        {
          label: 'Cost ($)',
          data: topResources.map(r => r.cost),
          backgroundColor: topResources.map(r => r.isShared ? ORANGE_400 : CYAN_400),
          borderColor: 'rgba(0, 0, 0, 0.1)',
          borderWidth: 1,
        }
      ]
    };
  };

  // Prepare data for Container Costs chart
  const getContainerCostChartData = () => {
    if (!costData || costData.containerCosts.length === 0) return null;

    // Sort by cost (highest first)
    const sortedContainers = [...costData.containerCosts].sort((a, b) => b.cost - a.cost);

    // Take top 10 for readability
    const topContainers = sortedContainers.slice(0, 10);

    return {
      labels: topContainers.map(c => c.name),
      datasets: [
        {
          label: 'Cost ($)',
          data: topContainers.map(c => c.cost),
          backgroundColor: CYAN_400,
          borderColor: 'rgba(0, 0, 0, 0.1)',
          borderWidth: 1,
        }
      ]
    };
  };

  // Prepare data for Indexer Costs chart
  const getIndexerCostChartData = () => {
    if (!costData || costData.indexerCosts.length === 0) return null;

    // Sort by cost (highest first)
    const sortedIndexers = [...costData.indexerCosts].sort((a, b) => b.cost - a.cost);

    // Take top 10 for readability
    const topIndexers = sortedIndexers.slice(0, 10);

    return {
      labels: topIndexers.map(i => i.name),
      datasets: [
        {
          label: 'Cost ($)',
          data: topIndexers.map(i => i.cost),
          backgroundColor: CYAN_400,
          borderColor: 'rgba(0, 0, 0, 0.1)',
          borderWidth: 1,
        }
      ]
    };
  };

  // Chart options
  const barChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false, // Hide legend since we only have one dataset
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const projectIndex = context.dataIndex;
            const project = costData?.projectCosts[projectIndex];

            if (project) {
              const percentUsed = (project.cost / project.budget) * 100;
              return [
                `Project: ${project.project}`,
                `Cost: $${project.cost.toLocaleString()}`,
                `Budget: $${project.budget.toLocaleString()}`,
                `Budget Usage: ${percentUsed.toFixed(1)}%`
              ];
            }
            return '';
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        }
      },
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Cost ($)'
        },
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          callback: function(value: any) {
            return '$' + value.toLocaleString();
          }
        }
      }
    },
    barPercentage: 0.7,
    categoryPercentage: 0.8
  };

  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,  // Hide the default legend since we're creating our own
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const serviceIndex = context.dataIndex;
            const service = costData?.serviceCosts[serviceIndex];
            if (service) {
              return `${service.service}: $${service.cost.toFixed(2)}`;
            }
            return '';
          }
        }
      }
    },
    cutout: '70%'  // This makes it a donut chart
  };

  const horizontalBarChartOptions = {
    indexAxis: 'y' as const,
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const regionIndex = context.dataIndex;
            const region = costData?.regionCosts[regionIndex];
            if (region) {
              return `${region.region}: $${region.cost.toFixed(2)}`;
            }
            return '';
          }
        }
      }
    },
    scales: {
      x: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Cost ($)'
        }
      }
    }
  };

  // Define columns for the detailed views
  const containerColumns: IColumn[] = [
    {
      key: 'name',
      name: 'Container Name',
      fieldName: 'name',
      minWidth: 150,
      isResizable: true
    },
    {
      key: 'storageAccount',
      name: 'Storage Account',
      fieldName: 'storageAccount',
      minWidth: 150,
      isResizable: true
    },
    {
      key: 'project',
      name: 'Project',
      fieldName: 'project',
      minWidth: 120,
      isResizable: true
    },
    {
      key: 'region',
      name: 'Region',
      fieldName: 'region',
      minWidth: 120,
      isResizable: true
    },
    {
      key: 'cost',
      name: 'Cost ($)',
      fieldName: 'cost',
      minWidth: 100,
      isResizable: true,
      onRender: (item: ContainerCost) => <span>${item.cost.toFixed(2)}</span>
    }
  ];

  const indexerColumns: IColumn[] = [
    {
      key: 'name',
      name: 'Indexer Name',
      fieldName: 'name',
      minWidth: 150,
      isResizable: true
    },
    {
      key: 'searchService',
      name: 'Search Service',
      fieldName: 'searchService',
      minWidth: 150,
      isResizable: true
    },
    {
      key: 'project',
      name: 'Project',
      fieldName: 'project',
      minWidth: 120,
      isResizable: true
    },
    {
      key: 'region',
      name: 'Region',
      fieldName: 'region',
      minWidth: 120,
      isResizable: true
    },
    {
      key: 'cost',
      name: 'Cost ($)',
      fieldName: 'cost',
      minWidth: 100,
      isResizable: true,
      onRender: (item: IndexerCost) => <span>${item.cost.toFixed(2)}</span>
    }
  ];

  // Filter container and indexer data based on search query
  const filteredContainers = costData?.containerCosts.filter(c =>
    searchQuery ? c.name.toLowerCase().includes(searchQuery.toLowerCase()) : true
  ) || [];

  const filteredIndexers = costData?.indexerCosts.filter(i =>
    searchQuery ? i.name.toLowerCase().includes(searchQuery.toLowerCase()) : true
  ) || [];

  if (isUserLoading || !currentUser) {
    return <Spinner label="Loading user..." size={SpinnerSize.large} />;
  }

  return (
    <Stack tokens={stackTokens}>
      <Stack horizontal verticalAlign="center" tokens={{ childrenGap: 10 }}>
        <Text variant="xLarge" styles={{ root: { fontWeight: 600, marginBottom: 10 } }}>
          Cost Analytics
        </Text>
        {userRole === UserRole.REGIONAL_ADMIN && userRegionId && (
          <Text
            variant="mediumPlus"
            styles={{
              root: {
                backgroundColor: CYAN_400,
                color: 'white',
                padding: '2px 10px',
                borderRadius: 4,
                marginBottom: 10
              }
            }}
          >
            {costData?.regionCosts.find(r => r.regionId === userRegionId)?.region || 'Regional'} View
          </Text>
        )}
      </Stack>

      {error && (
        <MessageBar
          messageBarType={MessageBarType.error}
          isMultiline={false}
          dismissButtonAriaLabel="Close"
        >
          {error}
        </MessageBar>
      )}

      {/* Filters Section */}
      <Stack
        horizontal
        wrap
        horizontalAlign="space-between"
        verticalAlign="center"
        tokens={{ childrenGap: 10 }}
        styles={{ root: { backgroundColor: 'white', padding: 15, borderRadius: 4, marginBottom: 10 } }}
      >
        <Stack horizontal wrap tokens={{ childrenGap: 15 }}>
          {/* Time Range Filter */}
          <Dropdown
            label="Time Range"
            selectedKey={selectedTimeRange}
            options={timeRangeOptions}
            styles={{ dropdown: { width: 150 } }}
            onChange={handleTimeRangeChange}
          />

          {/* Region Filter - Only show for Super Admins */}
          {userRole === UserRole.SUPER_ADMIN && (
            <Dropdown
              label="Region"
              selectedKey={selectedRegionId || 'all'}
              options={regionOptions}
              styles={{ dropdown: { width: 150 } }}
              onChange={handleRegionChange}
            />
          )}

          {/* Project Filter */}
          <Dropdown
            label="Project"
            selectedKey={selectedProjectId || 'all'}
            options={projectOptions}
            styles={{ dropdown: { width: 150 } }}
            onChange={handleProjectChange}
          />

          {/* Region indicator for Regional Admins */}
          {userRole === UserRole.REGIONAL_ADMIN && userRegionId && (
            <Stack verticalAlign="end" styles={{ root: { paddingBottom: 5 } }}>
              <Label>Region: {costData?.regionCosts.find(r => r.regionId === userRegionId)?.region || 'Loading...'}</Label>
            </Stack>
          )}
        </Stack>

        <Stack horizontal tokens={{ childrenGap: 15 }}>
          {/* View Selector */}
          <ChoiceGroup
            selectedKey={activeView}
            options={[
              { key: 'overview', text: 'Overview' },
              { key: 'containers', text: 'Containers' },
              { key: 'indexers', text: 'Indexers' },
              { key: 'resources', text: 'Resources' }
            ]}
            onChange={(_, option) => option && handleViewChange(option.key)}
            styles={{ flexContainer: { display: 'flex' } }}
          />

          {/* Shared Resources Toggle */}
          <Toggle
            label="Include Shared Resources"
            checked={showSharedResources}
            onChange={handleSharedResourcesToggle}
          />
        </Stack>
      </Stack>

      {/* Resource Type Filter - Only show in resources view */}
      {activeView === 'resources' && (
        <Stack
          horizontal
          wrap
          tokens={{ childrenGap: 10 }}
          styles={{ root: { backgroundColor: 'white', padding: 15, borderRadius: 4, marginBottom: 10 } }}
        >
          <ChoiceGroup
            label="Resource Type"
            selectedKey={selectedResourceType}
            options={resourceTypeOptions}
            onChange={handleResourceTypeChange}
            styles={{ flexContainer: { display: 'flex' } }}
          />
        </Stack>
      )}

      {/* Search Box - Only show in detailed views */}
      {(activeView === 'containers' || activeView === 'indexers') && (
        <SearchBox
          placeholder={`Search ${activeView}...`}
          onChange={handleSearchChange}
          styles={{ root: { width: '100%', marginBottom: 10 } }}
        />
      )}

      {isLoading ? (
        <Stack horizontalAlign="center" verticalAlign="center" styles={{ root: { padding: 20 } }}>
          <Spinner size={SpinnerSize.large} label="Loading cost data..." />
        </Stack>
      ) : (
        activeView === 'overview' ? (
          <Stack tokens={stackTokens}>
            {/* Cost per Project Chart */}
            <Stack
              styles={{
                root: {
                  backgroundColor: 'white',
                  padding: 20,
                  borderRadius: 4,
                  boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)'
                }
              }}
              tokens={chartContainerTokens}
            >
              <Text variant="large" styles={{ root: { fontWeight: 600 } }}>Cost per Project</Text>
              <div style={{ height: 350 }}>
                {getProjectCostChartData() ? (
                  <Bar
                    data={getProjectCostChartData()!}
                    options={{
                      ...barChartOptions,
                      onClick: (event: ChartEvent, elements: any[]) => {
                        if (elements && elements.length > 0) {
                          const index = elements[0].index;
                          const projectId = costData?.projectCosts[index].projectId;
                          if (projectId) {
                            setSelectedProjectId(projectId);
                          }
                        }
                      }
                    }}
                    plugins={[createBudgetThresholdPlugin(costData)]}
                  />
                ) : (
                  <Stack horizontalAlign="center" verticalAlign="center" style={{ height: '100%' }}>
                    <Text>No project cost data available for the selected filters.</Text>
                  </Stack>
                )}
              </div>

              {/* Legend for the chart */}
              <div style={{
                display: 'flex',
                justifyContent: 'center',
                marginTop: '15px',
                gap: '20px'
              }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div style={{
                    width: '12px',
                    height: '12px',
                    backgroundColor: CYAN_400,
                    marginRight: '5px',
                    borderRadius: '2px'
                  }}></div>
                  <Text styles={{ root: { fontSize: '12px' } }}>Under Budget</Text>
                </div>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div style={{
                    width: '12px',
                    height: '12px',
                    backgroundColor: ORANGE_400,
                    marginRight: '5px',
                    borderRadius: '2px'
                  }}></div>
                  <Text styles={{ root: { fontSize: '12px' } }}>Near Budget</Text>
                </div>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div style={{
                    width: '12px',
                    height: '12px',
                    backgroundColor: RED_400,
                    marginRight: '5px',
                    borderRadius: '2px'
                  }}></div>
                  <Text styles={{ root: { fontSize: '12px' } }}>Over Budget</Text>
                </div>
              </div>
            </Stack>

            {/* Cost by Service and Region Charts */}
            <Stack horizontal tokens={{ childrenGap: 20 }}>
              {/* Cost by Service Chart */}
              <Stack.Item grow={1}>
                <Stack
                  styles={{
                    root: {
                      backgroundColor: 'white',
                      padding: 20,
                      borderRadius: 4,
                      boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)'
                    }
                  }}
                  tokens={chartContainerTokens}
                >
                  <Text variant="large" styles={{ root: { fontWeight: 600 } }}>Cost by Service</Text>
                  <div style={{ height: 300, position: 'relative', display: 'flex' }}>
                    {getServiceCostChartData() ? (
                      <>
                        <div style={{ flex: '0 0 70%', position: 'relative' }}>
                          <Doughnut
                            data={getServiceCostChartData()!}
                            options={pieChartOptions}
                          />
                          <div style={{
                            position: 'absolute',
                            top: '50%',
                            left: '50%',
                            transform: 'translate(-50%, -50%)',
                            textAlign: 'center',
                            pointerEvents: 'none',
                            width: '100%'
                          }}>
                            <Text variant="small" styles={{ root: { color: '#666', fontSize: '12px' } }}>Total Cost</Text>
                            <Text variant="xLarge" styles={{ root: { fontWeight: 'bold', color: '#0EA5E9', lineHeight: 1.2 } }}>
                              ${costData?.totalCost.toFixed(2)}
                            </Text>
                          </div>
                        </div>

                        {/* Legend on the right side */}
                        <div style={{ flex: '0 0 30%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                          {costData?.serviceCosts.map((service, index) => {
                            return (
                              <div key={index} style={{
                                display: 'flex',
                                alignItems: 'center',
                                marginBottom: 10
                              }}>
                                <div style={{
                                  width: 12,
                                  height: 12,
                                  backgroundColor: BLUE_PALETTE[index % BLUE_PALETTE.length],
                                  borderRadius: '50%',
                                  marginRight: 8
                                }} />
                                <Text styles={{ root: { fontSize: '12px' } }}>
                                  {service.service}{service.isShared ? ' (Shared)' : ''}: ${service.cost.toFixed(2)}
                                </Text>
                              </div>
                            );
                          })}
                        </div>
                      </>
                    ) : (
                      <Stack horizontalAlign="center" verticalAlign="center" style={{ width: '100%' }}>
                        <Text>No service cost data available for the selected filters.</Text>
                      </Stack>
                    )}
                  </div>
                </Stack>
              </Stack.Item>

              {/* Cost by Region Chart */}
              <Stack.Item grow={1}>
                <Stack
                  styles={{
                    root: {
                      backgroundColor: 'white',
                      padding: 20,
                      borderRadius: 4,
                      boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)'
                    }
                  }}
                  tokens={chartContainerTokens}
                >
                  <Text variant="large" styles={{ root: { fontWeight: 600 } }}>Cost by Region</Text>
                  <div style={{ height: 300 }}>
                    {getRegionCostChartData() ? (
                      <Bar
                        data={getRegionCostChartData()!}
                        options={horizontalBarChartOptions}
                      />
                    ) : (
                      <Stack horizontalAlign="center" verticalAlign="center" style={{ height: '100%' }}>
                        <Text>No region cost data available for the selected filters.</Text>
                      </Stack>
                    )}
                  </div>
                </Stack>
              </Stack.Item>
            </Stack>
          </Stack>
        ) : activeView === 'resources' ? (
          <Stack
            styles={{
              root: {
                backgroundColor: 'white',
                padding: 20,
                borderRadius: 4,
                boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)'
              }
            }}
            tokens={chartContainerTokens}
          >
            <Text variant="large" styles={{ root: { fontWeight: 600 } }}>
              Resource Costs {selectedResourceType !== 'all' && `- ${selectedResourceType.charAt(0).toUpperCase() + selectedResourceType.slice(1)}`}
            </Text>
            <div style={{ height: 400 }}>
              {getResourceCostChartData() ? (
                <Bar
                  data={getResourceCostChartData()!}
                  options={{
                    ...barChartOptions,
                    indexAxis: 'y' as const,
                  }}
                />
              ) : (
                <Stack horizontalAlign="center" verticalAlign="center" style={{ height: '100%' }}>
                  <Text>No resource cost data available for the selected filters.</Text>
                </Stack>
              )}
            </div>

            {/* Legend for shared resources */}
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              marginTop: '15px',
              gap: '20px'
            }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '12px',
                  height: '12px',
                  backgroundColor: CYAN_400,
                  marginRight: '5px',
                  borderRadius: '2px'
                }}></div>
                <Text styles={{ root: { fontSize: '12px' } }}>Dedicated Resources</Text>
              </div>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '12px',
                  height: '12px',
                  backgroundColor: ORANGE_400,
                  marginRight: '5px',
                  borderRadius: '2px'
                }}></div>
                <Text styles={{ root: { fontSize: '12px' } }}>Shared Resources</Text>
              </div>
            </div>
          </Stack>
        ) : activeView === 'containers' ? (
          <Stack
            styles={{
              root: {
                backgroundColor: 'white',
                padding: 20,
                borderRadius: 4,
                boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)'
              }
            }}
          >
            <Text variant="large" styles={{ root: { fontWeight: 600, marginBottom: 10 } }}>Container Costs</Text>

            {filteredContainers.length > 0 ? (
              <>
                <DetailsList
                  items={filteredContainers}
                  columns={containerColumns}
                  selectionMode={SelectionMode.none}
                  layoutMode={DetailsListLayoutMode.justified}
                />
                <Text variant="small" styles={{ root: { marginTop: 10, color: '#666' } }}>
                  Total: ${filteredContainers.reduce((sum, c) => sum + c.cost, 0).toFixed(2)}
                </Text>
              </>
            ) : (
              <Stack horizontalAlign="center" verticalAlign="center" style={{ height: 200 }}>
                <Text>No container cost data available for the selected filters.</Text>
              </Stack>
            )}
          </Stack>
        ) : activeView === 'indexers' ? (
          <Stack
            styles={{
              root: {
                backgroundColor: 'white',
                padding: 20,
                borderRadius: 4,
                boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)'
              }
            }}
          >
            <Text variant="large" styles={{ root: { fontWeight: 600, marginBottom: 10 } }}>Indexer Costs</Text>

            {filteredIndexers.length > 0 ? (
              <>
                <DetailsList
                  items={filteredIndexers}
                  columns={indexerColumns}
                  selectionMode={SelectionMode.none}
                  layoutMode={DetailsListLayoutMode.justified}
                />
                <Text variant="small" styles={{ root: { marginTop: 10, color: '#666' } }}>
                  Total: ${filteredIndexers.reduce((sum, i) => sum + i.cost, 0).toFixed(2)}
                </Text>
              </>
            ) : (
              <Stack horizontalAlign="center" verticalAlign="center" style={{ height: 200 }}>
                <Text>No indexer cost data available for the selected filters.</Text>
              </Stack>
            )}
          </Stack>
        ) : null
      )}
    </Stack>
  );
};

export default CostAnalytics;
