import React, { useState, useEffect } from 'react';
import {
  DetailsList,
  DetailsListLayoutMode,
  SelectionMode,
  IColumn,
  PrimaryButton,
  CommandBar,
  ICommandBarItemProps,
  Stack,
  TextField,
  Dropdown,
  IDropdownOption,
  Dialog,
  DialogType,
  DialogFooter,
  DefaultButton,
  MessageBar,
  MessageBarType,
  Text,
  SearchBox,
  IconButton,
  TooltipHost,
  DirectionalHint,
  Label,
  Checkbox,
  IStackTokens,
  Spinner,
  SpinnerSize
} from '@fluentui/react';
import { useUser } from '../../state/UserProvider';
import { UserRole, getRolePermissions, Team, UserProfile } from '../../models/roles';
// import rbacService, { RegionData } from '../../services/rbacService'; // Removed rbacService
import userContextService from '../../services/userContextService'; // Added userContextService
import { RegionData } from '../../services/rbacService'; // Keep type definition
import { PermissionBasedAccess } from '../../components/RoleBasedAccess';

// Stack tokens
const stackTokens: IStackTokens = { childrenGap: 15 };

// Mock data for teams
const mockTeams: Team[] = [
  {
    id: '1',
    name: 'Marketing Team',
    description: 'Team responsible for marketing projects',
    region: 'North America',
    projects: ['1', '3'],
    members: ['4', '5'],
    createdBy: '2',
    createdAt: '2023-05-15'
  },
  {
    id: '2',
    name: 'Sales Team',
    description: 'Team responsible for sales projects',
    region: 'Europe',
    projects: ['2'],
    members: ['5', '6'],
    createdBy: '3',
    createdAt: '2023-06-22'
  },
  {
    id: '3',
    name: 'Product Team',
    description: 'Team responsible for product development projects',
    region: 'North America',
    projects: ['4'],
    members: ['4'],
    createdBy: '2',
    createdAt: '2023-07-10'
  }
];

// Mock data for users (simplified version of what's in UserManagement)
const mockUsers: UserProfile[] = [
  {
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>',
    role: UserRole.SUPER_ADMIN,
  },
  {
    id: '2',
    name: 'Jane Smith',
    email: '<EMAIL>',
    role: UserRole.REGIONAL_ADMIN,
    region: 'North America'
  },
  {
    id: '3',
    name: 'Bob Johnson',
    email: '<EMAIL>',
    role: UserRole.REGIONAL_ADMIN,
    region: 'Europe'
  },
  {
    id: '4',
    name: 'Alice Brown',
    email: '<EMAIL>',
    role: UserRole.REGULAR_USER,
    region: 'North America'
  },
  {
    id: '5',
    name: 'Charlie Wilson',
    email: '<EMAIL>',
    role: UserRole.REGULAR_USER,
    region: 'Europe'
  },
  {
    id: '6',
    name: 'Diana Miller',
    email: '<EMAIL>',
    role: UserRole.REGULAR_USER,
    region: 'Asia Pacific'
  }
];

// Mock data for projects (simplified)
const mockProjects = [
  { id: '1', name: 'Marketing Analytics', region: 'North America', teams: ['1', '3'] },
  { id: '2', name: 'Customer Support AI', region: 'Europe', teams: ['2'] },
  { id: '3', name: 'Sales Forecasting', region: 'North America', teams: ['1'] },
  { id: '4', name: 'Product Recommendation Engine', region: 'Asia Pacific', teams: ['3'] },
  { id: '5', name: 'HR Document Analysis', region: 'Europe', teams: ['2'] }
];

// Region options will be loaded from the API

const TeamManagement: React.FC = () => {
  const { currentUser } = useUser();
  const [teams, setTeams] = useState<Team[]>([]);
  const [filteredTeams, setFilteredTeams] = useState<Team[]>([]);
  const [searchText, setSearchText] = useState('');
  const [isAddTeamDialogOpen, setIsAddTeamDialogOpen] = useState(false);
  const [isEditTeamDialogOpen, setIsEditTeamDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isAssignProjectsDialogOpen, setIsAssignProjectsDialogOpen] = useState(false);
  const [isAssignMembersDialogOpen, setIsAssignMembersDialogOpen] = useState(false);
  const [selectedTeam, setSelectedTeam] = useState<Team | null>(null);
  const [newTeam, setNewTeam] = useState<Partial<Team>>({
    name: '',
    description: '',
    region: currentUser?.region || '',
    projects: [],
    members: []
  });
  const [availableProjects, setAvailableProjects] = useState<any[]>([]);
  const [availableUsers, setAvailableUsers] = useState<any[]>([]);
  const [selectedProjects, setSelectedProjects] = useState<string[]>([]);
  const [selectedMembers, setSelectedMembers] = useState<string[]>([]);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [regionOptions, setRegionOptions] = useState<IDropdownOption[]>([]);

  // Get permissions based on user role
  const permissions = currentUser ? getRolePermissions(currentUser.role) : getRolePermissions(UserRole.REGULAR_USER);

  // Columns for the DetailsList
  const columns: IColumn[] = [
    {
      key: 'name',
      name: 'Team Name',
      fieldName: 'name',
      minWidth: 150,
      maxWidth: 200,
      isResizable: true
    },
    {
      key: 'description',
      name: 'Description',
      fieldName: 'description',
      minWidth: 200,
      maxWidth: 300,
      isResizable: true
    },
    {
      key: 'region',
      name: 'Region',
      fieldName: 'region',
      minWidth: 100,
      maxWidth: 150,
      isResizable: true,
      onRender: (item: Team) => {
        // Find the region name from the region ID
        const regionOption = regionOptions.find(r => r.key === item.region);
        return <span>{regionOption ? regionOption.text : item.region}</span>;
      }
    },
    {
      key: 'projectCount',
      name: 'Projects',
      minWidth: 150,
      maxWidth: 200,
      isResizable: true,
      onRender: (item: Team) => (
        <div>
          <span>{item.projects.length}</span>
          {item.projects.length > 0 && (
            <TooltipHost
              content={
                <div>
                  <Text variant="medium" styles={{ root: { fontWeight: 600, marginBottom: 5 } }}>
                    Assigned Projects:
                  </Text>
                  <ul style={{ margin: 0, paddingLeft: 20 }}>
                    {item.projects.map(projectId => (
                      <li key={projectId}>{getProjectName(projectId)}</li>
                    ))}
                  </ul>
                </div>
              }
              styles={{ root: { display: 'inline-block', marginLeft: 5 } }}
              directionalHint={DirectionalHint.rightCenter}
            >
              <IconButton
                iconProps={{ iconName: 'Info' }}
                title="View Projects"
                ariaLabel="View Projects"
                styles={{ root: { height: 20, width: 20 } }}
              />
            </TooltipHost>
          )}
        </div>
      )
    },
    {
      key: 'memberCount',
      name: 'Members',
      minWidth: 100,
      maxWidth: 150,
      isResizable: true,
      onRender: (item: Team) => (
        <div>
          <span>{item.members.length}</span>
          {item.members.length > 0 && (
            <TooltipHost
              content={
                <div>
                  <Text variant="medium" styles={{ root: { fontWeight: 600, marginBottom: 5 } }}>
                    Team Members:
                  </Text>
                  <ul style={{ margin: 0, paddingLeft: 20 }}>
                    {item.members.map(userId => (
                      <li key={userId}>{getUserName(userId)}</li>
                    ))}
                  </ul>
                </div>
              }
              styles={{ root: { display: 'inline-block', marginLeft: 5 } }}
              directionalHint={DirectionalHint.rightCenter}
            >
              <IconButton
                iconProps={{ iconName: 'Info' }}
                title="View Members"
                ariaLabel="View Members"
                styles={{ root: { height: 20, width: 20 } }}
              />
            </TooltipHost>
          )}
        </div>
      )
    },
    {
      key: 'actions',
      name: 'Actions',
      minWidth: 150,
      maxWidth: 150,
      isResizable: false,
      onRender: (item: Team) => (
        <Stack horizontal tokens={{ childrenGap: 8 }}>
          <PermissionBasedAccess requiredPermission="canCreateTeams">
            <IconButton
              iconProps={{ iconName: 'Edit' }}
              title="Edit Team"
              ariaLabel="Edit Team"
              onClick={() => handleEditTeam(item)}
              disabled={currentUser?.role === UserRole.REGIONAL_ADMIN && item.region !== currentUser.region}
            />
          </PermissionBasedAccess>
          <PermissionBasedAccess requiredPermission="canAssignProjects">
            <IconButton
              iconProps={{ iconName: 'ProjectCollection' }}
              title="Assign Projects"
              ariaLabel="Assign Projects"
              onClick={() => handleAssignProjects(item)}
              disabled={currentUser?.role === UserRole.REGIONAL_ADMIN && item.region !== currentUser.region}
            />
          </PermissionBasedAccess>
          <PermissionBasedAccess requiredPermission="canAssignTeams">
            <IconButton
              iconProps={{ iconName: 'People' }}
              title="Assign Members"
              ariaLabel="Assign Members"
              onClick={() => handleAssignMembers(item)}
              disabled={currentUser?.role === UserRole.REGIONAL_ADMIN && item.region !== currentUser.region}
            />
          </PermissionBasedAccess>
          <PermissionBasedAccess requiredPermission="canCreateTeams">
            <IconButton
              iconProps={{ iconName: 'Delete' }}
              title="Delete Team"
              ariaLabel="Delete Team"
              onClick={() => handleDeleteClick(item)}
              disabled={currentUser?.role === UserRole.REGIONAL_ADMIN && item.region !== currentUser.region}
            />
          </PermissionBasedAccess>
        </Stack>
      )
    }
  ];

  // Command bar items
  const commandItems: ICommandBarItemProps[] = [
    {
      key: 'addTeam',
      text: 'Create Team',
      iconProps: { iconName: 'AddGroup' },
      onClick: () => setIsAddTeamDialogOpen(true),
      disabled: !permissions.canCreateTeams
    },
    {
      key: 'refresh',
      text: 'Refresh',
      iconProps: { iconName: 'Refresh' },
      onClick: () => { loadTeams(); return; }
    }
  ];

  // Load teams on component mount with optimized loading
  useEffect(() => {
    const initializeData = async () => {
      setIsLoading(true);

      // Load data in parallel where possible
      await Promise.all([
        loadRegions(),
        loadAvailableProjects(),
        loadAvailableUsers()
      ]);

      // Load teams last since it depends on projects data
      await loadTeams();

      setIsLoading(false);
    };

    initializeData();
  }, []);

  // Filter teams when search text changes
  useEffect(() => {
    if (searchText) {
      const filtered = teams.filter(team =>
        team.name.toLowerCase().includes(searchText.toLowerCase()) ||
        (team.description && team.description.toLowerCase().includes(searchText.toLowerCase())) ||
        team.region.toLowerCase().includes(searchText.toLowerCase())
      );
      setFilteredTeams(filtered);
    } else {
      setFilteredTeams(teams);
    }
  }, [searchText, teams]);

  // Load regions from API (userContext or direct fetch)
  const loadRegions = async () => {
    try {
      const userContext = await userContextService.fetchUserContext();
      if (userContext && userContext.accessibleResources && userContext.accessibleResources.regions) {
        const regionOptionsFromCtx = userContext.accessibleResources.regions.map((region: { id: string, name: string }) => ({
          key: region.id,
          text: region.name
        }));
        setRegionOptions(regionOptionsFromCtx);
        return;
      }
      // Fallback to direct fetch if not in userContext
      // Import the getAuthHeaders function from authHeaderService
      const { getAuthHeaders } = await import('../../services/authHeaderService');

      // Get authentication headers
      const authHeaders = await getAuthHeaders();
      console.log('Fetching regions with auth headers');

      const response = await fetch('/api/rbac/regions', {
        headers: authHeaders
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }
      const data: RegionData[] = await response.json();
      const regionOptionsFromApi = data.map((region) => ({
        key: region.id,
        text: region.name
      }));
      setRegionOptions(regionOptionsFromApi);
    } catch (error) {
      console.error('Error fetching regions:', error);
      setErrorMessage('Failed to load regions. Please try again later.');
    }
  };

  // Load teams from API with optimized data fetching
  const loadTeams = async () => {
    try {
      setIsLoading(true);

      // Import the getAuthHeaders function from authHeaderService
      const { getAuthHeaders } = await import('../../services/authHeaderService');

      // Get authentication headers
      const authHeaders = await getAuthHeaders();
      console.log('Fetching teams with auth headers');

      // Fetch all teams in a single request using fetch with authentication
      const teamsResponse = await fetch('/api/rbac/teams', {
        headers: authHeaders
      });

      if (!teamsResponse.ok) {
        const errorData = await teamsResponse.json();
        console.error('Error loading teams:', errorData.error || teamsResponse.statusText);
        setErrorMessage(`Error loading teams: ${errorData.error || 'Unknown error'}`);
        setTeams(mockTeams);
        setFilteredTeams(mockTeams);
        return;
      }
      const teamsData = await teamsResponse.json();

      // Fetch all projects in a single request using fetch with authentication
      const projectsApiResponse = await fetch('/api/projects', {
        headers: authHeaders
      });

      const allProjects = projectsApiResponse.ok ? await projectsApiResponse.json() : [];

      // Batch fetch all project teams in a single loop using fetch with authentication
      const projectTeamsMap = new Map();
      for (const project of allProjects) {
        try {
            const projectTeamsResponse = await fetch(`/api/rbac/projects/${project.id}/teams`, {
              headers: authHeaders
            });
            projectTeamsMap.set(project.id, projectTeamsResponse.ok ? await projectTeamsResponse.json() : []);
        } catch (e) {
            console.warn(`Failed to fetch teams for project ${project.id}`, e);
            projectTeamsMap.set(project.id, []);
        }
      }

      // Process all teams with the pre-fetched data
      const apiTeams: Team[] = [];
      for (const teamData of teamsData) {
        // Get team members using fetch with authentication
        let memberIds: string[] = [];
        try {
            const membersResponse = await fetch(`/api/rbac/teams/${teamData.id}/members`, {
              headers: authHeaders
            });
            if (membersResponse.ok) {
                const membersData = await membersResponse.json();
                memberIds = membersData.map((member: { userId: string }) => member.userId);
            }
        } catch (e) {
            console.warn(`Failed to fetch members for team ${teamData.id}`, e);
        }

        // Find projects this team is assigned to using our pre-fetched map
        const teamProjects: string[] = [];
        projectTeamsMap.forEach((projectTeams, projectId) => {
          if (projectTeams.some((pt: any) => pt.teamId === teamData.id)) {
            teamProjects.push(projectId);
          }
        });

        apiTeams.push({
          id: teamData.id,
          name: teamData.name,
          description: teamData.description || '',
          region: teamData.region,
          projects: teamProjects,
          members: memberIds,
          createdBy: teamData.created_by,
          createdAt: teamData.created_at
        } as Team);
      }

      // Filter teams based on current user's role and region
      let filteredApiTeams = apiTeams;

      if (currentUser?.role === UserRole.REGIONAL_ADMIN && currentUser?.region) {
        // Regional admins should only see teams in their region
        filteredApiTeams = apiTeams.filter(team => team.region === currentUser.region);
      } else if (currentUser?.role === UserRole.REGULAR_USER) {
        // Regular users should only see teams they are members of
        // This would typically be handled by the backend, but we're adding an extra check here
        filteredApiTeams = apiTeams.filter(team => {
          // Check if the user is a member of this team
          // This is a simplified check - in a real app, you would check team memberships
          return team.members?.includes(currentUser.id);
        });
      }

      setTeams(filteredApiTeams);
      setFilteredTeams(filteredApiTeams);
    } catch (error) {
      console.error('Error fetching teams:', error);
      setErrorMessage('Failed to load teams. Please try again later.');
      // Fallback to mock data for development
      setTeams(mockTeams);
      setFilteredTeams(mockTeams);
    } finally {
      setIsLoading(false);
    }
  };

  // Load available projects from API with caching
  const loadAvailableProjects = async () => {
    try {
      // Import the getAuthHeaders function from authHeaderService
      const { getAuthHeaders } = await import('../../services/authHeaderService');

      // Get authentication headers
      const authHeaders = await getAuthHeaders();
      console.log('Fetching available projects with auth headers');

      const response = await fetch('/api/projects', {
        headers: authHeaders
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      setAvailableProjects(data);
    } catch (error) {
      console.error('Error fetching projects:', error);
      setErrorMessage('Failed to load available projects. Using mock data.');
      setAvailableProjects(mockProjects);
    }
  };

  // Load available users from API with caching
  const loadAvailableUsers = async () => {
    try {
      // Import the getAuthHeaders function from authHeaderService
      const { getAuthHeaders } = await import('../../services/authHeaderService');

      // Get authentication headers
      const authHeaders = await getAuthHeaders();
      console.log('Fetching available users with auth headers');

      const response = await fetch('/api/rbac/users', {
        headers: authHeaders
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      // Filter to only include regular users
      const filteredUsers = data.filter((user: UserProfile) =>
        user.role === UserRole.REGULAR_USER
      );
      setAvailableUsers(filteredUsers);
    } catch (error) {
      console.error('Error fetching users:', error);
      setErrorMessage('Failed to load available users. Using mock data.');
      setAvailableUsers(mockUsers);
    }
  };

  // Handle adding a new team
  const handleAddTeam = async () => {
    // Validate form
    if (!newTeam.name || !newTeam.region) {
      setErrorMessage('Team name and region are required.');
      return;
    }

    try {
      // Prepare team data for API
      const teamData = {
        name: newTeam.name,
        description: newTeam.description || '',
        region: newTeam.region || currentUser?.region || ''
      };

      // Import the getAuthHeaders function from authHeaderService
      const { getAuthHeaders } = await import('../../services/authHeaderService');

      // Get authentication headers
      const authHeaders = await getAuthHeaders();
      console.log('Creating team with auth headers');

      // Call API to create team using fetch with authentication
      const response = await fetch('/api/rbac/teams', {
        method: 'POST',
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(teamData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        setErrorMessage(`Failed to create team: ${errorData.error || response.statusText}`);
        return;
      }
      // const responseData = await response.json(); // if needed

      // Refresh teams list
      await loadTeams();

      // Reset form
      setIsAddTeamDialogOpen(false);
      setNewTeam({
        name: '',
        description: '',
        region: currentUser?.region || '',
        projects: [],
        members: []
      });

      setSuccessMessage(`Team "${teamData.name}" created successfully.`);
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (error) {
      console.error('Error creating team:', error);
      setErrorMessage('Failed to create team. Please try again later.');
    }
  };

  // Handle editing a team
  const handleEditTeam = (team: Team) => {
    setSelectedTeam(team);
    setNewTeam({
      name: team.name,
      description: team.description,
      region: team.region
    });
    setIsEditTeamDialogOpen(true);
  };

  // Handle saving edited team
  const handleSaveEdit = async () => {
    if (!selectedTeam) return;

    // Validate form
    if (!newTeam.name || !newTeam.region) {
      setErrorMessage('Team name and region are required.');
      return;
    }

    try {
      // Prepare team data for API
      const teamData = {
        name: newTeam.name || selectedTeam.name,
        description: newTeam.description || selectedTeam.description,
        region: newTeam.region || selectedTeam.region
      };

      // Import the getAuthHeaders function from authHeaderService
      const { getAuthHeaders } = await import('../../services/authHeaderService');

      // Get authentication headers
      const authHeaders = await getAuthHeaders();
      console.log('Updating team with auth headers');

      // Call API to update team using fetch with authentication
      const response = await fetch(`/api/rbac/teams/${selectedTeam.id}`, {
        method: 'PUT',
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(teamData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        setErrorMessage(`Failed to update team: ${errorData.error || response.statusText}`);
        return;
      }
      // const responseData = await response.json(); // if needed

      // Refresh teams list
      await loadTeams();

      // Reset form
      setIsEditTeamDialogOpen(false);
      setSelectedTeam(null);
      setNewTeam({
        name: '',
        description: '',
        region: currentUser?.region || '',
        projects: [],
        members: []
      });

      setSuccessMessage(`Team "${teamData.name}" updated successfully.`);
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (error) {
      console.error('Error updating team:', error);
      setErrorMessage('Failed to update team. Please try again later.');
    }
  };

  // Handle delete click
  const handleDeleteClick = (team: Team) => {
    setSelectedTeam(team);
    setIsDeleteDialogOpen(true);
  };

  // Handle deleting a team
  const handleDeleteTeam = async () => {
    if (!selectedTeam) return;

    try {
      // Import the getAuthHeaders function from authHeaderService
      const { getAuthHeaders } = await import('../../services/authHeaderService');

      // Get authentication headers
      const authHeaders = await getAuthHeaders();
      console.log('Deleting team with auth headers');

      // Call API to delete team using fetch with authentication
      const response = await fetch(`/api/rbac/teams/${selectedTeam.id}`, {
        method: 'DELETE',
        headers: authHeaders
      });

      if (!response.ok) {
        let errorMsg = `Failed to delete team. Status: ${response.status}`;
        try {
            const errorData = await response.json();
            errorMsg = errorData.error || errorData.message || errorMsg;
        } catch(e) { /* ignore */ }
        setErrorMessage(errorMsg);
        return;
      }

      // Refresh teams list
      await loadTeams();

      setIsDeleteDialogOpen(false);
      setSelectedTeam(null);
      setSuccessMessage(`Team "${selectedTeam.name}" deleted successfully.`);
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (error) {
      console.error('Error deleting team:', error);
      setErrorMessage('Failed to delete team. Please try again later.');
    }
  };

  // Handle assigning projects to a team
  const handleAssignProjects = (team: Team) => {
    setSelectedTeam(team);
    setSelectedProjects([...team.projects]);
    setIsAssignProjectsDialogOpen(true);
  };

  // Handle saving assigned projects
  const handleSaveProjects = async () => {
    if (!selectedTeam) return;

    try {
      // Get current project assignments
      const currentProjects = selectedTeam.projects || [];

      // Projects to add
      const projectsToAdd = selectedProjects.filter(p => !currentProjects.includes(p));

      // Projects to remove
      const projectsToRemove = currentProjects.filter(p => !selectedProjects.includes(p));

      // Import the getAuthHeaders function from authHeaderService
      const { getAuthHeaders } = await import('../../services/authHeaderService');

      // Get authentication headers
      const authHeaders = await getAuthHeaders();
      console.log('Updating project assignments with auth headers');

      // Add new project assignments using fetch with authentication
      for (const projectId of projectsToAdd) {
        const assignResponse = await fetch(`/api/rbac/projects/${projectId}/teams`, {
          method: 'POST',
          headers: {
            ...authHeaders,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ teamId: selectedTeam.id }),
        });
        if (!assignResponse.ok) throw new Error(`Failed to assign team to project ${projectId}`);
      }

      // Remove old project assignments using fetch with authentication
      for (const projectId of projectsToRemove) {
        const removeResponse = await fetch(`/api/rbac/projects/${projectId}/teams/${selectedTeam.id}`, {
          method: 'DELETE',
          headers: authHeaders
        });
        if (!removeResponse.ok) throw new Error(`Failed to remove team from project ${projectId}`);
      }

      // Refresh teams list
      await loadTeams();

      setIsAssignProjectsDialogOpen(false);
      setSelectedTeam(null);
      setSelectedProjects([]);
      setSuccessMessage(`Projects assigned to team successfully.`);
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (error) {
      console.error('Error assigning projects to team:', error);
      setErrorMessage('Failed to assign projects. Please try again later.');
    }
  };

  // Handle assigning members to a team
  const handleAssignMembers = (team: Team) => {
    setSelectedTeam(team);
    setSelectedMembers([...team.members]);
    setIsAssignMembersDialogOpen(true);
  };

  // Handle saving assigned members
  const handleSaveMembers = async () => {
    if (!selectedTeam) return;

    try {
      // Get current team members
      const currentMembers = selectedTeam.members || [];

      // Members to add
      const membersToAdd = selectedMembers.filter(m => !currentMembers.includes(m));

      // Members to remove
      const membersToRemove = currentMembers.filter(m => !selectedMembers.includes(m));

      // Import the getAuthHeaders function from authHeaderService
      const { getAuthHeaders } = await import('../../services/authHeaderService');

      // Get authentication headers
      const authHeaders = await getAuthHeaders();
      console.log('Updating team members with auth headers');

      // Add new team members using fetch with authentication
      for (const userId of membersToAdd) {
        const addResponse = await fetch(`/api/rbac/teams/${selectedTeam.id}/members`, {
          method: 'POST',
          headers: {
            ...authHeaders,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ userId, role: 'member' }),
        });
        if (!addResponse.ok) throw new Error(`Failed to add member ${userId} to team`);
      }

      // Remove old team members using fetch with authentication
      for (const userId of membersToRemove) {
        const removeResponse = await fetch(`/api/rbac/teams/${selectedTeam.id}/members/${userId}`, {
          method: 'DELETE',
          headers: authHeaders
        });
        if (!removeResponse.ok) throw new Error(`Failed to remove member ${userId} from team`);
      }

      // Refresh teams list
      await loadTeams();

      setIsAssignMembersDialogOpen(false);
      setSelectedTeam(null);
      setSelectedMembers([]);
      setSuccessMessage(`Members assigned to team successfully.`);
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (error) {
      console.error('Error assigning members to team:', error);
      setErrorMessage('Failed to assign members. Please try again later.');
    }
  };

  // Handle project checkbox change
  const onProjectCheckboxChange = (projectId: string, isChecked?: boolean) => {
    if (isChecked) {
      setSelectedProjects([...selectedProjects, projectId]);
    } else {
      setSelectedProjects(selectedProjects.filter(id => id !== projectId));
    }
  };

  // Handle member checkbox change
  const onMemberCheckboxChange = (userId: string, isChecked?: boolean) => {
    if (isChecked) {
      setSelectedMembers([...selectedMembers, userId]);
    } else {
      setSelectedMembers(selectedMembers.filter(id => id !== userId));
    }
  };

  // Get project name by ID
  const getProjectName = (projectId: string) => {
    const project = availableProjects.find(p => p.id === projectId);
    return project ? project.name : 'Unknown Project';
  };

  // Get user name by ID
  const getUserName = (userId: string) => {
    const user = availableUsers.find(u => u.id === userId);
    return user ? user.name : 'Unknown User';
  };

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {errorMessage && (
        <MessageBar
          messageBarType={MessageBarType.error}
          isMultiline={false}
          onDismiss={() => setErrorMessage(null)}
          dismissButtonAriaLabel="Close"
          styles={{ root: { marginBottom: 10 } }}
        >
          {errorMessage}
        </MessageBar>
      )}

      {successMessage && (
        <MessageBar
          messageBarType={MessageBarType.success}
          isMultiline={false}
          onDismiss={() => setSuccessMessage(null)}
          dismissButtonAriaLabel="Close"
          styles={{ root: { marginBottom: 10 } }}
        >
          {successMessage}
        </MessageBar>
      )}

      <Stack horizontal horizontalAlign="space-between" verticalAlign="center" styles={{ root: { marginBottom: 16 } }}>
        <Text variant="xLarge">Team Management</Text>
        <Stack horizontal tokens={{ childrenGap: 8 }}>
          <SearchBox
            placeholder="Search teams..."
            onChange={(_, newValue) => setSearchText(newValue || '')}
            styles={{ root: { width: 300 } }}
          />
          <PermissionBasedAccess requiredPermission="canCreateTeams">
            <CommandBar items={commandItems} />
          </PermissionBasedAccess>
        </Stack>
      </Stack>

      {isLoading ? (
        <Stack horizontalAlign="center" verticalAlign="center" styles={{ root: { padding: 20 } }}>
          <Spinner size={SpinnerSize.large} label="Loading teams..." />
        </Stack>
      ) : (
        <DetailsList
          items={filteredTeams}
          columns={columns}
          setKey="set"
          layoutMode={DetailsListLayoutMode.justified}
          selectionMode={SelectionMode.none}
          isHeaderVisible={true}
        />
      )}

      {/* Add Team Dialog */}
      <Dialog
        hidden={!isAddTeamDialogOpen}
        onDismiss={() => {
          setIsAddTeamDialogOpen(false);
          setErrorMessage(null);
        }}
        dialogContentProps={{
          type: DialogType.normal,
          title: 'Create New Team',
          subText: 'Fill in the details to create a new team.'
        }}
        modalProps={{
          isBlocking: true,
          styles: { main: { maxWidth: 450 } }
        }}
      >
        <Stack tokens={stackTokens}>
          <TextField
            label="Team Name"
            required
            value={newTeam.name}
            onChange={(_, value) => setNewTeam({ ...newTeam, name: value })}
          />
          <TextField
            label="Description"
            multiline
            rows={3}
            value={newTeam.description}
            onChange={(_, value) => setNewTeam({ ...newTeam, description: value })}
          />
          <Dropdown
            label="Region"
            required
            selectedKey={newTeam.region}
            options={regionOptions}
            onChange={(_, option) => option && setNewTeam({ ...newTeam, region: option.key as string })}
            disabled={currentUser?.role === UserRole.REGIONAL_ADMIN} // Regional admins can only create teams in their region
          />
        </Stack>
        <DialogFooter>
          <PrimaryButton onClick={handleAddTeam} text="Create" />
          <DefaultButton onClick={() => {
            setIsAddTeamDialogOpen(false);
            setErrorMessage(null);
          }} text="Cancel" />
        </DialogFooter>
      </Dialog>

      {/* Edit Team Dialog */}
      <Dialog
        hidden={!isEditTeamDialogOpen}
        onDismiss={() => {
          setIsEditTeamDialogOpen(false);
          setErrorMessage(null);
        }}
        dialogContentProps={{
          type: DialogType.normal,
          title: 'Edit Team',
          subText: 'Update team details.'
        }}
        modalProps={{
          isBlocking: true,
          styles: { main: { maxWidth: 450 } }
        }}
      >
        <Stack tokens={stackTokens}>
          <TextField
            label="Team Name"
            required
            value={newTeam.name}
            onChange={(_, value) => setNewTeam({ ...newTeam, name: value })}
          />
          <TextField
            label="Description"
            multiline
            rows={3}
            value={newTeam.description}
            onChange={(_, value) => setNewTeam({ ...newTeam, description: value })}
          />
          <Dropdown
            label="Region"
            required
            selectedKey={newTeam.region}
            options={regionOptions}
            onChange={(_, option) => option && setNewTeam({ ...newTeam, region: option.key as string })}
            disabled={currentUser?.role === UserRole.REGIONAL_ADMIN} // Regional admins can only manage teams in their region
          />
        </Stack>
        <DialogFooter>
          <PrimaryButton onClick={handleSaveEdit} text="Save" />
          <DefaultButton onClick={() => {
            setIsEditTeamDialogOpen(false);
            setErrorMessage(null);
          }} text="Cancel" />
        </DialogFooter>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        hidden={!isDeleteDialogOpen}
        onDismiss={() => setIsDeleteDialogOpen(false)}
        dialogContentProps={{
          type: DialogType.normal,
          title: 'Confirm Delete',
          subText: `Are you sure you want to delete the team "${selectedTeam?.name}"? This action cannot be undone.`
        }}
        modalProps={{
          isBlocking: true,
          styles: { main: { maxWidth: 450 } }
        }}
      >
        <DialogFooter>
          <PrimaryButton onClick={handleDeleteTeam} text="Delete" />
          <DefaultButton onClick={() => setIsDeleteDialogOpen(false)} text="Cancel" />
        </DialogFooter>
      </Dialog>

      {/* Assign Projects Dialog */}
      <Dialog
        hidden={!isAssignProjectsDialogOpen}
        onDismiss={() => {
          setIsAssignProjectsDialogOpen(false);
          setSelectedProjects([]);
        }}
        dialogContentProps={{
          type: DialogType.normal,
          title: 'Assign Projects',
          subText: `Select projects to assign to the team "${selectedTeam?.name}".`
        }}
        modalProps={{
          isBlocking: true,
          styles: { main: { maxWidth: 450 } }
        }}
      >
        <Stack tokens={stackTokens} styles={{ root: { maxHeight: 400, overflowY: 'auto' } }}>
          <Label>Available Projects:</Label>
          {availableProjects.map(project => (
            <Checkbox
              key={project.id}
              label={project.name}
              checked={selectedProjects.includes(project.id)}
              onChange={(_, checked) => onProjectCheckboxChange(project.id, checked)}
            />
          ))}
        </Stack>
        <DialogFooter>
          <PrimaryButton onClick={handleSaveProjects} text="Save" />
          <DefaultButton onClick={() => {
            setIsAssignProjectsDialogOpen(false);
            setSelectedProjects([]);
          }} text="Cancel" />
        </DialogFooter>
      </Dialog>

      {/* Assign Members Dialog */}
      <Dialog
        hidden={!isAssignMembersDialogOpen}
        onDismiss={() => {
          setIsAssignMembersDialogOpen(false);
          setSelectedMembers([]);
        }}
        dialogContentProps={{
          type: DialogType.normal,
          title: 'Assign Members',
          subText: `Select users to assign to the team "${selectedTeam?.name}".`
        }}
        modalProps={{
          isBlocking: true,
          styles: { main: { maxWidth: 450 } }
        }}
      >
        <Stack tokens={stackTokens} styles={{ root: { maxHeight: 400, overflowY: 'auto' } }}>
          <Label>Available Users:</Label>
          {availableUsers.map(user => (
            <Checkbox
              key={user.id}
              label={`${user.name} (${user.email})`}
              checked={selectedMembers.includes(user.id)}
              onChange={(_, checked) => onMemberCheckboxChange(user.id, checked)}
            />
          ))}
        </Stack>
        <DialogFooter>
          <PrimaryButton onClick={handleSaveMembers} text="Save" />
          <DefaultButton onClick={() => {
            setIsAssignMembersDialogOpen(false);
            setSelectedMembers([]);
          }} text="Cancel" />
        </DialogFooter>
      </Dialog>
    </div>
  );
};

export default TeamManagement;
