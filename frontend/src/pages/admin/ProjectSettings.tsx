import React, { useState, useEffect } from 'react';
import {
  Stack,
  Text,
  DetailsList,
  DetailsListLayoutMode,
  SelectionMode,
  IColumn,
  SearchBox,
  Dropdown,
  IDropdownOption,
  PrimaryButton,
  DefaultButton,
  Dialog,
  DialogType,
  Dialog<PERSON>ooter,
  TextField,
  SpinButton,
  ISpinButtonStyles,
  MessageBar,
  MessageBarType,
  IStackTokens,
  CommandBar,
  ICommandBarItemProps,
  IconButton,
  TooltipHost,
  DirectionalHint,
  Spinner,
  SpinnerSize
} from '@fluentui/react';
import { useUser } from '../../state/UserProvider';
import { UserRole, getRolePermissions } from '../../models/roles';
// import rbacService, { ProjectData, TeamData, RegionData } from '../../services/rbacService'; // Removed rbacService
import userContextService from '../../services/userContextService'; // Added userContextService
import { ProjectData, TeamData, RegionData } from '../../services/rbacService'; // Keep type definitions if still needed
import { PermissionBasedAccess } from '../../components/RoleBasedAccess';

// Stack tokens
const stackTokens: IStackTokens = { childrenGap: 15 };

// SpinButton styles
const spinButtonStyles: Partial<ISpinButtonStyles> = {
  root: { width: 200 }
};

// Mock project data
interface ProjectSettings {
  id: string;
  name: string;
  region: string;
  costLimit: number;
  azureFunctionLimit: number;
  storageQuota: number;
  owner: string;
  createdAt: string;
  teams?: string[]; // Teams assigned to this project
}

const mockProjects: ProjectSettings[] = [
  {
    id: '1',
    name: 'Marketing Analytics',
    region: 'North America',
    costLimit: 1500,
    azureFunctionLimit: 800,
    storageQuota: 10,
    owner: 'Jane Smith',
    createdAt: '2023-05-15',
    teams: ['1', '3']
  },
  {
    id: '2',
    name: 'Customer Support AI',
    region: 'Europe',
    costLimit: 2000,
    azureFunctionLimit: 1000,
    storageQuota: 15,
    owner: 'Bob Johnson',
    createdAt: '2023-06-22',
    teams: ['2']
  },
  {
    id: '3',
    name: 'Sales Forecasting',
    region: 'North America',
    costLimit: 1200,
    azureFunctionLimit: 600,
    storageQuota: 8,
    owner: 'Jane Smith',
    createdAt: '2023-07-10',
    teams: ['1']
  },
  {
    id: '4',
    name: 'Product Recommendation Engine',
    region: 'Asia Pacific',
    costLimit: 1800,
    azureFunctionLimit: 900,
    storageQuota: 12,
    owner: 'Diana Miller',
    createdAt: '2023-08-05',
    teams: ['3']
  },
  {
    id: '5',
    name: 'HR Document Analysis',
    region: 'Europe',
    costLimit: 1000,
    azureFunctionLimit: 500,
    storageQuota: 5,
    owner: 'Charlie Wilson',
    createdAt: '2023-09-18',
    teams: ['2']
  }
];

// Initial region options - will be loaded from API
const initialRegionOptions: IDropdownOption[] = [
  { key: 'all', text: 'All Regions' }
];

// Mock teams data (simplified version of what's in TeamManagement)
const mockTeams = [
  {
    id: '1',
    name: 'Marketing Team',
    region: 'North America'
  },
  {
    id: '2',
    name: 'Sales Team',
    region: 'Europe'
  },
  {
    id: '3',
    name: 'Product Team',
    region: 'North America'
  }
];

const ProjectSettings: React.FC = () => {
  const { currentUser } = useUser();
  const [projects, setProjects] = useState<ProjectSettings[]>([]);
  const [filteredProjects, setFilteredProjects] = useState<ProjectSettings[]>([]);
  const [searchText, setSearchText] = useState('');
  const [selectedRegion, setSelectedRegion] = useState<string>('all');
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState<ProjectSettings | null>(null);
  const [editedSettings, setEditedSettings] = useState<Partial<ProjectSettings>>({});
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [regionOptions, setRegionOptions] = useState<IDropdownOption[]>(initialRegionOptions);
  const [teams, setTeams] = useState<any[]>([]);
  const [users, setUsers] = useState<{[key: string]: {name: string, email: string}}>({});

  // Get permissions based on user role
  const permissions = currentUser ? getRolePermissions(currentUser.role) : getRolePermissions(UserRole.REGULAR_USER);

  // Columns for the DetailsList
  const columns: IColumn[] = [
    {
      key: 'name',
      name: 'Project Name',
      fieldName: 'name',
      minWidth: 150,
      maxWidth: 200,
      isResizable: true
    },
    {
      key: 'region',
      name: 'Region',
      fieldName: 'region',
      minWidth: 100,
      maxWidth: 150,
      isResizable: true,
      onRender: (item: ProjectSettings) => {
        // Find the region name from the region ID
        const regionOption = regionOptions.find(r => r.key === item.region);
        return <span>{regionOption && regionOption.key !== 'all' ? regionOption.text : item.region}</span>;
      }
    },
    {
      key: 'teams',
      name: 'Assigned Teams',
      minWidth: 150,
      maxWidth: 200,
      isResizable: true,
      onRender: (item: ProjectSettings) => (
        <div>
          {item.teams && item.teams.length > 0 ? (
            <TooltipHost
              content={
                <div>
                  <Text variant="medium" styles={{ root: { fontWeight: 600, marginBottom: 5 } }}>
                    Assigned Teams:
                  </Text>
                  <ul style={{ margin: 0, paddingLeft: 20 }}>
                    {item.teams.map(teamId => {
                      const team = teams.find(t => t.id === teamId);
                      return <li key={teamId}>{team ? team.name : 'Unknown Team'}</li>;
                    })}
                  </ul>
                </div>
              }
              styles={{ root: { display: 'inline-block' } }}
              directionalHint={DirectionalHint.rightCenter}
            >
              <Text styles={{ root: { textDecoration: 'underline', cursor: 'pointer' } }}>
                {item.teams.length} {item.teams.length === 1 ? 'Team' : 'Teams'}
              </Text>
            </TooltipHost>
          ) : (
            <Text>None</Text>
          )}
        </div>
      )
    },
    {
      key: 'costLimit',
      name: 'Cost Limit ($)',
      fieldName: 'costLimit',
      minWidth: 100,
      maxWidth: 100,
      isResizable: true
    },
    {
      key: 'azureFunctionLimit',
      name: 'Azure Function Limit',
      fieldName: 'azureFunctionLimit',
      minWidth: 120,
      maxWidth: 150,
      isResizable: true
    },
    {
      key: 'storageQuota',
      name: 'Storage Quota (GB)',
      fieldName: 'storageQuota',
      minWidth: 120,
      maxWidth: 150,
      isResizable: true
    },
    {
      key: 'owner',
      name: 'Owner',
      fieldName: 'owner',
      minWidth: 120,
      maxWidth: 150,
      isResizable: true,
      onRender: (item: ProjectSettings) => {
        // Display the owner name if available, otherwise show the ID
        const ownerInfo = users[item.owner];
        return <span>{ownerInfo ? ownerInfo.name : item.owner}</span>;
      }
    },
    {
      key: 'actions',
      name: 'Actions',
      minWidth: 100,
      maxWidth: 100,
      isResizable: false,
      onRender: (item: ProjectSettings) => (
        <Stack horizontal tokens={{ childrenGap: 8 }}>
          <PermissionBasedAccess
            requiredPermission="canEditProject"
            fallback={
              <IconButton
                iconProps={{ iconName: 'Edit' }}
                title="Edit Settings"
                ariaLabel="Edit Settings"
                disabled={true}
              />
            }
          >
            <IconButton
              iconProps={{ iconName: 'Edit' }}
              title="Edit Settings"
              ariaLabel="Edit Settings"
              onClick={() => handleEditProject(item)}
              disabled={!canEditProject(item)}
            />
          </PermissionBasedAccess>
          <TooltipHost
            content="View Project Details"
            directionalHint={DirectionalHint.bottomCenter}
          >
            <IconButton
              iconProps={{ iconName: 'RedEye' }}
              title="View Project"
              ariaLabel="View Project"
              onClick={() => handleViewProject(item)}
            />
          </TooltipHost>
        </Stack>
      )
    }
  ];

  // Command bar items
  const commandItems: ICommandBarItemProps[] = [
    {
      key: 'refresh',
      text: 'Refresh',
      iconProps: { iconName: 'Refresh' },
      onClick: () => { loadProjects(); return; }
    }
  ];

  // Load projects on component mount
  useEffect(() => {
    const initializeData = async () => {
      await loadRegions();
      await loadTeams();
      await loadUsers();
      await loadProjects();
      setIsLoading(false);
    };

    initializeData();
  }, []);

  // Filter projects when search text or region changes
  useEffect(() => {
    let filtered = [...projects];

    // Filter by search text
    if (searchText) {
      filtered = filtered.filter(project =>
        project.name.toLowerCase().includes(searchText.toLowerCase()) ||
        project.owner.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    // Filter by region
    if (selectedRegion !== 'all') {
      filtered = filtered.filter(project => project.region === selectedRegion);
    }

    setFilteredProjects(filtered);
  }, [searchText, selectedRegion, projects]);

  // Load regions from API using userContextService
  const loadRegions = async () => {
    try {
      const userContext = await userContextService.fetchUserContext();
      if (!userContext || !userContext.accessibleResources.regions) {
        console.error('Error loading regions from user context');
        setErrorMessage('Failed to load regions.');
        // Fallback or keep existing options
        setRegionOptions(prev => prev.length > 1 ? prev : initialRegionOptions);
        return;
      }

      const apiRegionOptions = userContext.accessibleResources.regions.map((region: { id: string, name: string }) => ({
        key: region.id,
        text: region.name
      }));

      setRegionOptions([...initialRegionOptions, ...apiRegionOptions]);
    } catch (error) {
      console.error('Error fetching regions via userContext:', error);
      setErrorMessage('Failed to load regions. Please try again later.');
      // Fallback or keep existing options
      setRegionOptions(prev => prev.length > 1 ? prev : initialRegionOptions);
    }
  };

  // Load teams from API using fetch with authentication
  const loadTeams = async () => {
    try {
      // Import the getAuthHeaders function from authHeaderService
      const { getAuthHeaders } = await import('../../services/authHeaderService');

      // Get authentication headers
      const authHeaders = await getAuthHeaders();
      console.log('Fetching teams with auth headers');

      const response = await fetch('/api/rbac/teams', {
        headers: authHeaders
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      setTeams(data);
    } catch (error) {
      console.error('Error fetching teams:', error);
      setErrorMessage('Failed to load teams. Using mock data for development.');
      setTeams(mockTeams); // Fallback to mock data
    }
  };

  // Load users from API using fetch with authentication
  const loadUsers = async () => {
    try {
      // Import the getAuthHeaders function from authHeaderService
      const { getAuthHeaders } = await import('../../services/authHeaderService');

      // Get authentication headers
      const authHeaders = await getAuthHeaders();
      console.log('Fetching users with auth headers');

      const response = await fetch('/api/rbac/users', {
        headers: authHeaders
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      // Convert array of users to a map for easy lookup
      const usersMap: {[key: string]: {name: string, email: string}} = {};
      data.forEach((user: { id: string, name: string, email: string }) => {
        usersMap[user.id] = {
          name: user.name,
          email: user.email
        };
      });
      setUsers(usersMap);
    } catch (error) {
      console.error('Error fetching users:', error);
      setErrorMessage('Failed to load users.');
    }
  };

  // Load projects from API using fetch with authentication
  const loadProjects = async () => {
    try {
      setIsLoading(true);

      // Import the getAuthHeaders function from authHeaderService
      const { getAuthHeaders } = await import('../../services/authHeaderService');

      // Get authentication headers
      const authHeaders = await getAuthHeaders();
      console.log('Fetching projects with auth headers');

      const projectsResponse = await fetch('/api/projects', {
        headers: authHeaders
      });

      if (!projectsResponse.ok) {
        const errorData = await projectsResponse.json();
        throw new Error(errorData.error || `HTTP error! status: ${projectsResponse.status}`);
      }
      const projectsData = await projectsResponse.json();

      // Convert API response to ProjectSettings format
      const apiProjects: ProjectSettings[] = await Promise.all(projectsData.map(async (projectData: ProjectData) => {
        // Get teams assigned to this project using fetch with authentication
        let teamIds: string[] = [];
        try {
          const teamsResponse = await fetch(`/api/rbac/projects/${projectData.id}/teams`, {
            headers: authHeaders
          });

          if (teamsResponse.ok) {
            const teamsData = await teamsResponse.json();
            teamIds = teamsData.map((team: { teamId: string }) => team.teamId);
          } else {
            console.warn(`Failed to fetch teams for project ${projectData.id}: ${teamsResponse.status}`);
          }
        } catch (teamsError) {
          console.warn(`Error fetching teams for project ${projectData.id}:`, teamsError);
        }

        return {
          id: projectData.id,
          name: projectData.name,
          region: projectData.region,
          costLimit: projectData.cost_limit || 1000,
          azureFunctionLimit: 500, // Default value, not in API
          storageQuota: 5, // Default value, not in API
          owner: projectData.owner,
          createdAt: projectData.created_at,
          teams: teamIds
        };
      }));

      // Filter projects based on current user's role and region
      let filteredApiProjects = apiProjects;

      // Super Admins can see all projects from all regions
      if (currentUser?.role === UserRole.SUPER_ADMIN) {
        // No filtering needed, they see everything
        console.log('Super Admin sees all projects:', filteredApiProjects);
      } else if (currentUser?.role === UserRole.REGIONAL_ADMIN && currentUser?.region) {
        // Regional admins should only see projects in their region
        filteredApiProjects = apiProjects.filter(project => project.region === currentUser.region);
        console.log('Filtered projects for Regional Admin:', filteredApiProjects);
      } else if (currentUser?.role === UserRole.REGULAR_USER) {
        // Regular users should only see projects they are assigned to
        // This would typically be handled by the backend, but we're adding an extra check here
        filteredApiProjects = apiProjects.filter(project => {
          // Check if the user is assigned to this project
          // This is a simplified check - in a real app, you would check project assignments
          return project.owner === currentUser.id;
        });
      }

      setProjects(filteredApiProjects);
      setFilteredProjects(filteredApiProjects);
    } catch (error) {
      console.error('Error fetching projects:', error);
      // Fallback to mock data for development
      setProjects(mockProjects);
      setFilteredProjects(mockProjects);
    } finally {
      setIsLoading(false);
    }
  };

  // Check if user can edit a project
  const canEditProject = (project: ProjectSettings): boolean => {
    if (currentUser?.role === UserRole.SUPER_ADMIN) {
      return true;
    }

    if (currentUser?.role === UserRole.REGIONAL_ADMIN) {
      return project.region === currentUser.region;
    }

    return false;
  };



  // Handle editing a project
  const handleEditProject = (project: ProjectSettings) => {
    setSelectedProject(project);
    setEditedSettings({
      costLimit: project.costLimit,
      azureFunctionLimit: project.azureFunctionLimit,
      storageQuota: project.storageQuota
    });
    setIsEditDialogOpen(true);
  };

  // Handle viewing a project
  const handleViewProject = (project: ProjectSettings) => {
    // Navigate to project details page
    window.location.href = `/project/${project.id}`;
  };

  // Handle saving edited project settings
  const handleSaveSettings = async () => {
    if (!selectedProject) return;

    // Validate settings
    if (
      editedSettings.costLimit === undefined ||
      editedSettings.azureFunctionLimit === undefined ||
      editedSettings.storageQuota === undefined
    ) {
      setErrorMessage('All fields are required.');
      return;
    }

    try {
      // Only send the field we want to update
      const projectData = {
        cost_limit: editedSettings.costLimit
        // Note: azureFunctionLimit and storageQuota are not part of the API yet
        // We would need to extend the API to support these fields
      };

      console.log('Updating project with partial data:', projectData);

      // Import the getAuthHeaders function from authHeaderService
      const { getAuthHeaders } = await import('../../services/authHeaderService');

      // Get authentication headers
      const authHeaders = await getAuthHeaders();
      console.log('Updating project with auth headers');

      // Call API to update project using fetch with authentication
      const updateResponse = await fetch(`/api/projects/${selectedProject.id}`, {
        method: 'PUT', // Or PATCH, depending on your API design for partial updates
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(projectData),
      });

      if (!updateResponse.ok) {
        const errorData = await updateResponse.json();
        setErrorMessage(`Failed to update project: ${errorData.error || errorData.message || 'Unknown error'}`);
        return;
      }

      // Refresh projects list
      await loadProjects();

      // Reset form
      setIsEditDialogOpen(false);
      setSelectedProject(null);
      setEditedSettings({});
      setErrorMessage(null);

      // Show success message
      setSuccessMessage(`Settings for "${selectedProject.name}" updated successfully.`);
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } catch (error) {
      console.error('Error updating project:', error);
      setErrorMessage('Failed to update project. Please try again later.');
    }
  };

  return (
    <Stack tokens={stackTokens}>
      <Text variant="xLarge">Project Settings</Text>

      {errorMessage && (
        <MessageBar
          messageBarType={MessageBarType.error}
          isMultiline={false}
          onDismiss={() => setErrorMessage(null)}
          dismissButtonAriaLabel="Close"
        >
          {errorMessage}
        </MessageBar>
      )}

      {successMessage && (
        <MessageBar
          messageBarType={MessageBarType.success}
          isMultiline={false}
          onDismiss={() => setSuccessMessage(null)}
          dismissButtonAriaLabel="Close"
        >
          {successMessage}
        </MessageBar>
      )}

      <Stack horizontal horizontalAlign="space-between" verticalAlign="center" styles={{ root: { marginBottom: 16 } }}>
        <Stack horizontal tokens={{ childrenGap: 16 }}>
          <SearchBox
            placeholder="Search projects..."
            onChange={(_, newValue) => setSearchText(newValue || '')}
            styles={{ root: { width: 300 } }}
          />

          <Dropdown
            placeholder="Select Region"
            label=""
            selectedKey={selectedRegion}
            options={regionOptions}
            styles={{ root: { width: 200 } }}
            onChange={(_, option) => option && setSelectedRegion(option.key as string)}
          />
        </Stack>

        <CommandBar items={commandItems} />
      </Stack>

      {isLoading ? (
        <Stack horizontalAlign="center" verticalAlign="center" styles={{ root: { padding: 20 } }}>
          <Spinner size={SpinnerSize.large} label="Loading projects..." />
        </Stack>
      ) : (
        <DetailsList
          items={filteredProjects}
          columns={columns}
          setKey="set"
          layoutMode={DetailsListLayoutMode.justified}
          selectionMode={SelectionMode.none}
          isHeaderVisible={true}
        />
      )}

      {/* Edit Project Settings Dialog */}
      <Dialog
        hidden={!isEditDialogOpen}
        onDismiss={() => {
          setIsEditDialogOpen(false);
          setErrorMessage(null);
        }}
        dialogContentProps={{
          type: DialogType.normal,
          title: `Edit Settings: ${selectedProject?.name}`,
          subText: 'Update project resource limits.'
        }}
        modalProps={{
          isBlocking: true,
          styles: { main: { maxWidth: 450 } }
        }}
      >
        <Stack tokens={stackTokens}>
          <Text>Region: {selectedProject?.region}</Text>

          <Stack.Item>
            <Text>Cost Limit ($)</Text>
            <SpinButton
              value={editedSettings.costLimit?.toString()}
              min={0}
              max={10000}
              step={100}
              incrementButtonAriaLabel="Increase value"
              decrementButtonAriaLabel="Decrease value"
              onChange={(_, value) => setEditedSettings({
                ...editedSettings,
                costLimit: Number(value)
              })}
              styles={spinButtonStyles}
            />
          </Stack.Item>

          <Stack.Item>
            <Text>Azure Function Daily Limit</Text>
            <SpinButton
              value={editedSettings.azureFunctionLimit?.toString()}
              min={0}
              max={5000}
              step={50}
              incrementButtonAriaLabel="Increase value"
              decrementButtonAriaLabel="Decrease value"
              onChange={(_, value) => setEditedSettings({
                ...editedSettings,
                azureFunctionLimit: Number(value)
              })}
              styles={spinButtonStyles}
            />
          </Stack.Item>

          <Stack.Item>
            <Text>Storage Quota (GB)</Text>
            <SpinButton
              value={editedSettings.storageQuota?.toString()}
              min={1}
              max={100}
              step={1}
              incrementButtonAriaLabel="Increase value"
              decrementButtonAriaLabel="Decrease value"
              onChange={(_, value) => setEditedSettings({
                ...editedSettings,
                storageQuota: Number(value)
              })}
              styles={spinButtonStyles}
            />
          </Stack.Item>
        </Stack>
        <DialogFooter>
          <PrimaryButton onClick={handleSaveSettings} text="Save" />
          <DefaultButton onClick={() => {
            setIsEditDialogOpen(false);
            setErrorMessage(null);
            setSelectedProject(null);
            setEditedSettings({});
          }} text="Cancel" />
        </DialogFooter>
      </Dialog>
    </Stack>
  );
};

export default ProjectSettings;
