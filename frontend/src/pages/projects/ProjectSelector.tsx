import React, { useEffect, useState, useContext, useRef, useCallback } from 'react';
import {
  Stack,
  Text,
  DefaultButton,
  IconButton,
  MessageBar,
  MessageBarType,
  Spinner,
  SpinnerSize,
  IButtonStyles,
  ContextualMenu,
  Dialog,
  DialogType,
  DialogFooter,
  PrimaryButton,
  IContextualMenuItem,
  DirectionalHint,
  Icon,
  CommandButton,
  IContextualMenuProps,
  TextField,
  ProgressIndicator
} from '@fluentui/react';
import { useNavigate, useLocation } from 'react-router-dom';
import { AddRegular, GridRegular, ListRegular } from '@fluentui/react-icons';
import useAuthCheck from '../../hooks/useAuthCheck';
import styles from './ProjectSelector.module.css';
import { AppStateContext } from '../../state/AppProvider';
import { useUser } from '../../state/UserProvider';
import { UserRole, RolePermissions } from '../../models/roles'; // Added RolePermissions
import CompanyLogo from '../../assets/keyrus-2.svg';
import { projectDelete } from '../../api/api';
import { getCachedProjects, cacheProjects, getCachedProjectStatus, cacheProjectStatus } from '../../utils/projectCache';
import RoleBasedHeader from '../../components/RoleBasedHeader';
import ProjectActionsMenu from '../../components/ProjectActionsMenu';
import userContextService from '../../services/userContextService';
import * as WebSocketManager from '../../services/websocketManager';

interface Project {
  id: string;
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
  search_index: string;
  storage_container: string;
  // ai_service_version: string; // Removed as it wasn't in backend data
  role: 'owner' | 'contributor' | 'viewer';
  environment: Record<string, string>;
  color?: string;
  icon?: string;
  deploymentStatus?: 'pending' | 'in_progress' | 'completed' | 'failed';
  // Backend response format may include these fields
  deployment_status?: {
    status?: string;
    details?: Record<string, any>;
    error?: string;
  };
  deploymentDetails?: Record<string, any>;
  teams?: string[]; // Teams assigned to this project
  region?: string; // Region this project belongs to
  deploymentStartTime?: string; // When deployment started
  deploymentProgress?: number; // Progress percentage (0-100)
}

type ViewType = 'grid' | 'list';

const ProjectSelector: React.FC = () => {
  const { isCheckingAuth } = useAuthCheck('/');
  const [projects, setProjects] = useState<Project[]>([]);
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);
  const [searchText, setSearchText] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewType, setViewType] = useState<ViewType>('grid');
  const [logo, setLogo] = useState<string>(CompanyLogo);
  const [menuTarget, setMenuTarget] = useState<HTMLElement | null>(null);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showRegionFilter, setShowRegionFilter] = useState<boolean>(false);
  const [selectedRegion, setSelectedRegion] = useState<string>('all');
  const [isEditDialogOpen, setIsEditDialogOpen] = useState<boolean>(false);
  const [editedProjectName, setEditedProjectName] = useState<string>('');
  const [editedProjectDescription, setEditedProjectDescription] = useState<string>('');
  const [regionMap, setRegionMap] = useState<{[key: string]: string}>({});
  const [isLoadingRegions, setIsLoadingRegions] = useState<boolean>(false);
  const [showNewProjectBanner, setShowNewProjectBanner] = useState(false);
  const [newProjectName, setNewProjectName] = useState('');
  const appStateContext = useContext(AppStateContext);
  const { currentUser, isLoading: isUserLoading } = useUser(); // Get currentUser and its loading state

  // Default permissions structure
  const defaultPermissions: RolePermissions = {
    canCreateProject: false,
    canEditProject: false,
    canDeleteProject: false,
    canAssignUsers: false,
    canSetCostLimits: false,
    canAccessAdminPanel: false,
    canManageUsers: false,
    canManageGlobalSettings: false,
    canCreateTeams: false,
    canAssignProjects: false,
    canAssignTeams: false,
    canSetupRegionalAdmins: false,
    canTagUsers: false,
    canViewAllRegions: false
  };
  const viewPermissions: RolePermissions = currentUser?.permissions || defaultPermissions; // Use permissions from currentUser or default

  const ui = appStateContext?.state.frontendSettings?.ui;
  const navigate = useNavigate();
  const menuButtonRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  // Load regions from currentUser context
  useEffect(() => {
    if (currentUser && currentUser.accessibleResources && currentUser.accessibleResources.regions) {
      setIsLoadingRegions(true);
      const regionMapping: {[key: string]: string} = {};
      currentUser.accessibleResources.regions.forEach((region: any) => { // Use 'any' or a proper Region type
        regionMapping[region.id] = region.name;
      });
      setRegionMap(regionMapping);
      setIsLoadingRegions(false);
    } else if (!isUserLoading) {
      // User loaded but no regions, or accessibleResources missing
      console.warn('No accessible regions found in currentUser context.');
      setIsLoadingRegions(false);
    }
    // Reset region filter to 'all' when component mounts or currentUser changes
    setSelectedRegion('all');
  }, [currentUser, isUserLoading]);

  // Check for newly created project and show banner
  useEffect(() => {
    // Check if we have a newly created project
    const newProjectId = localStorage.getItem('newProjectId');
    const newProjectName = localStorage.getItem('newProjectName');

    if (newProjectId && newProjectName) {
      console.log('Found new project in localStorage:', { newProjectId, newProjectName });
      setShowNewProjectBanner(true);
      setNewProjectName(newProjectName);

      // Keep the banner for 30 seconds, then clear localStorage
      setTimeout(() => {
        localStorage.removeItem('newProjectId');
        localStorage.removeItem('newProjectName');
        localStorage.removeItem('newProjectCreatedAt');
      }, 30000);
    }
  }, []);

  // Calculate deployment progress based on time (20 minute deployment time)
  const calculateDeploymentProgress = useCallback((startTime: string) => {
    const DEPLOYMENT_TIME_MS = 20 * 60 * 1000; // 20 minutes in milliseconds
    const start = new Date(startTime).getTime();
    const now = new Date().getTime();
    const elapsed = now - start;

    // If more than 20 minutes have passed, return 100%
    if (elapsed >= DEPLOYMENT_TIME_MS) {
      return 100;
    }

    // Otherwise, calculate percentage (0-100)
    return Math.floor((elapsed / DEPLOYMENT_TIME_MS) * 100);
  }, []);

  // Close any existing WebSocket connections when the ProjectSelector mounts
  // This ensures we don't have lingering connections from previous project pages
  useEffect(() => {
    console.log('ProjectSelector mounted - closing all WebSocket connections');
    WebSocketManager.closeAllWebSockets();

    // Set current project to null to prevent new connections
    WebSocketManager.setCurrentProject(null);

    return () => {
      // No cleanup needed here
    };
  }, []);

  // Update deployment progress every 30 seconds
  useEffect(() => {
    // Only update if we have projects with pending or in_progress status
    const hasDeployingProjects = filteredProjects.some(
      p => p.deploymentStatus === 'pending' || p.deploymentStatus === 'in_progress'
    );

    if (!hasDeployingProjects) return;

    // Update progress every 30 seconds
    const interval = setInterval(() => {
      setFilteredProjects(prevProjects => {
        return prevProjects.map(project => {
          // Only update projects that are pending or in_progress
          if (project.deploymentStatus !== 'pending' && project.deploymentStatus !== 'in_progress') {
            return project;
          }

          // Get deployment start time from environment or use created_at
          const deploymentStartTime = project.environment?.deploymentStartTime;
          const startTime = deploymentStartTime || project.created_at;
          if (!startTime) return project;

          // Calculate new progress
          const progress = calculateDeploymentProgress(startTime);

          // If progress is 100% but status is still pending/in_progress, mark as failed
          let newStatus: 'pending' | 'in_progress' | 'completed' | 'failed' = project.deploymentStatus;
          if (progress >= 100 && (newStatus === 'pending' || newStatus === 'in_progress')) {
            newStatus = 'failed';
          }

          return {
            ...project,
            deploymentProgress: progress,
            deploymentStatus: newStatus
          };
        });
      });
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [filteredProjects, calculateDeploymentProgress]);

  // Mock teams data for demonstration
  const mockTeams = [
    { id: '1', name: 'Marketing Team', region: 'North America' },
    { id: '2', name: 'Sales Team', region: 'Europe' },
    { id: '3', name: 'Product Team', region: 'North America' }
  ];

  useEffect(() => {
    if (!appStateContext?.state.isLoading) {
      setLogo(ui?.logo || CompanyLogo);
    }
  }, [appStateContext?.state.isLoading, ui?.logo]);

  const getRandomColor = () => {
    // Always return the same white to light blue gradient for consistency
    return 'linear-gradient(135deg, #ffffff 0%, #add8e6 100%)';
  };

  const getRandomIcon = () => {
    const icons = ['📊', '📈', '🤖', '📋', '📑', '📚', '📝', '🔍', '💡', '🌐'];
    return icons[Math.floor(Math.random() * icons.length)];
  };

  // Function to retry API calls with exponential backoff and caching support
  const fetchWithRetry = async (url: string, options = {}, maxRetries = 5, initialDelay = 1000) => {
    let retries = 0;
    let delay = initialDelay;

    // Get cached ETag if available
    const cachedETag = localStorage.getItem(`etag:${url}`);

    while (retries < maxRetries) {
      try {
        // Merge default headers with provided options
        const mergedOptions = {
          ...options,
          headers: {
            'Accept-Encoding': 'gzip, deflate', // Enable compression
            ...(cachedETag ? { 'If-None-Match': cachedETag } : {}), // Add ETag if available
            ...(options as any).headers
          }
        };

        const response = await fetch(url, mergedOptions);

        // Handle 304 Not Modified - use cached data
        if (response.status === 304) {
          console.log(`304 Not Modified for ${url}, using cached data`);
          const cachedData = localStorage.getItem(`response:${url}`);
          if (cachedData) {
            // Create a mock response with the cached data
            return {
              ok: true,
              status: 200,
              headers: new Headers({ 'Content-Type': 'application/json' }),
              json: async () => JSON.parse(cachedData)
            } as Response;
          }
          // If we don't have cached data, continue with normal flow
        }

        if (response.status === 503) {
          // Service unavailable - likely CosmosDB not ready yet
          console.log(`Service unavailable (attempt ${retries + 1}/${maxRetries}), retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          retries++;
          delay *= 2; // Exponential backoff
          continue;
        }

        // Consider 404 as a valid response for empty projects list
        if (response.ok || response.status === 404) {
          // For successful responses, cache the ETag and response
          if (response.ok) {
            const etag = response.headers.get('ETag');
            if (etag) {
              localStorage.setItem(`etag:${url}`, etag);

              // Clone and cache the response data
              const clonedResponse = response.clone();
              try {
                const data = await clonedResponse.json();
                localStorage.setItem(`response:${url}`, JSON.stringify(data));
              } catch (e) {
                console.error('Error caching response data:', e);
              }
            }
          }
          return response;
        }

        // If we get here, it's an error response but not 503 or 404
        console.log(`Error response (${response.status}) for ${url}, retrying...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        retries++;
        delay *= 2; // Exponential backoff
        continue;
      } catch (error) {
        if (retries >= maxRetries - 1) throw error;
        console.log(`Network error (attempt ${retries + 1}/${maxRetries}), retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        retries++;
        delay *= 2; // Exponential backoff
      }
    }
    throw new Error(`Failed after ${maxRetries} retries`);
  };



  // Function to enhance projects with visual elements and default status
  const enhanceProjects = useCallback((projects: Project[]) => {
    return projects.map(project => {
      // Set default values
      const enhancedProject = {
        ...project,
        color: project.color || getRandomColor(),
        icon: project.icon || getRandomIcon(), // Add icon if missing
        deploymentStatus: project.deploymentStatus || 'pending' as 'pending' | 'in_progress' | 'completed' | 'failed'
      };

      // If project is in progress or pending, add deployment start time and calculate progress
      if (enhancedProject.deploymentStatus === 'in_progress' || enhancedProject.deploymentStatus === 'pending') {
        // Get deployment start time from environment or use created_at or current time
        const deploymentStartTime = enhancedProject.environment?.deploymentStartTime;
        enhancedProject.deploymentStartTime = deploymentStartTime || enhancedProject.created_at || new Date().toISOString();

        // Calculate progress based on time
        const progress = calculateDeploymentProgress(enhancedProject.deploymentStartTime);
        enhancedProject.deploymentProgress = progress;

        // If progress is 100% but status is still pending/in_progress, mark as failed
        if (progress >= 100 && (enhancedProject.deploymentStatus === 'pending' || enhancedProject.deploymentStatus === 'in_progress')) {
          enhancedProject.deploymentStatus = 'failed';
        }
      }

      return enhancedProject;
    });
  }, [calculateDeploymentProgress]);

  // Function to load mock projects for testing
  const loadMockProjects = () => {
    setIsLoading(true);
    setError(null);

    setTimeout(() => {
      const mockProjects: Project[] = [
        {
          id: '1',
          name: 'Marketing Analytics',
          description: 'AI-powered marketing analytics dashboard',
          created_at: '2023-01-15T12:00:00Z',
          updated_at: '2023-05-20T14:30:00Z',
          search_index: 'index1',
          storage_container: 'container1',
          role: 'owner',
          environment: {},
          color: '#e6f7ff',
          icon: '📊',
          teams: ['1', '3'],
          region: 'North America'
        },
        {
          id: '2',
          name: 'Customer Support AI',
          description: 'AI assistant for customer support',
          created_at: '2023-02-10T09:15:00Z',
          updated_at: '2023-05-18T11:45:00Z',
          search_index: 'index2',
          storage_container: 'container2',
          role: 'contributor',
          environment: {},
          color: '#fff2e8',
          icon: '🤖',
          teams: ['2'],
          region: 'Europe'
        },
        {
          id: '3',
          name: 'Sales Forecasting',
          description: 'AI-driven sales prediction tool',
          created_at: '2023-03-05T16:30:00Z',
          updated_at: '2023-05-15T10:20:00Z',
          search_index: 'index3',
          storage_container: 'container3',
          role: 'viewer',
          environment: {},
          color: '#e6fffb',
          icon: '📈',
          teams: ['1'],
          region: 'North America'
        },
        {
          id: '4',
          name: 'HR Document Analysis',
          description: 'AI tool for analyzing HR documents',
          created_at: '2023-04-20T14:00:00Z',
          updated_at: '2023-05-10T09:30:00Z',
          search_index: 'index4',
          storage_container: 'container4',
          role: 'owner',
          environment: {},
          color: '#f9f0ff',
          icon: '📄',
          teams: ['2'],
          region: 'Europe'
        }
      ];

      // Filter projects based on user role and region
      let filteredProjects = [...mockProjects];

      if (currentUser?.role === UserRole.REGIONAL_ADMIN && currentUser.region) {
        // Regional admins can only see projects in their region
        filteredProjects = mockProjects.filter(project => project.region === currentUser.region);
      } else if (currentUser?.role === UserRole.REGULAR_USER) {
        // Regular users can only see projects they are assigned to via teams
        // For now, we'll simulate this with a simple filter
        // In a real app, this would be based on team membership
        filteredProjects = mockProjects.filter(project =>
          project.teams?.some(teamId => {
            const team = mockTeams.find(t => t.id === teamId);
            return team && team.region === currentUser?.region;
          })
        );
      }

      setProjects(filteredProjects);
      setFilteredProjects(filteredProjects);
      setIsLoading(false);
    }, 1000);
  };

  useEffect(() => {
    if (isCheckingAuth || isUserLoading) { // Wait for auth check and user loading
      setIsLoading(true);
      return;
    }
    setIsLoading(true);
    setError(null);

    if (currentUser && currentUser.accessibleResources && currentUser.accessibleResources.projects) {
      console.log('Using projects from currentUser context');
      const projectsData = currentUser.accessibleResources.projects;
      const validProjectsData = Array.isArray(projectsData) ? projectsData : [];

      const projectsWithStatus = validProjectsData.map((project: any) => { // Use 'any' or a proper Project type from context
        const processedProject = {
          id: project.id,
          name: project.name || 'Unnamed Project',
          description: project.description || '',
          region: project.region || '',
          owner: project.owner || '',
          created_at: project.created_at || new Date().toISOString(),
          updated_at: project.updated_at || new Date().toISOString(),
          search_index: project.search_index_name || '',
          storage_container: project.storage_container_uploads || '',
          role: 'owner' as 'owner' | 'contributor' | 'viewer',
          environment: project.environment || {},
          icon: project.icon || getRandomIcon(),
          color: project.color || getRandomColor(),
          storage_container_uploads: project.storage_container_uploads || '',
          storage_container_input: project.storage_container_input || '',
          storage_container_output: project.storage_container_output || '',
          search_index_name: project.search_index_name || '',
          search_datasource_name: project.search_datasource_name || '',
          search_indexer_name: project.search_indexer_name || ''
        };

        let deploymentStatus: 'pending' | 'in_progress' | 'completed' | 'failed' = 'pending';
        if (project.deploymentStatus && ['pending', 'in_progress', 'completed', 'failed'].includes(project.deploymentStatus)) {
          deploymentStatus = project.deploymentStatus;
        } else if (project.deployment_status && project.deployment_status.status && ['pending', 'in_progress', 'completed', 'failed'].includes(project.deployment_status.status)) {
          deploymentStatus = project.deployment_status.status as 'pending' | 'in_progress' | 'completed' | 'failed';
        }
        return { ...processedProject, deploymentStatus };
      });

      const enhancedProjects = enhanceProjects(projectsWithStatus);
      setProjects(enhancedProjects);
      setFilteredProjects(enhancedProjects);
      cacheProjects(enhancedProjects); // Cache if still needed, or rely on UserProvider's caching
    } else if (currentUser) {
      // User is loaded, but no projects or accessibleResources
      console.warn('No accessible projects found in currentUser context.');
      setProjects([]);
      setFilteredProjects([]);
    } else {
      // No currentUser yet, or an error occurred in UserProvider
      setError('Failed to load user data, cannot fetch projects.');
      setProjects([]); // Clear projects if user data fails
      setFilteredProjects([]);
    }
    setIsLoading(false);
  }, [isCheckingAuth, currentUser, isUserLoading, enhanceProjects]);

  // WebSocket connections for deployment status updates
  const wsConnections = useRef<{ [key: string]: WebSocket }>({});

  // Function to check deployment status for a specific project
  const checkDeploymentStatus = async (projectId: string, forceUpdate: boolean = false) => {
    try {
      // If force update is requested, use HTTP polling directly
      if (forceUpdate) {
        await fallbackToHttpPolling(projectId, true);
        return;
      }

      // First, try to establish a WebSocket connection if not already connected
      if (!wsConnections.current[projectId]) {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const host = window.location.host;
        const wsUrl = `${protocol}//${host}/ws/deployment-status/${projectId}`;

        console.log(`Connecting to deployment status WebSocket for project ${projectId}: ${wsUrl}`);
        const ws = new WebSocket(wsUrl);

        ws.onopen = () => {
          console.log(`Deployment status WebSocket connected for project ${projectId}`);
        };

        ws.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);

            if (message.type === 'deployment_status_update' && message.data) {
              console.log(`Received deployment status update for project ${projectId}:`, message.data);
              const status = message.data.status || 'pending';

              // Update the project status in the state (updateProjectStatus will validate the status)
              updateProjectStatus(projectId, status);
            }
          } catch (err) {
            console.error(`Error parsing WebSocket message for project ${projectId}:`, err);
          }
        };

        ws.onerror = (error) => {
          console.error(`Deployment status WebSocket error for project ${projectId}:`, error);
          // Fall back to HTTP polling on error
          fallbackToHttpPolling(projectId);
        };

        ws.onclose = () => {
          console.log(`Deployment status WebSocket closed for project ${projectId}`);
          delete wsConnections.current[projectId];
          // Fall back to HTTP polling on close
          fallbackToHttpPolling(projectId);
        };

        wsConnections.current[projectId] = ws;
      }

      // Also do an immediate HTTP check for the current status
      fallbackToHttpPolling(projectId);
    } catch (error) {
      console.error(`Error setting up WebSocket for project ${projectId}:`, error);
      // Fall back to HTTP polling on error
      fallbackToHttpPolling(projectId);
    }
  };

  // Function to update project status in state and cache
  const updateProjectStatus = (projectId: string, status: string) => {
    // Ensure status is one of the allowed values
    let validStatus: 'pending' | 'in_progress' | 'completed' | 'failed' = 'pending';
    if (status === 'pending' || status === 'in_progress' || status === 'completed' || status === 'failed') {
      validStatus = status as 'pending' | 'in_progress' | 'completed' | 'failed';
    }

    // Update the project status in the state
    setProjects(prevProjects =>
      prevProjects.map(project =>
        project.id === projectId
          ? { ...project, deploymentStatus: validStatus }
          : project
      )
    );

    setFilteredProjects(prevProjects =>
      prevProjects.map(project =>
        project.id === projectId
          ? { ...project, deploymentStatus: validStatus }
          : project
      )
    );

    // Cache the status
    cacheProjectStatus(projectId, validStatus);

    // Update the cached user context
    const cachedUserContext = localStorage.getItem('userContext');
    if (cachedUserContext) {
      try {
        const userContext = JSON.parse(cachedUserContext);
        if (userContext.accessibleResources && userContext.accessibleResources.projects) {
          const updatedProjects = userContext.accessibleResources.projects.map((project: any) =>
            project.id === projectId
              ? { ...project, deploymentStatus: validStatus }
              : project
          );
          userContext.accessibleResources.projects = updatedProjects;
          localStorage.setItem('userContext', JSON.stringify(userContext));
        }
      } catch (e) {
        console.error('Error updating cached user context:', e);
      }
    }
  };

  // Fallback to HTTP polling if WebSocket fails
  const fallbackToHttpPolling = async (projectId: string, forceUpdate: boolean = false) => {
    try {
      // Use the enhanced fetchWithRetry with caching support
      const url = forceUpdate
        ? `/api/projects/${projectId}/deployment-status?force_update=true`
        : `/api/projects/${projectId}/deployment-status`;

      console.log(`Checking deployment status for project ${projectId}${forceUpdate ? ' (force update)' : ''}`);

      const response = await fetchWithRetry(url, {
        headers: {
          'Accept-Encoding': 'gzip, deflate' // Enable compression
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      const status = data.status || 'pending';

      console.log(`Received deployment status for project ${projectId}: ${status}`);

      // Update the project status (updateProjectStatus will validate the status)
      updateProjectStatus(projectId, status);
    } catch (error) {
      console.error(`Error checking deployment status for project ${projectId}:`, error);
    }
  };

  // Render deployment status with refresh button
  const renderDeploymentStatus = (project: Project) => {
    return (
      <div className={styles.deploymentStatus}>
        {renderDeploymentStatusIcon(project.deploymentStatus)}
        <IconButton
          iconProps={{ iconName: 'Refresh' }}
          title="Check deployment status"
          onClick={(e) => {
            e.stopPropagation();
            checkDeploymentStatus(project.id);
          }}
          className={styles.refreshButton}
        />
        {/* Add force update button for failed or pending statuses */}
        {(project.deploymentStatus === 'failed' || project.deploymentStatus === 'pending') && (
          <IconButton
            iconProps={{ iconName: 'Sync' }}
            title="Force update deployment status"
            onClick={(e) => {
              e.stopPropagation();
              fallbackToHttpPolling(project.id, true);
            }}
            className={styles.forceUpdateButton}
          />
        )}
      </div>
    );
  };


  // Filter projects based on search text and selected region
  useEffect(() => {
    let filtered = [...projects];

    // Apply search text filter
    if (searchText) {
      filtered = filtered.filter(project =>
        project.name.toLowerCase().includes(searchText.toLowerCase()) ||
        (project.description && project.description.toLowerCase().includes(searchText.toLowerCase()))
      );
    }

    // Apply region filter for Super Admin
    if (currentUser?.role === UserRole.SUPER_ADMIN && selectedRegion !== 'all') {
      filtered = filtered.filter(project => project.region === selectedRegion);
    }

    setFilteredProjects(filtered);
  }, [searchText, projects, selectedRegion, currentUser]);

  const handleProjectSelect = (project: Project) => {
    navigate(`/project/${project.id}`);
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLDivElement>, project: Project) => {
    // Stop propagation to prevent the card click from triggering
    event.stopPropagation();
    event.preventDefault();
    setMenuTarget(event.currentTarget);
    setSelectedProject(project);
  };

  const handleMenuDismiss = () => {
    setMenuTarget(null);
    // Don't clear selected project here, let the action handlers do it
  };

  const handleDeleteClick = () => {
    setIsDeleteDialogOpen(true);
  };

  // Handle project edit
  const handleEditProject = (project: Project) => {
    setSelectedProject(project);
    setEditedProjectName(project.name);
    setEditedProjectDescription(project.description || '');
    setIsEditDialogOpen(true);
  };

  // Handle project delete
  const handleDeleteProject = (project: Project) => {
    setSelectedProject(project);
    setIsDeleteDialogOpen(true);
  };

  // Handle save edit
  const handleSaveEdit = async () => {
    if (!selectedProject) return;

    try {
      // Only send the name field to update
      const projectData = {
        name: editedProjectName
      };

      console.log('Updating project with partial data:', projectData);

      // Call API to update project
      const response = await fetch(`/api/rbac/projects/${selectedProject.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(projectData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Failed to update project:', errorData);
        setError(`Failed to update project: ${errorData.error || response.statusText || 'Unknown error'}`);
        return;
      }

      const responseData = await response.json();
      console.log('Project updated successfully:', responseData);

      // Update the project in the local state
      const updatedProject = {
        ...selectedProject,
        name: editedProjectName
      };

      const updatedProjects = projects.map(p =>
        p.id === selectedProject.id ? updatedProject : p
      );

      setProjects(updatedProjects);
      setFilteredProjects(updatedProjects);
      setIsEditDialogOpen(false);
      setSelectedProject(null);
      setError(null);
    } catch (err) {
      console.error('Error updating project:', err);
      setError(err instanceof Error ? err.message : 'Failed to update project');
    }
  };

  const handleDeleteConfirm = async () => {
    if (!selectedProject) {
      return; // Should not happen now, but keep guard clause
    }

    console.log(`Starting deletion of project: ${selectedProject.id} (${selectedProject.name})`);
    setIsDeleting(true);
    try {
      console.log(`Calling projectDelete API for project ID: ${selectedProject.id}`);
      const response = await projectDelete(selectedProject.id);

      if (!response.ok) {
        const errorData = await response.json();
        console.error(`API error response:`, errorData);
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      // Log the successful response
      const responseData = await response.json();
      console.log(`Project deletion API response:`, responseData);

      // Remove the deleted project from the state
      setProjects(prevProjects => prevProjects.filter(p => p.id !== selectedProject.id));
      setFilteredProjects(prevProjects => prevProjects.filter(p => p.id !== selectedProject.id));
      setError(null);
      console.log(`Successfully removed project ${selectedProject.id} from UI state`);
    } catch (err) {
      console.error('Error deleting project:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete project');
    } finally {
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
      setSelectedProject(null);
      console.log(`Project deletion process completed`);
    }
  };

  const handleDeleteCancel = () => {
    setIsDeleteDialogOpen(false);
    setSelectedProject(null); // Clear project on cancel
  };

  const getMenuItems = (): IContextualMenuItem[] => {
    return [
      {
        key: 'openProject',
        text: 'Open project',
        disabled: true,
        onClick: () => {
          if (selectedProject) {
            handleProjectSelect(selectedProject);
          }
          handleMenuDismiss();
        }
      },
      {
        key: 'editProject',
        text: 'Edit project',
        disabled: true,
        onClick: () => {
          handleMenuDismiss();
        }
      },
      {
        key: 'assignUsers',
        text: 'Assign users',
        disabled: true,
        onClick: () => {
          handleMenuDismiss();
        }
      },
      {
        key: 'setCostLimit',
        text: 'Set cost limit',
        disabled: true,
        onClick: () => {
          handleMenuDismiss();
        }
      },
      {
        key: 'divider1',
        itemType: 1 // Divider
      },
      {
        key: 'deleteProject',
        text: 'Delete project',
        iconProps: { iconName: 'Delete' },
        onClick: handleDeleteClick
      }
    ];
  };

  const handleCreateNewProject = () => navigate('/new-project');

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
            return '';
        }
        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                           'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        return `${monthNames[date.getMonth()]} ${date.getDate()}, ${date.getFullYear()}`;
    } catch (e) {
        console.error("Error formatting date:", dateString, e);
        return '';
    }
  };

  // Render deployment status icon
  const renderDeploymentStatusIcon = (status?: 'pending' | 'in_progress' | 'completed' | 'failed') => {
    switch (status) {
      case 'completed':
        return <Icon iconName="CheckMark" className={styles.deploymentStatusCompleted} title="Deployment completed" />;
      case 'in_progress':
        return <Icon iconName="Sync" className={styles.deploymentStatusInProgress} title="Deployment in progress" />;
      case 'failed':
        return <Icon iconName="Error" className={styles.deploymentStatusFailed} title="Deployment failed" />;
      case 'pending':
        return <Icon iconName="Clock" className={styles.deploymentStatusPending} title="Deployment pending" />;
      default:
        return <Spinner size={SpinnerSize.small} className={styles.deploymentStatusLoading} title="Loading status..." />;
    }
  };


  if (isCheckingAuth) {
    return (
      <div className={styles.container}>
        <Stack horizontalAlign="center" verticalAlign="center" style={{ height: '100%' }}>
          <Spinner size={SpinnerSize.large} label="Checking authentication..." />
        </Stack>
      </div>
    );
  }

  const createButtonStyles: IButtonStyles = {
    root: {
      borderRadius: '16px',
      height: '36px',
      padding: '0 16px',
      backgroundColor: '#f0f0f0',
      border: 'none',
    },
    rootHovered: { backgroundColor: '#e0e0e0' },
    label: { fontWeight: 'normal' }
  };

  return (
    <div className={styles.container}>
      <RoleBasedHeader logo={logo} title="AI Scope Project Management" />

      <div className={styles.welcomeContainer}>
        <h1 className={styles.welcomeTitle}>Welcome to AI scope project management</h1>
      </div>

      <div className={styles.contentContainer}>
        {showNewProjectBanner && (
          <MessageBar
            messageBarType={MessageBarType.info}
            isMultiline={false}
            onDismiss={() => setShowNewProjectBanner(false)}
            dismissButtonAriaLabel="Close"
          >
            Project "{newProjectName}" is being created. This process may take up to 20 minutes.
          </MessageBar>
        )}

        {error && (
          <MessageBar messageBarType={MessageBarType.error} onDismiss={() => setError(null)}>
            {error}
          </MessageBar>
        )}

        <div className={styles.projectsSection}>
          <div className={styles.sectionHeader}>
            <h2 className={styles.sectionTitle}>
              {currentUser?.role === UserRole.SUPER_ADMIN ? 'All Projects' :
               currentUser?.role === UserRole.REGIONAL_ADMIN ?
                 `${currentUser.region ? (regionMap[currentUser.region] || currentUser.region) : ''} Projects` :
               'My Projects'}
            </h2>
            <div className={styles.sectionActions}>
              {/* Only show Create New button for Super Admin and Regional Admin */}
              {viewPermissions.canCreateProject && (
                <DefaultButton
                  text="Create new"
                  onClick={handleCreateNewProject}
                  iconProps={{ iconName: 'Add' }}
                  styles={createButtonStyles}
                />
              )}

              {/* Region filter dropdown for Super Admin */}
              {currentUser?.role === UserRole.SUPER_ADMIN && (
                <CommandButton
                  text={selectedRegion === 'all' ? 'All Regions' : `Region: ${regionMap[selectedRegion] || selectedRegion}`}
                  iconProps={{ iconName: 'Filter' }}
                  menuProps={{
                    items: [
                      { key: 'all', text: 'All Regions' },
                      ...Object.entries(regionMap).map(([id, name]) => ({ key: id, text: name }))
                    ],
                    onItemClick: (_, item) => item && setSelectedRegion(item.key as string)
                  }}
                  styles={{
                    root: { marginLeft: 8 }
                  }}
                />
              )}
            </div>
          </div>

          <div className={styles.toolbarContainer}>
            {/* Add Search Bar Here if needed */}
            {/* <TextField placeholder="Search projects..." className={styles.searchBar} onChange={(_, newValue) => setSearchText(newValue || '')} /> */}
            <div className={styles.viewToggleContainer}>
              <IconButton
                iconProps={{ iconName: 'GridViewMedium' }}
                onClick={() => setViewType('grid')}
                className={viewType === 'grid' ? styles.viewButtonActive : styles.viewButton}
                title="Grid view"
              />
              <IconButton
                iconProps={{ iconName: 'BulletedList' }}
                onClick={() => setViewType('list')}
                className={viewType === 'list' ? styles.viewButtonActive : styles.viewButton}
                title="List view"
              />
            </div>
          </div>

          {isLoading ? (
            <div className={styles.loadingContainer}>
              <Spinner size={SpinnerSize.large} label="Loading projects..." />
            </div>
          ) : filteredProjects.length === 0 && !error ? (
             <div className={styles.noProjects}>
               <Text>No projects found. Click "Create new" to get started.</Text>
             </div>
           ) : (
             <div className={viewType === 'grid' ? styles.projectsGrid : styles.projectsList}>
               {filteredProjects.map(project => (
                 <div
                   key={project.id}
                   className={`${styles.projectCard} ${(project.deploymentStatus === 'pending' || project.deploymentStatus === 'in_progress') ? styles.projectCardDeploying : ''}`}
                   style={{ background: project.color }}
                   role="button" // Added for accessibility
                   tabIndex={(project.deploymentStatus === 'pending' || project.deploymentStatus === 'in_progress') ? -1 : 0} // Disable tabbing for deploying projects
                 >
                   <div
                     className={styles.projectCardContent}
                     onClick={() => {
                       // Only allow clicking if project is not deploying
                       if (project.deploymentStatus !== 'pending' && project.deploymentStatus !== 'in_progress') {
                         handleProjectSelect(project);
                       }
                     }}
                     onKeyPress={(e) => {
                       // Only allow keyboard navigation if project is not deploying
                       if (project.deploymentStatus !== 'pending' && project.deploymentStatus !== 'in_progress' &&
                           (e.key === 'Enter' || e.key === ' ')) {
                         handleProjectSelect(project);
                       }
                     }} // Added for accessibility
                   >
                     <div className={styles.projectIcon}>{project.icon}</div>
                     <div className={styles.projectMenuButton} onClick={(e) => e.stopPropagation()}>
                       <ProjectActionsMenu
                         projectId={project.id}
                         projectName={project.name}
                         onEdit={() => handleEditProject(project)}
                         onDelete={() => handleDeleteProject(project)}
                       />
                     </div>
                     <h3 className={styles.projectName}>{project.name}</h3>

                     {/* Add deployment progress bar */}
                     {project.deploymentStatus && project.deploymentStatus !== 'completed' && (
                       <div className={styles.deploymentProgressBar}>
                         <ProgressIndicator
                           percentComplete={(project.deploymentProgress || 0) / 100}
                           barHeight={8}
                           className={project.deploymentStatus === 'failed' ? styles.failedProgressBar : styles.inProgressProgressBar}
                         />
                       </div>
                     )}

                     {/* Only show metadata if project is completed or no deployment status */}
                     {(!project.deploymentStatus || project.deploymentStatus === 'completed') ? (
                       <div className={styles.projectMeta}>
                         {/* Display description if available */}
                         {project.description && <Text className={styles.projectDescription} block>{project.description}</Text>}
                         {/* Add deployment status indicator */}
                         {renderDeploymentStatus(project)}
                         {formatDate(project.updated_at) && <Text variant="small">Updated: {formatDate(project.updated_at)}</Text>}
                         {project.teams && project.teams.length > 0 && (
                           <Text variant="small" styles={{ root: { marginTop: 4 } }}>
                             Teams: {project.teams.map(teamId => {
                               const team = mockTeams.find(t => t.id === teamId);
                               return team ? team.name : 'Unknown';
                             }).join(', ')}
                           </Text>
                         )}
                         {project.region && (
                           <Text variant="small" styles={{ root: { marginTop: 4 } }}>
                             Region: {regionMap[project.region] || project.region}
                           </Text>
                         )}
                       </div>
                     ) : (
                       // For projects that are still deploying or failed, show minimal info
                       <div className={styles.projectMeta}>
                         {/* Only show status indicator */}
                         {renderDeploymentStatus(project)}
                         {project.deploymentStatus === 'failed' && (
                           <Text variant="small" className={styles.failedText}>
                             Deployment failed
                           </Text>
                         )}
                         {project.deploymentStatus === 'in_progress' && (
                           <Text variant="small" className={styles.inProgressText}>
                             Deploying... ({project.deploymentProgress || 0}%)
                           </Text>
                         )}
                       </div>
                     )}
                   </div>
                 </div>
               ))}
             </div>
           )
          }
        </div>
      </div>

      {/* Project Options Menu */}
      {menuTarget && (
        <ContextualMenu
          items={getMenuItems()}
          hidden={!menuTarget}
          target={menuTarget}
          onDismiss={handleMenuDismiss}
          directionalHint={DirectionalHint.bottomRightEdge}
        />
      )}

      {/* Edit Project Dialog */}
      <Dialog
        hidden={!isEditDialogOpen}
        onDismiss={() => setIsEditDialogOpen(false)}
        dialogContentProps={{
          type: DialogType.normal,
          title: 'Edit Project',
          subText: 'Update project details'
        }}
        modalProps={{
          isBlocking: true,
          styles: { main: { maxWidth: 450 } }
        }}
      >
        <Stack tokens={{ childrenGap: 15 }}>
          <TextField
            label="Project Name"
            value={editedProjectName}
            onChange={(_, value) => setEditedProjectName(value || '')}
            required
          />
        </Stack>
        <DialogFooter>
          <PrimaryButton onClick={handleSaveEdit} text="Save" />
          <DefaultButton onClick={() => setIsEditDialogOpen(false)} text="Cancel" />
        </DialogFooter>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        hidden={!isDeleteDialogOpen}
        onDismiss={handleDeleteCancel}
        dialogContentProps={{
          type: DialogType.normal,
          title: 'Delete Project',
          subText: selectedProject ?
            `Are you sure you want to delete "${selectedProject.name}"? This will permanently delete all associated Azure resources including storage containers, search indexes, and function apps.` :
            'Are you sure you want to delete this project?'
        }}
        modalProps={{
          isBlocking: true,
          styles: { main: { maxWidth: 450 } }
        }}
      >
        <DialogFooter>
          {/* Removed inline logs, back to direct call */}
          <PrimaryButton onClick={handleDeleteConfirm} text="Delete" disabled={isDeleting} />
          <DefaultButton onClick={handleDeleteCancel} text="Cancel" />
        </DialogFooter>
      </Dialog>
    </div>
  );
};

export default ProjectSelector;
