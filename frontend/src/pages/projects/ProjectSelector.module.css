.container {
  display: flex;
  flex-direction: column;
  padding: 0;
  margin: 0;
  min-height: 100vh;
  background-color: #f9f9f9;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Header styles */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 24px;
  background-color: white;
  border-bottom: 1px solid #eaeaea;
  height: 60px;
}

.appTitle {
  display: flex;
  align-items: center;
}

.logoContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.logoText {
  font-size: 16px;
  margin-right: 2px;
  background: linear-gradient(135deg, #4285f4, #34a853, #fbbc05, #ea4335);
  border-radius: 8px;
  padding: 4px 8px;
  color: white;
  font-weight: bold;
}

.logoPlus {
  font-size: 12px;
  background-color: #673ab7;
  padding: 2px 6px;
  border-radius: 4px;
}

.appName {
  font-size: 18px;
  font-weight: 500;
  margin: 0;
}

.headerActions {
  display: flex;
  align-items: center;
}

.settingsIcon {
  margin-right: 16px;
  color: #5f6368;
}

.userIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #f1f3f4;
  cursor: pointer;
}

/* Welcome section */
.welcomeContainer {
  padding: 48px 64px 24px;
}

.welcomeTitle {
  font-size: 40px;
  font-weight: 500;
  margin: 0;
  color: #202124;
}

.plusText {
  color: #4285f4;
}

/* Content container */
.contentContainer {
  padding: 0 64px 64px;
  flex: 1;
}

.errorBar {
  margin-bottom: 16px;
}

/* Projects section */
.projectsSection {
  margin-top: 24px;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.sectionTitle {
  font-size: 22px;
  font-weight: 500;
  margin: 0;
  color: #202124;
}

.sectionActions {
  display: flex;
  align-items: center;
}

/* Toolbar */
.toolbarContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.viewToggleContainer {
  display: flex;
  background-color: #f1f3f4;
  border-radius: 4px;
  overflow: hidden;
}

.viewButton, .viewButtonActive {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background-color: transparent;
  cursor: pointer;
}

.viewButtonActive {
  background-color: #e8eaed;
}

.sortButton {
  background-color: #f1f3f4;
  border: none;
  border-radius: 4px;
  height: 40px;
  padding: 0 16px;
}

/* Project grid */
.projectsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 16px;
}

.projectsList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Project card */
.projectCard {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
  height: 180px;
  position: relative;
}

.projectCard:hover:not(.projectCardDeploying) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.projectCardDeploying {
  cursor: default;
  opacity: 0.9;
}

.projectCardContent {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.projectIcon {
  font-size: 24px;
  margin-bottom: 12px;
}

.projectMenuButton {
  position: absolute;
  top: 8px;
  right: 8px;
  background: transparent;
  color: #5f6368;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;

  opacity: 0;
  transition: opacity 0.2s ease-in-out, background-color 0.2s ease-in-out;
  z-index: 10;
  border-radius: 50%;
}

.projectCard:hover .projectMenuButton {
  opacity: 0.9;
}

.projectMenuButton:hover {
  background-color: rgba(255, 255, 255, 0.2);
  opacity: 1;
}

.projectName {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 8px;
  color: #202124;
  /* Allow for two lines of text with ellipsis */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.projectMeta {
  margin-top: auto;
  font-size: 12px;
  color: #5f6368;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.deploymentStatus {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
  gap: 4px;
}

.refreshButton, .forceUpdateButton {
  font-size: 12px;
  padding: 2px;
  margin-left: 4px;
}

.forceUpdateButton {
  color: #0078D4;
}

.deploymentStatusCompleted {
  color: #107C10;
  font-size: 16px;
}

.deploymentStatusInProgress {
  color: #0078D4;
  font-size: 16px;
  animation: spin 1.5s linear infinite;
}

.deploymentStatusFailed {
  color: #D13438;
  font-size: 16px;
}

.deploymentStatusPending {
  color: #797775;
  font-size: 16px;
}

.deploymentStatusLoading {
  color: #0078D4;
  width: 16px;
  height: 16px;
}

.deploymentProgressBar {
  margin: 12px 0;
  width: 100%;
}

.inProgressProgressBar {
  color: #0078D4;
}

.failedProgressBar {
  color: #D13438;
}

.inProgressText {
  color: #0078D4;
  font-weight: 500;
}

.failedText {
  color: #D13438;
  font-weight: 500;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Loading state */
.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .container {
    padding: 0;
  }

  .welcomeContainer {
    padding: 24px 16px 12px;
  }

  .welcomeTitle {
    font-size: 28px;
  }

  .contentContainer {
    padding: 0 16px 32px;
  }

  .projectsGrid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 12px;
  }

  .projectCard {
    height: 160px;
  }
}

/* Update the logoImage style to be more flexible */
.logoImage {
  height: 40px;
  width: auto;
  max-width: 100%;
  object-fit: contain;
}