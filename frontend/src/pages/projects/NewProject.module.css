.container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 40px;
  background-color: #f5f5f5;
  max-width: 500px;
  margin: 0 auto;
}

.formContainer {
  width: 100%;
  max-width: 600px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.header {
  margin-bottom: 32px;
}

.title {
  font-size: 24px;
  margin-bottom: 8px;
  color: #333;
}

.subtitle {
  font-size: 16px;
  color: #666;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.formGroup label {
  font-size: 14px;
  font-weight: 500;
  color: #555;
}

.formGroup input,
.formGroup textarea {
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  transition: border-color 0.2s;
}

.formGroup input:focus,
.formGroup textarea:focus {
  outline: none;
  border-color: #0078d4;
}

.formGroup textarea {
  resize: vertical;
  min-height: 100px;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 16px;
}

.cancelButton {
  padding: 12px 24px;
  background-color: transparent;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancelButton:hover {
  background-color: #f5f5f5;
}

.createButton {
  padding: 12px 24px;
  background-color: #0078d4;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.createButton:hover {
  background-color: #106ebe;
}

.createButton:disabled,
.cancelButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.errorMessage {
  background-color: #fde7e9;
  border-left: 4px solid #d13438;
  color: #d13438;
  padding: 12px 16px;
  margin-bottom: 20px;
  border-radius: 4px;
  font-size: 14px;
}

/* Environment variables section removed */

.actionButtons {
  margin-top: 24px;
}

/* Deployment status styles */
.deploymentTitle {
  font-weight: 600;
  margin-bottom: 8px;
}

.overallProgress {
  margin: 16px 0;
}

.resourceList {
  margin-top: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
}

.resourceItem {
  padding: 12px 0;
  border-bottom: 1px solid #eaeaea;
}

.resourceItem:last-child {
  border-bottom: none;
}

.resourceStatus {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  margin-right: 16px;
}

.resourceInfo {
  flex: 1;
}

.resourceName {
  font-weight: 500;
  margin-bottom: 4px;
}

.resourceStatusText {
  font-size: 12px;
  color: #666;
}

.resourceDetails {
  font-size: 11px;
  color: #777;
  margin-top: 4px;
  font-style: italic;
  line-height: 1.3;
}

/* Status icons */
.completedIcon {
  color: #107C10;
  font-size: 20px;
}

.inProgressIcon {
  color: #0078D4;
  font-size: 20px;
  animation: spin 1.5s linear infinite;
}

.failedIcon {
  color: #D13438;
  font-size: 20px;
}

.pendingIcon {
  color: #797775;
  font-size: 20px;
}

/* Resource item status styles */
.resourceItem.completed {
  background-color: rgba(16, 124, 16, 0.05);
}

.resourceItem.in_progress {
  background-color: rgba(0, 120, 212, 0.05);
}

.resourceItem.failed {
  background-color: rgba(209, 52, 56, 0.05);
}

.deploymentFooter {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.deploymentNote {
  font-size: 14px;
  color: #666;
  text-align: center;
}

.errorNote {
  color: #D13438;
  font-weight: 500;
}

.supportButton {
  width: 200px;
}

.detailedError {
  margin-top: 16px;
  width: 100%;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 16px;
  background-color: #fdf6f6;
}

.detailedErrorTitle {
  margin-bottom: 8px;
  color: #D13438;
  font-weight: 600;
}

.errorDetails {
  max-height: 200px;
  overflow-y: auto;
  background-color: #f8f8f8;
  padding: 12px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-word;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
}