// frontend/src/pages/chat/Chat.tsx
import { useRef, useState, useEffect, useContext, useLayoutEffect } from 'react' // Removed useMemo as it wasn't used
import { CommandBarButton, IconButton, Dialog, DialogType, Stack } from '@fluentui/react'
import { SquareRegular, ShieldLockRegular, ErrorCircleRegular } from '@fluentui/react-icons'
import { MainPriorityPlotButton, PriorityPlotModal } from '../../components/PriorityPlot'

import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeRaw from 'rehype-raw'
import uuid from 'react-uuid'
import { isEmpty } from 'lodash'
import DOMPurify from 'dompurify'

import styles from './Chat.module.css'
import CompanyLogo from '../../assets/keyrus-2.svg'
import { XSSAllowTags } from '../../constants/sanatizeAllowables'

import {
  ChatMessage,
  ConversationRequest,
  conversationApi,
  Citation,
  ToolMessageContent,
  AzureSqlServerExecResults,
  ChatResponse,
  getUserInfo,
  Conversation,
  historyGenerate,
  historyUpdate,
  historyClear,
  ChatHistoryLoadingState,
  CosmosDBStatus,
  ErrorMessage,
  ExecResults
} from '../../api'
import { Answer } from '../../components/Answer'
import { QuestionInput } from '../../components/QuestionInput'
import { ChatHistoryPanel } from '../../components/ChatHistory/ChatHistoryPanel'
import { AppStateContext } from '../../state/AppProvider'
import { ProjectContext } from '../layout/ProjectLayout' // Import ProjectContext
import { useBoolean } from '@fluentui/react-hooks'
import FileManagement from '../../components/FileManagement/FileManagement'
import { BlobServiceClient } from '@azure/storage-blob'
import RightFileManagement from '../../components/FileManagement/RightFileManagement'
import { getStorageConfig, StorageConfig } from '../../services/configService'

const enum messageStatus {
  NotRunning = 'Not Running',
  Processing = 'Processing',
  Done = 'Done'
}

const Chat = () => {
  const appStateContext = useContext(AppStateContext)
  const projectContext = useContext(ProjectContext); // Get project context
  const projectId = projectContext?.projectId; // Extract projectId

  if (!appStateContext) {
    return null // or some loading/error state
  }
  const { state } = appStateContext

  // Storage config state
  const [storageConfig, setStorageConfig] = useState<StorageConfig>({
    account_name: '',
    container_name: '',
    container_sas_token: ''
  })

  // Fetch storage config
  useEffect(() => {
    const fetchStorageConfig = async () => {
      try {
        const config = await getStorageConfig()
        console.log('Storage config fetched:', {
          hasConfig: Boolean(config),
          hasAccountName: Boolean(config?.account_name),
          hasContainerName: Boolean(config?.container_name),
          hasSasToken: Boolean(config?.container_sas_token)
        })

        if (!config?.account_name || !config?.container_name || !config?.container_sas_token) {
          console.warn('Incomplete storage configuration:', {
            accountName: config?.account_name || '[missing]',
            containerName: config?.container_name || '[missing]',
            hasToken: Boolean(config?.container_sas_token)
          })
        }

        setStorageConfig(config)
      } catch (error) {
        console.error('Error fetching storage config:', error)
        // Keep default empty config
      }
    }
    fetchStorageConfig()
  }, [])

  // Add debug logging for Azure Storage settings
  useEffect(() => {
    console.log('Storage configuration state:', {
      hasStorageConfig: Boolean(storageConfig),
      accountName: storageConfig?.account_name || '[empty]',
      containerName: storageConfig?.container_name || '[empty]',
      hasSasToken: Boolean(storageConfig?.container_sas_token)
    })
  }, [storageConfig])

  const sasToken = storageConfig?.container_sas_token || ''
  const storageAccountName = storageConfig?.account_name || ''

  // Add debug logging for Azure Storage settings
  useEffect(() => {
    console.log('Frontend settings state changed:', {
      hasSettings: Boolean(state.frontendSettings),
      hasAzureStorage: Boolean(state.frontendSettings?.azure_storage),
      azureStorage: state.frontendSettings?.azure_storage
    })
  }, [state.frontendSettings])

  const ui = state.frontendSettings?.ui
  const AUTH_ENABLED = state.frontendSettings?.auth_enabled
  const chatMessageStreamEnd = useRef<HTMLDivElement | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [showLoadingMessage, setShowLoadingMessage] = useState<boolean>(false)
  const [activeCitation, setActiveCitation] = useState<Citation>()
  const [isCitationPanelOpen, setIsCitationPanelOpen] = useState<boolean>(false)
  const [isIntentsPanelOpen, setIsIntentsPanelOpen] = useState<boolean>(false)
  const abortFuncs = useRef([] as AbortController[])
  const [showAuthMessage, setShowAuthMessage] = useState<boolean | undefined>()
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [execResults, setExecResults] = useState<ExecResults[]>([])
  const [processMessages, setProcessMessages] = useState<messageStatus>(messageStatus.NotRunning)
  const [clearingChat, setClearingChat] = useState<boolean>(false)
  const [hideErrorDialog, { toggle: toggleErrorDialog }] = useBoolean(true)
  const [errorMsg, setErrorMsg] = useState<ErrorMessage | null>()
  const [logo, setLogo] = useState('')
  const [answerId, setAnswerId] = useState<string>('')
  const [isBannerVisible, setIsBannerVisible] = useState<boolean>(true) // Add state for banner visibility
  const [isFileManagementOpen, setIsFileManagementOpen] = useState(true)
  const [isRightPanelOpen, setIsRightPanelOpen] = useState(true)
  const [leftPanelStateBeforeCitation, setLeftPanelStateBeforeCitation] = useState<boolean>(true)
  const [isPriorityPlotModalOpen, setIsPriorityPlotModalOpen] = useState(false)

  // Add console logs to track state changes
  useEffect(() => {
    console.log('isBannerVisible:', isBannerVisible)
  }, [isBannerVisible])

  // In Chat.tsx, add console logs to track state changes
  useEffect(() => {
    console.log('Chat Component States:', {
      isLoading,
      showLoadingMessage,
      processMessages,
      isBannerVisible
    })
  }, [isLoading, showLoadingMessage, processMessages, isBannerVisible])

  const errorDialogContentProps = {
    type: DialogType.close,
    title: errorMsg?.title,
    closeButtonAriaLabel: 'Close',
    subText: errorMsg?.subtitle
  }

  const modalProps = {
    titleAriaId: 'labelId',
    subtitleAriaId: 'subTextId',
    isBlocking: true,
    styles: { main: { maxWidth: 450 } }
  }

  const [ASSISTANT, TOOL, ERROR] = ['assistant', 'tool', 'error']
  const NO_CONTENT_ERROR = 'No content in messages object.'

  useEffect(() => {
    if (
      state.isCosmosDBAvailable?.status !== CosmosDBStatus.Working &&
      state.isCosmosDBAvailable?.status !== CosmosDBStatus.NotConfigured &&
      state.chatHistoryLoadingState === ChatHistoryLoadingState.Fail &&
      hideErrorDialog
    ) {
      let subtitle = `${state.isCosmosDBAvailable.status}. Please contact the site administrator.`
      setErrorMsg({
        title: 'Chat history is not enabled',
        subtitle: subtitle
      })
      toggleErrorDialog()
    }
  }, [state.isCosmosDBAvailable])

  const handleErrorDialogClose = () => {
    toggleErrorDialog()
    setTimeout(() => {
      setErrorMsg(null)
    }, 500)
  }

  useEffect(() => {
    if (!state.isLoading) {
      setLogo(ui?.chat_logo || ui?.logo || CompanyLogo)
    }
  }, [state.isLoading])

  useEffect(() => {
    setIsLoading(state.chatHistoryLoadingState === ChatHistoryLoadingState.Loading)
  }, [state.chatHistoryLoadingState])

  const getUserInfoList = async () => {
    if (!AUTH_ENABLED) {
      setShowAuthMessage(false)
      return
    }
    const userInfoList = await getUserInfo()
    if (userInfoList.length === 0 && window.location.hostname !== '127.0.0.1') {
      setShowAuthMessage(true)
    } else {
      setShowAuthMessage(false)
    }
  }

  let assistantMessage = {} as ChatMessage
  let toolMessage = {} as ChatMessage
  let assistantContent = ''

  useEffect(() => parseExecResults(execResults), [execResults])

  const parseExecResults = (exec_results_: any): void => {
    if (exec_results_ == undefined) return
    const exec_results = exec_results_.length === 2 ? exec_results_ : exec_results_.splice(2)
    appStateContext?.dispatch({
      type: 'SET_ANSWER_EXEC_RESULT',
      payload: { answerId: answerId, exec_result: exec_results }
    })
  }

  const processResultMessage = (resultMessage: ChatMessage, userMessage: ChatMessage, conversationId?: string) => {
    if (resultMessage.content.includes('all_exec_results')) {
      const parsedExecResults = JSON.parse(resultMessage.content) as AzureSqlServerExecResults
      setExecResults(parsedExecResults.all_exec_results)
      assistantMessage.context = JSON.stringify({
        all_exec_results: parsedExecResults.all_exec_results
      })
    }

    if (resultMessage.role === ASSISTANT) {
      setAnswerId(resultMessage.id)
      assistantContent += resultMessage.content
      assistantMessage = { ...assistantMessage, ...resultMessage }
      assistantMessage.content = assistantContent

      if (resultMessage.context) {
        toolMessage = {
          id: uuid(),
          role: TOOL,
          content: resultMessage.context,
          date: new Date().toISOString()
        }
      }
    }

    if (resultMessage.role === TOOL) toolMessage = resultMessage

    if (!conversationId) {
      isEmpty(toolMessage)
        ? setMessages([...messages, userMessage, assistantMessage])
        : setMessages([...messages, userMessage, toolMessage, assistantMessage])
    } else {
      isEmpty(toolMessage)
        ? setMessages([...messages, assistantMessage])
        : setMessages([...messages, toolMessage, assistantMessage])
    }
  }

  const makeApiRequestWithoutCosmosDB = async (question: string, conversationId?: string) => {
    setIsLoading(true)
    setShowLoadingMessage(true)
    //setIsBannerVisible(false); // Hide banner when loading starts
    const abortController = new AbortController()
    abortFuncs.current.unshift(abortController)

    const userMessage: ChatMessage = {
      id: uuid(),
      role: 'user',
      content: question,
      date: new Date().toISOString()
    }

    let conversation: Conversation | null | undefined
    if (!conversationId) {
      conversation = {
        id: conversationId ?? uuid(),
        title: question,
        messages: [userMessage],
        date: new Date().toISOString()
      }
    } else {
      conversation = state?.currentChat
      if (!conversation) {
        console.error('Conversation not found.')
        setIsLoading(false)
        setShowLoadingMessage(false)
        abortFuncs.current = abortFuncs.current.filter(a => a !== abortController)
        return
      } else {
        conversation.messages.push(userMessage)
      }
    }

    appStateContext?.dispatch({ type: 'UPDATE_CURRENT_CHAT', payload: conversation })
    setMessages(conversation.messages)

    const request: ConversationRequest = {
      messages: [...conversation.messages.filter(answer => answer.role !== ERROR)]
    }

    let result = {} as ChatResponse
    try {
      // Pass projectId to conversationApi
      const response = await conversationApi(request, abortController.signal, projectId)
      if (response?.body) {
        const reader = response.body.getReader()

        let runningText = ''
        while (true) {
          setProcessMessages(messageStatus.Processing)
          const { done, value } = await reader.read()
          if (done) break

          var text = new TextDecoder('utf-8').decode(value)
          const objects = text.split('\n')
          objects.forEach(obj => {
            try {
              if (obj !== '' && obj !== '{}') {
                runningText += obj
                result = JSON.parse(runningText)
                if (result.choices?.length > 0) {
                  result.choices[0].messages.forEach(msg => {
                    msg.id = result.id
                    msg.date = new Date().toISOString()
                  })
                  if (result.choices[0].messages?.some(m => m.role === ASSISTANT)) {
                    setShowLoadingMessage(false)
                  }
                  result.choices[0].messages.forEach(resultObj => {
                    processResultMessage(resultObj, userMessage, conversationId)
                  })
                } else if (result.error) {
                  throw Error(result.error)
                }
                runningText = ''
              }
            } catch (e) {
              if (!(e instanceof SyntaxError)) {
                console.error(e)
                throw e
              } else {
                console.log('Incomplete message. Continuing...')
              }
            }
          })
        }
        conversation.messages.push(toolMessage, assistantMessage)
        appStateContext?.dispatch({ type: 'UPDATE_CURRENT_CHAT', payload: conversation })
        setMessages([...messages, toolMessage, assistantMessage])
      }
    } catch (e) {
      if (!abortController.signal.aborted) {
        let errorMessage =
          'An error occurred. Please try again. If the problem persists, please contact the site administrator.'
        if (result.error?.message) {
          errorMessage = result.error.message
        } else if (typeof result.error === 'string') {
          errorMessage = result.error
        }

        errorMessage = parseErrorMessage(errorMessage)

        let errorChatMsg: ChatMessage = {
          id: uuid(),
          role: ERROR,
          content: errorMessage,
          date: new Date().toISOString()
        }
        conversation.messages.push(errorChatMsg)
        appStateContext?.dispatch({ type: 'UPDATE_CURRENT_CHAT', payload: conversation })
        setMessages([...messages, errorChatMsg])
      } else {
        setMessages([...messages, userMessage])
      }
    } finally {
      setIsLoading(false)
      setShowLoadingMessage(false)
      //setIsBannerVisible(true); // Show banner when loading ends
      abortFuncs.current = abortFuncs.current.filter(a => a !== abortController)
      setProcessMessages(messageStatus.Done)
    }

    return abortController.abort()
  }

  const makeApiRequestWithCosmosDB = async (question: string, conversationId?: string) => {
    setIsLoading(true)
    setShowLoadingMessage(true)
    const abortController = new AbortController()
    abortFuncs.current.unshift(abortController)

    const userMessage: ChatMessage = {
      id: uuid(),
      role: 'user',
      content: question,
      date: new Date().toISOString()
    }

    //api call params set here (generate)
    let request: ConversationRequest
    let conversation
    if (conversationId) {
      conversation = state?.chatHistory?.find(conv => conv.id === conversationId)
      if (!conversation) {
        console.error('Conversation not found.')
        setIsLoading(false)
        setShowLoadingMessage(false)
        abortFuncs.current = abortFuncs.current.filter(a => a !== abortController)
        return
      } else {
        conversation.messages.push(userMessage)
        request = {
          messages: [...conversation.messages.filter(answer => answer.role !== ERROR)]
        }
      }
    } else {
      request = {
        messages: [userMessage].filter(answer => answer.role !== ERROR)
      }
      setMessages(request.messages)
    }
    let result = {} as ChatResponse
    var errorResponseMessage = 'Please try again. If the problem persists, please contact the site administrator.'
    try {
      // Pass projectId to historyGenerate
      const response = conversationId
        ? await historyGenerate(request, abortController.signal, projectId, conversationId)
        : await historyGenerate(request, abortController.signal, projectId)
      if (!response?.ok) {
        const responseJson = await response.json()
        errorResponseMessage =
          responseJson.error === undefined ? errorResponseMessage : parseErrorMessage(responseJson.error)
        let errorChatMsg: ChatMessage = {
          id: uuid(),
          role: ERROR,
          content: `There was an error generating a response. Chat history can't be saved at this time. ${errorResponseMessage}`,
          date: new Date().toISOString()
        }
        let resultConversation
        if (conversationId) {
          resultConversation = state?.chatHistory?.find(conv => conv.id === conversationId)
          if (!resultConversation) {
            console.error('Conversation not found.')
            setIsLoading(false)
            setShowLoadingMessage(false)
            abortFuncs.current = abortFuncs.current.filter(a => a !== abortController)
            return
          }
          resultConversation.messages.push(errorChatMsg)
        } else {
          setMessages([...messages, userMessage, errorChatMsg])
          setIsLoading(false)
          setShowLoadingMessage(false)
          abortFuncs.current = abortFuncs.current.filter(a => a !== abortController)
          return
        }
        appStateContext?.dispatch({ type: 'UPDATE_CURRENT_CHAT', payload: resultConversation })
        setMessages([...resultConversation.messages])
        return
      }
      if (response?.body) {
        const reader = response.body.getReader()

        let runningText = ''
        while (true) {
          setProcessMessages(messageStatus.Processing)
          const { done, value } = await reader.read()
          if (done) break

          var text = new TextDecoder('utf-8').decode(value)
          const objects = text.split('\n')
          objects.forEach(obj => {
            try {
              if (obj !== '' && obj !== '{}') {
                runningText += obj
                result = JSON.parse(runningText)
                if (!result.choices?.[0]?.messages?.[0].content) {
                  errorResponseMessage = NO_CONTENT_ERROR
                  throw Error()
                }
                if (result.choices?.length > 0) {
                  result.choices[0].messages.forEach(msg => {
                    msg.id = result.id
                    msg.date = new Date().toISOString()
                  })
                  if (result.choices[0].messages?.some(m => m.role === ASSISTANT)) {
                    setShowLoadingMessage(false)
                  }
                  result.choices[0].messages.forEach(resultObj => {
                    processResultMessage(resultObj, userMessage, conversationId)
                  })
                }
                runningText = ''
              } else if (result.error) {
                throw Error(result.error)
              }
            } catch (e) {
              if (!(e instanceof SyntaxError)) {
                console.error(e)
                throw e
              } else {
                console.log('Incomplete message. Continuing...')
              }
            }
          })
        }

        let resultConversation
        if (conversationId) {
          resultConversation = state?.chatHistory?.find(conv => conv.id === conversationId)
          if (!resultConversation) {
            console.error('Conversation not found.')
            setIsLoading(false)
            setShowLoadingMessage(false)
            abortFuncs.current = abortFuncs.current.filter(a => a !== abortController)
            return
          }
          isEmpty(toolMessage)
            ? resultConversation.messages.push(assistantMessage)
            : resultConversation.messages.push(toolMessage, assistantMessage)
        } else {
          resultConversation = {
            id: result.history_metadata.conversation_id,
            title: result.history_metadata.title,
            messages: [userMessage],
            date: result.history_metadata.date
          }
          isEmpty(toolMessage)
            ? resultConversation.messages.push(assistantMessage)
            : resultConversation.messages.push(toolMessage, assistantMessage)
        }
        if (!resultConversation) {
          setIsLoading(false)
          setShowLoadingMessage(false)
          abortFuncs.current = abortFuncs.current.filter(a => a !== abortController)
          return
        }
        appStateContext?.dispatch({ type: 'UPDATE_CURRENT_CHAT', payload: resultConversation })
        isEmpty(toolMessage)
          ? setMessages([...messages, assistantMessage])
          : setMessages([...messages, toolMessage, assistantMessage])
      }
    } catch (e) {
      if (!abortController.signal.aborted) {
        let errorMessage = `An error occurred. ${errorResponseMessage}`
        if (result.error?.message) {
          errorMessage = result.error.message
        } else if (typeof result.error === 'string') {
          errorMessage = result.error
        }

        errorMessage = parseErrorMessage(errorMessage)

        let errorChatMsg: ChatMessage = {
          id: uuid(),
          role: ERROR,
          content: errorMessage,
          date: new Date().toISOString()
        }
        let resultConversation
        if (conversationId) {
          resultConversation = state?.chatHistory?.find(conv => conv.id === conversationId)
          if (!resultConversation) {
            console.error('Conversation not found.')
            setIsLoading(false)
            setShowLoadingMessage(false)
            abortFuncs.current = abortFuncs.current.filter(a => a !== abortController)
            return
          }
          resultConversation.messages.push(errorChatMsg)
        } else {
          if (!result.history_metadata) {
            console.error('Error retrieving data.', result)
            let errorChatMsg: ChatMessage = {
              id: uuid(),
              role: ERROR,
              content: errorMessage,
              date: new Date().toISOString()
            }
            setMessages([...messages, userMessage, errorChatMsg])
            setIsLoading(false)
            setShowLoadingMessage(false)
            abortFuncs.current = abortFuncs.current.filter(a => a !== abortController)
            return
          }
          resultConversation = {
            id: result.history_metadata.conversation_id,
            title: result.history_metadata.title,
            messages: [userMessage],
            date: result.history_metadata.date
          }
          resultConversation.messages.push(errorChatMsg)
        }
        if (!resultConversation) {
          setIsLoading(false)
          setShowLoadingMessage(false)
          abortFuncs.current = abortFuncs.current.filter(a => a !== abortController)
          return
        }
        appStateContext?.dispatch({ type: 'UPDATE_CURRENT_CHAT', payload: resultConversation })
        setMessages([...messages, errorChatMsg])
      } else {
        setMessages([...messages, userMessage])
      }
    } finally {
      setIsLoading(false)
      setShowLoadingMessage(false)
      abortFuncs.current = abortFuncs.current.filter(a => a !== abortController)
      setProcessMessages(messageStatus.Done)
    }
    return abortController.abort()
  }

  const clearChat = async () => {
    setClearingChat(true)
    try {
      if (!state.currentChat?.id) {
        console.error('Cannot clear chat: No current chat ID available')
        setErrorMsg({
          title: 'Error clearing current chat',
          subtitle: 'No chat ID found. Please try again or start a new chat.'
        })
        toggleErrorDialog()
        return
      }

      if (!state.isCosmosDBAvailable?.cosmosDB) {
        console.error('Cannot clear chat: CosmosDB is not available')
        setErrorMsg({
          title: 'Error clearing current chat',
          subtitle: 'Chat history database is not available. Please try again or contact the site administrator.'
        })
        toggleErrorDialog()
        return
      }

      let response = await historyClear(state.currentChat.id)
      if (!response.ok) {
        console.error(`Error clearing chat: ${response.status} ${response.statusText}`)
        setErrorMsg({
          title: 'Error clearing current chat',
          subtitle: 'Please try again. If the problem persists, please contact the site administrator.'
        })
        toggleErrorDialog()
      } else {
        if (!appStateContext) {
          console.error('App state context is not available')
          setErrorMsg({
            title: 'Error clearing current chat',
            subtitle: 'Application state is not available. Please refresh the page and try again.'
          })
          toggleErrorDialog()
          return
        }

        appStateContext.dispatch({
          type: 'DELETE_CURRENT_CHAT_MESSAGES',
          payload: state.currentChat.id
        })

        if (state.currentChat) {
          appStateContext.dispatch({ type: 'UPDATE_CHAT_HISTORY', payload: state.currentChat })
        }

        setActiveCitation(undefined)
        setIsCitationPanelOpen(false)
        setIsIntentsPanelOpen(false)
        setMessages([])
      }
    } catch (error) {
      console.error('Unexpected error in clearChat:', error)
      setErrorMsg({
        title: 'Unexpected error',
        subtitle: 'An unexpected error occurred while clearing chat. Please try again or contact support.'
      })
      toggleErrorDialog()
    } finally {
      setClearingChat(false)
    }
  }

  const tryGetRaiPrettyError = (errorMessage: string) => {
    try {
      // Using a regex to extract the JSON part that contains "innererror"
      const match = errorMessage.match(/'innererror': ({.*})\}\}/)
      if (match) {
        // Replacing single quotes with double quotes and converting Python-like booleans to JSON booleans
        const fixedJson = match[1]
          .replace(/'/g, '"')
          .replace(/\bTrue\b/g, 'true')
          .replace(/\bFalse\b/g, 'false')
        const innerErrorJson = JSON.parse(fixedJson)
        let reason = ''
        // Check if jailbreak content filter is the reason of the error
        const jailbreak = innerErrorJson.content_filter_result.jailbreak
        if (jailbreak.filtered === true) {
          reason = 'Jailbreak'
        }

        // Returning the prettified error message
        if (reason !== '') {
          return (
            "The prompt was filtered due to triggering Azure OpenAI's content filtering system.\n" +
            'Reason: This prompt contains content flagged as ' +
            reason +
            '\n\n' +
            'Please modify your prompt and retry. Learn more: https://go.microsoft.com/fwlink/?linkid=2198766'
          )
        }
      }
    } catch (e) {
      console.error('Failed to parse the error:', e)
    }
    return errorMessage
  }

  const parseErrorMessage = (errorMessage: string) => {
    let errorCodeMessage = errorMessage.substring(0, errorMessage.indexOf('-') + 1)
    const innerErrorCue = "{\\'error\\': {\\'message\\': "
    if (errorMessage.includes(innerErrorCue)) {
      try {
        let innerErrorString = errorMessage.substring(errorMessage.indexOf(innerErrorCue))
        if (innerErrorString.endsWith("'}}")) {
          innerErrorString = innerErrorString.substring(0, innerErrorString.length - 3)
        }
        innerErrorString = innerErrorString.replaceAll("\\'", "'")
        let newErrorMessage = errorCodeMessage + ' ' + innerErrorString
        errorMessage = newErrorMessage
      } catch (e) {
        console.error('Error parsing inner error message: ', e)
      }
    }

    return tryGetRaiPrettyError(errorMessage)
  }

  const newChat = () => {
    setProcessMessages(messageStatus.Processing)
    setMessages([])
    setIsCitationPanelOpen(false)
    setIsIntentsPanelOpen(false)
    setActiveCitation(undefined)
    appStateContext?.dispatch({ type: 'UPDATE_CURRENT_CHAT', payload: null })
    setProcessMessages(messageStatus.Done)
  }

  const stopGenerating = () => {
    abortFuncs.current.forEach(a => a.abort())
    setShowLoadingMessage(false)
    setIsLoading(false)
  }

  useEffect(() => {
    if (state.currentChat) {
      setMessages(state.currentChat.messages)
    } else {
      setMessages([])
    }
  }, [state.currentChat])

  useLayoutEffect(() => {
    const saveToDB = async (messages: ChatMessage[], id: string) => {
      try {
        const response = await historyUpdate(messages, id)
        return response
      } catch (error) {
        console.error('Error in saveToDB:', error)
        return {
          ok: false,
          status: 500,
          statusText: 'Internal error during history update'
        } as Response
      }
    }

    if (!appStateContext) {
      console.error('App state context is not available for saving to DB')
      return
    }

    if (!state.currentChat) {
      console.error('Current chat is not available for saving to DB')
      return
    }

    if (processMessages !== messageStatus.Done) {
      return
    }

    if (state.isCosmosDBAvailable?.cosmosDB) {
      if (!state.currentChat?.messages) {
        console.error('Failure fetching current chat state.')
        return
      }

      const noContentError = state.currentChat.messages.find(m => m.role === ERROR)

      if (!noContentError?.content.includes(NO_CONTENT_ERROR)) {
        saveToDB(state.currentChat.messages, state.currentChat.id)
          .then((res: Response) => {
            if (!res.ok) {
              let errorMessage =
                "An error occurred. Answers can't be saved at this time. If the problem persists, please contact the site administrator."
              let errorChatMsg: ChatMessage = {
                id: uuid(),
                role: ERROR,
                content: errorMessage,
                date: new Date().toISOString()
              }

              if (!state.currentChat?.messages) {
                let err: Error = {
                  ...new Error(),
                  message: 'Failure fetching current chat state.'
                }
                throw err
              }

              setMessages([...state.currentChat.messages, errorChatMsg])
            }
            return res as Response
          })
          .catch((err: Error) => {
            console.error('Error: ', err)
            let errRes: Response = {
              ...new Response(),
              ok: false,
              status: 500
            }
            return errRes
          })
      }
    }

    if (state.currentChat) {
      appStateContext.dispatch({ type: 'UPDATE_CHAT_HISTORY', payload: state.currentChat })
      setMessages(state.currentChat.messages || [])
    }

    setProcessMessages(messageStatus.NotRunning)
  }, [processMessages, appStateContext, state, setMessages, setProcessMessages])

  useEffect(() => {
    if (AUTH_ENABLED !== undefined) getUserInfoList()
  }, [AUTH_ENABLED])

  useLayoutEffect(() => {
    chatMessageStreamEnd.current?.scrollIntoView({ behavior: 'smooth' })
  }, [showLoadingMessage, processMessages])

  const onShowCitation = (citation: Citation) => {
    // Save the current state of the file management panel
    setLeftPanelStateBeforeCitation(isFileManagementOpen)
    // Hide the file management panel when showing citations
    setIsFileManagementOpen(false)
    setActiveCitation(citation)
    setIsCitationPanelOpen(true)
  }

  const onCloseCitationPanel = () => {
    // Restore the file management panel to its previous state
    setIsFileManagementOpen(leftPanelStateBeforeCitation)
    // Close the citation panel
    setIsCitationPanelOpen(false)
  }

  const onShowExecResult = (answerId: string) => {
    setIsIntentsPanelOpen(true)
  }

  const onViewSource = (citation: Citation) => {
    if (citation.url && !citation.url.includes('blob.core')) {
      window.open(citation.url, '_blank')
    }
  }

  const parseCitationFromMessage = (message: ChatMessage) => {
    if (message?.role && message?.role === 'tool') {
      try {
        const toolMessage = JSON.parse(message.content) as ToolMessageContent
        return toolMessage.citations
      } catch {
        return []
      }
    }
    return []
  }

  const parsePlotFromMessage = (message: ChatMessage) => {
    if (message?.role && message?.role === 'tool') {
      try {
        const execResults = JSON.parse(message.content) as AzureSqlServerExecResults
        const codeExecResult = execResults.all_exec_results.at(-1)?.code_exec_result

        if (codeExecResult === undefined) {
          return null
        }
        return codeExecResult.toString()
      } catch {
        return null
      }
      // const execResults = JSON.parse(message.content) as AzureSqlServerExecResults;
      // return execResults.all_exec_results.at(-1)?.code_exec_result;
    }
    return null
  }

  const disabledButton = () => {
    return (
      isLoading ||
      (messages && messages.length === 0) ||
      clearingChat ||
      state.chatHistoryLoadingState === ChatHistoryLoadingState.Loading
    )
  }

  // Remove the previous useEffect for custom resizing
  useEffect(() => {
    // Function to update panel classes based on their states
    const updatePanelClasses = () => {
      const leftPanel = document.querySelector(`.${styles.fileManagementPanel}`) as HTMLElement | null
      const rightPanel = document.querySelector(`.${styles.rightFileManagementPanel}`) as HTMLElement | null

      if (leftPanel && rightPanel) {
        // Clear any previous expanded classes
        leftPanel.classList.remove(styles.expanded)
        rightPanel.classList.remove(styles.expanded)

        // Apply expanded class based on which panel is open and which is collapsed
        if (isFileManagementOpen && !isRightPanelOpen) {
          // Left panel expanded, right panel collapsed
          leftPanel.classList.add(styles.expanded)
        } else if (!isFileManagementOpen && isRightPanelOpen) {
          // Right panel expanded, left panel collapsed
          rightPanel.classList.add(styles.expanded)
        }
        // If both are open or both are closed, no expanded class is needed
      }
    }

    // Run on mount and when panel states change
    updatePanelClasses()

    // Also update on window resize
    window.addEventListener('resize', updatePanelClasses)

    return () => {
      window.removeEventListener('resize', updatePanelClasses)
    }
  }, [isFileManagementOpen, isRightPanelOpen])

  return (
    <div className={styles.container} role="main">
      {isBannerVisible && <div className={styles.banner}>{/* Banner content */}</div>}
      {showAuthMessage ? (
        <Stack className={styles.chatEmptyState}>
          <ShieldLockRegular
            className={styles.chatIcon}
            style={{ color: 'darkorange', height: '200px', width: '200px' }}
          />
          <h1 className={styles.chatEmptyStateTitle}>Authentication Not Configured</h1>
          <h2 className={styles.chatEmptyStateSubtitle}>
            This app does not have authentication configured. Please add an identity provider by finding your app in the{' '}
            <a href="https://portal.azure.com/" target="_blank">
              Azure Portal
            </a>
            and following{' '}
            <a
              href="https://learn.microsoft.com/en-us/azure/app-service/scenario-secure-app-authentication-app-service#3-configure-authentication-and-authorization"
              target="_blank">
              these instructions
            </a>
            .
          </h2>
          <h2 className={styles.chatEmptyStateSubtitle} style={{ fontSize: '20px' }}>
            <strong>Authentication configuration takes a few minutes to apply. </strong>
          </h2>
          <h2 className={styles.chatEmptyStateSubtitle} style={{ fontSize: '20px' }}>
            <strong>If you deployed in the last 10 minutes, please wait and reload the page after 10 minutes.</strong>
          </h2>
        </Stack>
      ) : (
        <Stack horizontal className={styles.chatRoot}>
          {/* File Management Panel */}
          {!isCitationPanelOpen && (
            <div className={`${styles.fileManagementPanel} ${!isFileManagementOpen ? styles.collapsed : ''}`}>
              <FileManagement
                onToggle={isOpen => {
                  setIsFileManagementOpen(isOpen)
                }}
              />
            </div>
          )}

          {/* Citation Panel - Now a sibling to FileManagement, not nested within it */}
          {isCitationPanelOpen && activeCitation && (
            <div className={`${styles.citationPanelContainer} ${!isRightPanelOpen ? styles.expanded : ''}`}>
              <Stack className={styles.citationPanel} tabIndex={0} role="tabpanel" aria-label="Citations Panel">
                <Stack
                  horizontal
                  className={styles.citationPanelHeaderContainer}
                  horizontalAlign="space-between"
                  verticalAlign="center">
                  <h3>Citations</h3>
                  <IconButton
                    iconProps={{ iconName: 'Cancel' }}
                    aria-label="Close citations panel"
                    onClick={onCloseCitationPanel}
                  />
                </Stack>
                <h4>
                  {activeCitation.title && (
                    <span
                      onClick={() => activeCitation.url && onViewSource(activeCitation)}
                      style={{ cursor: activeCitation.url ? 'pointer' : 'default' }}>
                      {activeCitation.title}
                    </span>
                  )}
                </h4>
                <div>
                  <ReactMarkdown remarkPlugins={[remarkGfm]} rehypePlugins={[rehypeRaw]}>
                    {DOMPurify.sanitize(activeCitation.content, { ALLOWED_TAGS: XSSAllowTags })}
                  </ReactMarkdown>
                </div>
              </Stack>
            </div>
          )}

          <div className={styles.chatContainer}>
            {/* Priority Plot Button */}
            <MainPriorityPlotButton onClick={() => setIsPriorityPlotModalOpen(true)} />

            {!messages || messages.length < 1 ? (
              <Stack className={styles.chatEmptyState}>
                <img src={logo} className={styles.chatIcon} aria-hidden="true" />
                <h1 className={styles.chatEmptyStateTitle}>{ui?.chat_title}</h1>
                <h2 className={styles.chatEmptyStateSubtitle}>{ui?.chat_description}</h2>
              </Stack>
            ) : (
              <div className={styles.chatMessageStream} style={{ marginBottom: isLoading ? '40px' : '0px' }} role="log">
                {messages.map((answer, index) => (
                  <>
                    {answer.role === 'user' ? (
                      <div className={styles.chatMessageUser} tabIndex={0}>
                        <div className={styles.chatMessageUserMessage}>{answer.content}</div>
                      </div>
                    ) : answer.role === 'assistant' ? (
                      <div className={styles.chatMessageGpt}>
                        <Answer
                          answer={{
                            answer: answer.content,
                            citations: parseCitationFromMessage(messages[index - 1]),
                            generated_chart: parsePlotFromMessage(messages[index - 1]),
                            message_id: answer.id,
                            feedback: answer.feedback,
                            exec_results: execResults
                          }}
                          onCitationClicked={c => onShowCitation(c)}
                          onExectResultClicked={() => onShowExecResult(answerId)}
                        />
                      </div>
                    ) : answer.role === ERROR ? (
                      <div className={styles.chatMessageError}>
                        <Stack horizontal className={styles.chatMessageErrorContent}>
                          <ErrorCircleRegular className={styles.errorIcon} style={{ color: 'rgba(182, 52, 67, 1)' }} />
                          <span>Error</span>
                        </Stack>
                        <span className={styles.chatMessageErrorContent}>{answer.content}</span>
                      </div>
                    ) : null}
                  </>
                ))}
                {showLoadingMessage && (
                  <>
                    <div className={styles.chatMessageGpt}>
                      <Answer
                        answer={{
                          answer: 'Generating answer...',
                          citations: [],
                          generated_chart: null
                        }}
                        onCitationClicked={() => null}
                        onExectResultClicked={() => null}
                      />
                    </div>
                  </>
                )}
                <div ref={chatMessageStreamEnd} />
              </div>
            )}

            <Stack horizontal className={styles.chatInput}>
              {/* Button Container */}
              <div className={styles.chatButtonsContainer}>
                {state.isCosmosDBAvailable?.status !== CosmosDBStatus.NotConfigured && (
                  <CommandBarButton
                    role="button"
                    styles={{
                      icon: {
                        color: '#FFFFFF'
                      },
                      iconDisabled: {
                        color: '#BDBDBD !important'
                      },
                      root: {
                        color: '#FFFFFF',
                        width: '40px',
                        height: '40px',
                        padding: 0,
                        minWidth: '40px'
                      },
                      rootDisabled: {
                        background: '#F0F0F0'
                      }
                    }}
                    className={styles.newChatIcon}
                    iconProps={{ iconName: 'Add' }}
                    onClick={newChat}
                    disabled={disabledButton()}
                    aria-label="start a new chat button"
                  />
                )}
              </div>

              {/* Question Input */}
              <QuestionInput
                clearOnSend
                placeholder="Type a new question..."
                disabled={isLoading}
                onSend={(question, id) => {
                  state.isCosmosDBAvailable?.cosmosDB
                    ? makeApiRequestWithCosmosDB(question, id)
                    : makeApiRequestWithoutCosmosDB(question, id)
                }}
                conversationId={state.currentChat?.id ? state.currentChat?.id : undefined}
              />

              {/* Stop Generating Container - Move to be above the input */}
              {isLoading && messages.length > 0 && (
                <Stack
                  horizontal
                  className={styles.stopGeneratingContainer}
                  role="button"
                  aria-label="Stop generating"
                  tabIndex={0}
                  onClick={stopGenerating}
                  onKeyDown={e => (e.key === 'Enter' || e.key === ' ' ? stopGenerating() : null)}>
                  <SquareRegular className={styles.stopGeneratingIcon} aria-hidden="true" />
                  <span className={styles.stopGeneratingText} aria-hidden="true">
                    Stop generating
                  </span>
                </Stack>
              )}

              {/* Dialog should be outside the button container */}
              <Dialog
                hidden={hideErrorDialog}
                onDismiss={handleErrorDialogClose}
                dialogContentProps={errorDialogContentProps}
                modalProps={modalProps}
              />
            </Stack>
          </div>
          <div className={`${styles.rightFileManagementPanel} ${!isRightPanelOpen ? styles.collapsed : ''}`}>
            <RightFileManagement onToggle={setIsRightPanelOpen} />
          </div>
          {messages && messages.length > 0 && isIntentsPanelOpen && (
            <Stack.Item className={styles.citationPanel} tabIndex={0} role="tabpanel" aria-label="Intents Panel">
              <Stack
                aria-label="Intents Panel Header Container"
                horizontal
                className={styles.citationPanelHeaderContainer}
                horizontalAlign="space-between"
                verticalAlign="center">
                <span aria-label="Intents" className={styles.citationPanelHeader}>
                  Intents
                </span>
                <IconButton
                  iconProps={{ iconName: 'Cancel' }}
                  aria-label="Close intents panel"
                  onClick={() => setIsIntentsPanelOpen(false)}
                />
              </Stack>
              <Stack horizontalAlign="space-between">
                {state?.answerExecResult[answerId]?.map((execResult: ExecResults, index) => (
                  <Stack className={styles.exectResultList} verticalAlign="space-between">
                    <>
                      <span>Intent:</span> <p>{execResult.intent}</p>
                    </>
                    {execResult.search_query && (
                      <>
                        <span>Search Query:</span>
                        <pre style={{ wordBreak: 'break-all', whiteSpace: 'pre-wrap' }}>
                          <code>{execResult.search_query}</code>
                        </pre>
                      </>
                    )}
                    {execResult.search_result && (
                      <>
                        <span>Search Result:</span> <p>{execResult.search_result}</p>
                      </>
                    )}
                    {execResult.code_generated && (
                      <>
                        <span>Code Generated:</span>
                        <pre style={{ wordBreak: 'break-all', whiteSpace: 'pre-wrap' }}>
                          <code>{execResult.code_generated}</code>
                        </pre>
                      </>
                    )}
                  </Stack>
                ))}
              </Stack>
            </Stack.Item>
          )}
          {state.isChatHistoryOpen && state.isCosmosDBAvailable?.status !== CosmosDBStatus.NotConfigured && (
            <ChatHistoryPanel />
          )}
        </Stack>
      )}

      {/* Priority Plot Modal */}
      <PriorityPlotModal
        isOpen={isPriorityPlotModalOpen}
        onClose={() => setIsPriorityPlotModalOpen(false)}
      />
    </div>
  )
}

export default Chat
