/* frontend/src/pages/chat/Chat.module.css */
.container {
  display: flex;
  height: calc(100vh - 60px); /* Subtract header height */
  position: relative;
  z-index: 1;
  gap: 20px;
  padding: 20px; /* Add padding to container instead of margin to children */
  box-sizing: border-box;
  overflow: hidden; /* Prevent container scrollbars */
}

/* Left File management panel styles */
.fileManagementPanel {
  flex-shrink: 0; /* Don't allow shrinking below min-width */
  flex-grow: 0;
  width: 25%; /* Default width when both panels are expanded */
  min-width: 200px;
  max-width: 45%; /* Maximum width when only this panel is expanded */
  height: 100%;
  background: #ffffff;
  border-radius: 8px;
  box-shadow:
    0px 2px 4px rgba(0, 0, 0, 0.14),
    0px 0px 2px rgba(0, 0, 0, 0.12);
  transition: width 0.3s ease;
  position: relative;
  overflow-y: auto; /* Allow vertical scrolling */
  overflow-x: hidden; /* Hide horizontal scrollbar */
  resize: none; /* Remove custom resizing */
  display: flex;
  flex-direction: column;
}

/* Make sure all content inside panels adapts to the panel width */
.fileManagementPanel > * {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

/* Make sure all images and other media inside panels adapt to the panel width */
.fileManagementPanel img,
.fileManagementPanel svg,
.fileManagementPanel video,
.fileManagementPanel canvas {
  max-width: 100%;
  height: auto;
}

/* When only the left panel is expanded (right panel is collapsed) */
.fileManagementPanel.expanded {
  width: 45%;
}

/* Styling for collapsed panels */
.fileManagementPanel.collapsed {
  width: 64px !important;
  min-width: 64px !important;
  max-width: 64px !important;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer; /* Make the entire panel clickable */
}

/* Add double arrow indicators for collapsed panels */
.fileManagementPanel.collapsed::after {
  content: '»';
  position: absolute;
  top: 30%; /* Position the top arrow closer to the title */
  font-size: 24px;
  color: #0f6cbd;
  transform: translateY(-50%);
  display: block;
  text-align: center;
  width: 100%;
  font-weight: bold;
  opacity: 0.7;
  z-index: 1;
  pointer-events: none;
}

/* Add vertical text between arrows for left panel */
.fileManagementPanel.collapsed::before {
  content: 'Input Files';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(90deg);
  white-space: nowrap;
  font-size: 12px;
  color: #0f6cbd;
  font-weight: 500;
  opacity: 0.9;
  z-index: 2;
  pointer-events: none;
  width: auto;
}

/* Add second arrow at the bottom for left panel */
.fileManagementPanel.collapsed .second-arrow {
  content: '»';
  position: absolute;
  bottom: 60%; /* Position the bottom arrow closer to the title */
  font-size: 24px;
  color: #0f6cbd; /*#0F6CBD;*/
  transform: translateY(50%);
  display: block;
  text-align: center;
  width: 100%;
  font-weight: bold;
  opacity: 0.7;
  z-index: 1;
  pointer-events: none;
}

/* Hide header in collapsed panels */
.fileManagementPanel.collapsed .file-management-header,
.rightFileManagementPanel.collapsed .file-management-header {
  display: none !important;
}

/* Hide any other header elements that might be showing */
.fileManagementPanel.collapsed h2,
.fileManagementPanel.collapsed .toggle-button,
.rightFileManagementPanel.collapsed h2,
.rightFileManagementPanel.collapsed .toggle-button {
  display: none !important;
}

/* Ensure the collapsed icon is properly positioned at the top */
.fileManagementPanel.collapsed .collapsed-icon,
.rightFileManagementPanel.collapsed .collapsed-icon {
  position: absolute;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 3;
  margin-top: 0;
}

/* Right File management panel styles */
.rightFileManagementPanel {
  flex-shrink: 0; /* Don't allow shrinking below min-width */
  flex-grow: 0;
  width: 25%; /* Default width when both panels are expanded */
  min-width: 200px;
  max-width: 45%; /* Maximum width when only this panel is expanded */
  height: 100%;
  background: #ffffff;
  border-radius: 8px;
  box-shadow:
    0px 2px 4px rgba(0, 0, 0, 0.14),
    0px 0px 2px rgba(0, 0, 0, 0.12);
  transition: width 0.3s ease;
  position: relative;
  overflow-y: auto; /* Allow vertical scrolling */
  overflow-x: hidden; /* Hide horizontal scrollbar */
  resize: none; /* Remove custom resizing */
  display: flex;
  flex-direction: column;
}

/* Make sure all content inside panels adapts to the panel width */
.rightFileManagementPanel > * {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

/* Make sure all images and other media inside panels adapt to the panel width */
.rightFileManagementPanel img,
.rightFileManagementPanel svg,
.rightFileManagementPanel video,
.rightFileManagementPanel canvas {
  max-width: 100%;
  height: auto;
}

/* When only the right panel is expanded (left panel is collapsed) */
.rightFileManagementPanel.expanded {
  width: 45%;
}

/* Styling for collapsed panels */
.rightFileManagementPanel.collapsed {
  width: 64px;
  min-width: 64px;
  max-width: 64px;
  overflow: hidden;
  transition: width 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer; /* Make the entire panel clickable */
}

/* Add double arrow indicators for collapsed panels */
.rightFileManagementPanel.collapsed::after {
  content: '«';
  position: absolute;
  top: 30%; /* Position the top arrow closer to the title */
  font-size: 24px;
  color: #0f6cbd;
  transform: translateY(-50%);
  display: block;
  text-align: center;
  width: 100%;
  font-weight: bold;
  opacity: 0.7;
  z-index: 1;
  pointer-events: none;
}

/* Add vertical text between arrows for right panel */
.rightFileManagementPanel.collapsed::before {
  content: 'Template Files';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(90deg);
  white-space: nowrap;
  font-size: 12px;
  color: #0f6cbd;
  font-weight: 500;
  opacity: 0.9;
  z-index: 2;
  pointer-events: none;
  width: auto;
}

/* Add second arrow at the bottom for right panel */
.rightFileManagementPanel.collapsed .second-arrow {
  content: '«';
  position: absolute;
  bottom: 40%; /* Position the bottom arrow closer to the title */
  font-size: 24px;
  color: #0f6cbd; /* Match the color of the top arrow */
  transform: translateY(50%);
  display: block;
  text-align: center;
  width: 100%;
  font-weight: bold;
  opacity: 0.7;
  z-index: 1;
  pointer-events: none;
}

.chatRoot {
  display: flex;
  flex: 1;
  gap: 5px;
  height: 100%; /* Use full height of parent */
  width: 100%; /* Ensure it takes full width */
  overflow: hidden; /* Prevent chatRoot scrollbars */
}

.chatContainer {
  flex: 1;
  overflow-y: auto; /* Allow vertical scrolling */
  overflow-x: hidden; /* Hide horizontal scrollbar */
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative; /* Add position relative for absolute children */
  height: 100%; /* Take full height */
  padding-bottom: 120px; /* Add padding to ensure content doesn't get hidden behind input */
  box-sizing: border-box; /* Include padding in height calculation */
  background: radial-gradient(108.78% 108.78% at 50.02% 19.78%, #ffffff 57.29%, #eef6fe 100%);
  box-shadow:
    0px 2px 4px rgba(0, 0, 0, 0.14),
    0px 0px 2px rgba(0, 0, 0, 0.12);
  border-radius: 8px;
}

.chatEmptyState {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.chatEmptyStateTitle {
  font-style: normal;
  font-weight: 700;
  font-size: 36px;
  display: flex;
  align-items: flex-end;
  text-align: center;
  line-height: 24px;
  margin-top: 36px;
  margin-bottom: 0px;
}

.chatEmptyStateSubtitle {
  margin-top: 20px;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 150%;
  align-items: flex-end;
  text-align: center;
  letter-spacing: -0.01em;
  color: #616161;
}

.chatIcon {
  height: 62px;
  width: auto;
}

.chatMessageStream {
  flex-grow: 1;
  max-width: 1028px;
  width: 100%;
  overflow-y: auto;
  padding-left: 24px;
  padding-right: 24px;
  display: flex;
  flex-direction: column;
  margin-top: 24px;
  margin-bottom: 150px; /* Increased from 80px to ensure chat content stays above input */
  padding-bottom: 30px; /* Increased from 20px to add more space */
  position: relative; /* Add position relative */
  z-index: 1; /* Lower z-index than input */
}

.chatMessageUser {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 12px;
}

.chatMessageUserMessage {
  display: flex;
  padding: 20px;
  background: #edf5fd;
  border-radius: 8px;
  box-shadow:
    0px 2px 4px rgba(0, 0, 0, 0.14),
    0px 0px 2px rgba(0, 0, 0, 0.12);
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #242424;
  order: 0;
  flex-grow: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-width: 80%;
}

.chatMessageGpt {
  margin-bottom: 12px;
  max-width: 80%;
  display: flex;
}

.chatMessageError {
  padding: 20px;
  border-radius: 8px;
  box-shadow:
    rgba(182, 52, 67, 1) 1px 1px 2px,
    rgba(182, 52, 67, 1) 0px 0px 1px;
  color: #242424;
  flex: none;
  order: 0;
  flex-grow: 0;
  max-width: 800px;
  margin-bottom: 12px;
}

.chatMessageErrorContent {
  font-family: 'Segoe UI';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  white-space: pre-wrap;
  word-wrap: break-word;
  gap: 12px;
  align-items: center;
}

.chatInput {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  max-width: 100%;
  padding: 16px;
  background-color: white;
  border-top: 1px solid #f0f0f0;
  z-index: 100;
}

/* Create a container for the buttons */
.chatButtonsContainer {
  display: flex;
  flex-direction: column; /* Change to column for vertical layout */
  flex-shrink: 0;
  width: 40px; /* Fixed width for buttons */
  gap: 8px; /* Space between buttons */
  margin-top: 0;
  margin-left: 12px;
  margin-right: 8px;
}

/* Style for the QuestionInput component */
.chatInput > div:last-child {
  flex-grow: 1;
  width: auto;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 12px; /* Add right margin */
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.15); /* Add shadow for floating effect */
  margin-top: 0; /* Ensure no top margin */
}

/* Fix for QuestionInput component */
:global(.ms-TextField) {
  width: 100% !important;
}

/* Fix for the question input container */
:global(.questionInputContainer) {
  position: relative !important;
  left: 0 !important;
  height: auto !important;
  min-height: 50px !important;
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.15) !important; /* Add shadow for floating effect */
  border: 1px solid #e5e7eb; /* Add subtle border */
  background-color: white; /* Keep the input box white */
  border-radius: 8px; /* Add rounded corners */
}

.clearChatBroom {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  color: #ffffff;
  border-radius: 8px; /* Match input box border radius */
  z-index: 10;
  position: static;
  background: radial-gradient(109.81% 107.82% at 100.1% 90.19%, #0f6cbd 33.63%, #2d87c3 70.31%, #8dddd8 100%);
  cursor: pointer;
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.15); /* Add shadow for floating effect */
}

.clearChatBroomNoCosmos {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  color: #ffffff;
  border-radius: 8px; /* Match input box border radius */
  z-index: 10;
  position: static;
  background: radial-gradient(109.81% 107.82% at 100.1% 90.19%, #0f6cbd 33.63%, #2d87c3 70.31%, #8dddd8 100%);
  cursor: pointer;
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.15); /* Add shadow for floating effect */
}

.newChatIcon {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  color: #ffffff;
  border-radius: 8px; /* Match input box border radius */
  z-index: 10;
  position: static;
  background: radial-gradient(109.81% 107.82% at 100.1% 90.19%, #0f6cbd 33.63%, #2d87c3 70.31%, #8dddd8 100%);
  cursor: pointer;
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.15); /* Add shadow for floating effect */
}

.stopGeneratingContainer {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 5px 16px;
  gap: 4px;
  position: absolute;
  width: 161px;
  height: 32px;
  left: calc(50% - 80px); /* Center horizontally */
  bottom: 90px; /* Position above the input, adjusted for vertical buttons */
  border: 1px solid #d1d1d1;
  border-radius: 16px;
  background-color: white;
  z-index: 20; /* Ensure it's above other elements */
}

.stopGeneratingIcon {
  width: 14px;
  height: 14px;
  color: #424242;
}

.stopGeneratingText {
  width: 103px;
  height: 20px;
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  display: flex;
  align-items: center;
  color: #242424;
  flex: none;
  order: 0;
  flex-grow: 0;
}

.citationPanel {
  height: 100%;
  padding: 16px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

/* Style for the header in the citation panel */
.citationPanel h3 {
  margin-top: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Style for the close button in the citation panel */
.citationPanel button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  color: #666;
}

.citationPanel button:hover {
  color: #000;
}

/* Style for the content in the citation panel */
.citationPanel pre {
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  margin-top: 8px;
}

.citationPanelContainer {
  flex-shrink: 0;
  flex-grow: 0;
  width: 25%;
  min-width: 200px;
  max-width: 45%;
  height: 100%;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: width 0.3s ease;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
}

.citationPanelContainer.expanded {
  width: 45%;
}

/* Add a media query to handle smaller screens */
@media (max-width: 1200px) {
  .fileManagementPanel:not(.collapsed),
  .rightFileManagementPanel:not(.collapsed) {
    max-width: 35%; /* Reduce side panel max width on smaller screens */
  }

  .chatContainer {
    min-width: 60%; /* Increase chat container min width on smaller screens */
  }

  .chatInput {
    left: 8%; /* Slightly less space on smaller screens */
    right: 8%;
    width: 84%;
    max-width: 84%;
  }
}

/* Add a media query to handle very small screens */
@media (max-width: 768px) {
  .container {
    flex-direction: column;
    height: auto;
    overflow-y: auto; /* Allow vertical scrolling on small screens */
  }

  .chatRoot {
    flex-direction: column;
    height: auto;
  }

  .fileManagementPanel:not(.collapsed),
  .rightFileManagementPanel:not(.collapsed) {
    width: 100%;
    max-width: 100%;
    min-height: 200px;
    height: auto;
    resize: none; /* Disable resizing on mobile */
  }

  .chatContainer {
    min-width: 100%;
    min-height: 400px;
  }

  .chatInput {
    left: 5%; /* Even less space on mobile */
    right: 5%;
    width: 90%;
    max-width: 90%;
  }

  .chatButtonsContainer {
    flex-direction: column; /* Keep vertical layout on mobile */
    margin-left: 0;
    margin-right: 8px;
  }
}

@media (max-width: 480px) {
  .chatInput {
    left: 2%;
    right: 2%;
    width: 96%;
    max-width: 96%;
    padding-right: 20px;
  }

  .chatButtonsContainer {
    gap: 6px; /* Slightly reduce gap on small screens */
    margin-left: 6px; /* Reduce left margin on small screens */
  }

  .newChatIcon,
  .clearChatBroom,
  .clearChatBroomNoCosmos {
    width: 36px;
    height: 36px;
  }

  .chatEmptyStateTitle {
    font-size: 24px;
  }

  .citationPanel {
    width: 100%;
  }
}
