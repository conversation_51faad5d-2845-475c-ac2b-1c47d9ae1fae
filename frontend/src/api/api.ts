// frontend/src/api/api.ts
import { chatHistorySampleData } from '../constants/chatHistory'
import { getAuthHeaders } from '../services/authHeaderService'
import { ChatMessage, Conversation, ConversationRequest, CosmosDBHealth, CosmosDBStatus, UserInfo, FrontendSettings } from './models'

// Modified to accept projectId
export async function conversationApi(options: ConversationRequest, abortSignal: AbortSignal, projectId: string | null): Promise<Response> {
  // Use the project-specific endpoint if projectId is available
  const endpoint = projectId ? `/api/projects/${projectId}/conversation` : '/conversation';
  console.log(`Using endpoint: ${endpoint}`); // Add logging

  // Get authentication headers
  const authHeaders = await getAuthHeaders();

  const response = await fetch(endpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...authHeaders // Add authentication headers
    },
    body: JSON.stringify({
      messages: options.messages
    }),
    signal: abortSignal
  })

  return response
}

export async function getUserInfo(): Promise<UserInfo[]> {
  const response = await fetch('/.auth/me')
  if (!response.ok) {
    console.log('No identity provider found. Access to chat will be blocked.')
    return []
  }

  const payload = await response.json()
  return payload
}

// export const fetchChatHistoryInit = async (): Promise<Conversation[] | null> => {
export const fetchChatHistoryInit = (): Conversation[] | null => {
  // Make initial API call here

  return chatHistorySampleData
}

export const historyList = async (offset = 0, projectId?: string | null): Promise<Conversation[] | null> => {
  // Build URL with query parameters
  let url = `/history/list?offset=${offset}`;
  if (projectId) {
    url += `&project_id=${projectId}`;
    console.log(`Fetching chat history for project: ${projectId}`);
  }

  // Get authentication headers
  const authHeaders = await getAuthHeaders();

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      ...authHeaders
    }
  })
    .then(async res => {
      const payload = await res.json()
      if (!Array.isArray(payload)) {
        console.error('There was an issue fetching your data.')
        return null
      }
      const conversations: Conversation[] = await Promise.all(
        payload.map(async (conv: any) => {
          let convMessages: ChatMessage[] = []
          convMessages = await historyRead(conv.id)
            .then(res => {
              return res
            })
            .catch(err => {
              console.error('error fetching messages: ', err)
              return []
            })
          const conversation: Conversation = {
            id: conv.id,
            title: conv.title,
            date: conv.createdAt,
            messages: convMessages
          }
          return conversation
        })
      )
      return conversations
    })
    .catch(_err => {
      console.error('There was an issue fetching your data.')
      return null
    })

  return response
}

export const historyRead = async (convId: string): Promise<ChatMessage[]> => {
  // Get authentication headers
  const authHeaders = await getAuthHeaders();

  const response = await fetch('/history/read', {
    method: 'POST',
    body: JSON.stringify({
      conversation_id: convId
    }),
    headers: {
      'Content-Type': 'application/json',
      ...authHeaders
    }
  })
    .then(async res => {
      if (!res) {
        return []
      }
      const payload = await res.json()
      const messages: ChatMessage[] = []
      if (payload?.messages) {
        payload.messages.forEach((msg: any) => {
          const message: ChatMessage = {
            id: msg.id,
            role: msg.role,
            date: msg.createdAt,
            content: msg.content,
            feedback: msg.feedback ?? undefined
          }
          messages.push(message)
        })
      }
      return messages
    })
    .catch(_err => {
      console.error('There was an issue fetching your data.')
      return []
    })
  return response
}

// Modified to accept and send projectId
export const historyGenerate = async (
  options: ConversationRequest,
  abortSignal: AbortSignal,
  projectId: string | null, // Added projectId
  convId?: string
): Promise<Response> => {
  let requestBody: any = {
    messages: options.messages
  };

  if (convId) {
    requestBody.conversation_id = convId;
  }

  // Include projectId in the request body if available
  if (projectId) {
    requestBody.project_id = projectId;
    console.log(`Sending projectId ${projectId} to /history/generate`); // Add logging
  } else {
    // Decide if an error should be thrown or if it can proceed without projectId
    // For now, log a warning if projectId is missing when generating history
    console.warn("Attempting to generate history without a projectId.");
  }

  // Get authentication headers
  const authHeaders = await getAuthHeaders();

  const response = await fetch('/history/generate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...authHeaders // Add authentication headers
    },
    body: JSON.stringify(requestBody), // Use the constructed body
    signal: abortSignal
  })
    .then(res => {
      return res
    })
    .catch(_err => {
      console.error('There was an issue fetching your data.')
      return new Response()
    })
  return response
}

export const historyUpdate = async (messages: ChatMessage[], convId: string): Promise<Response> => {
  // Get authentication headers
  const authHeaders = await getAuthHeaders();

  const response = await fetch('/history/update', {
    method: 'POST',
    body: JSON.stringify({
      conversation_id: convId,
      messages: messages
    }),
    headers: {
      'Content-Type': 'application/json',
      ...authHeaders
    }
  })
    .then(async res => {
      return res
    })
    .catch(_err => {
      console.error('There was an issue fetching your data.')
      const errRes: Response = {
        ...new Response(),
        ok: false,
        status: 500
      }
      return errRes
    })
  return response
}

export const historyDelete = async (convId: string): Promise<Response> => {
  // Get authentication headers
  const authHeaders = await getAuthHeaders();

  const response = await fetch('/history/delete', {
    method: 'DELETE',
    body: JSON.stringify({
      conversation_id: convId
    }),
    headers: {
      'Content-Type': 'application/json',
      ...authHeaders
    }
  })
    .then(res => {
      return res
    })
    .catch(_err => {
      console.error('There was an issue fetching your data.')
      const errRes: Response = {
        ...new Response(),
        ok: false,
        status: 500
      }
      return errRes
    })
  return response
}

export const historyDeleteAll = async (): Promise<Response> => {
  // Get authentication headers
  const authHeaders = await getAuthHeaders();

  const response = await fetch('/history/delete_all', {
    method: 'DELETE',
    body: JSON.stringify({}),
    headers: {
      'Content-Type': 'application/json',
      ...authHeaders
    }
  })
    .then(res => {
      return res
    })
    .catch(_err => {
      console.error('There was an issue fetching your data.')
      const errRes: Response = {
        ...new Response(),
        ok: false,
        status: 500
      }
      return errRes
    })
  return response
}

export const historyClear = async (convId: string): Promise<Response> => {
  // Get authentication headers
  const authHeaders = await getAuthHeaders();

  const response = await fetch('/history/clear', {
    method: 'POST',
    body: JSON.stringify({
      conversation_id: convId
    }),
    headers: {
      'Content-Type': 'application/json',
      ...authHeaders
    }
  })
    .then(res => {
      return res
    })
    .catch(_err => {
      console.error('There was an issue fetching your data.')
      const errRes: Response = {
        ...new Response(),
        ok: false,
        status: 500
      }
      return errRes
    })
  return response
}

export const historyRename = async (convId: string, title: string): Promise<Response> => {
  // Get authentication headers
  const authHeaders = await getAuthHeaders();

  const response = await fetch('/history/rename', {
    method: 'POST',
    body: JSON.stringify({
      conversation_id: convId,
      title: title
    }),
    headers: {
      'Content-Type': 'application/json',
      ...authHeaders
    }
  })
    .then(res => {
      return res
    })
    .catch(_err => {
      console.error('There was an issue fetching your data.')
      const errRes: Response = {
        ...new Response(),
        ok: false,
        status: 500
      }
      return errRes
    })
  return response
}

export const projectDelete = async (projectId: string): Promise<Response> => {
  console.log(`projectDelete API called for project ID: ${projectId}`);
  try {
    // Get authentication headers
    const authHeaders = await getAuthHeaders();

    console.log(`Sending DELETE request to /api/projects/${projectId}`);
    const response = await fetch(`/api/projects/${projectId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        ...authHeaders
      }
    });

    console.log(`Received response from DELETE /api/projects/${projectId}:`, {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok
    });

    return response;
  } catch (err) {
    console.error(`Error in projectDelete API call for project ${projectId}:`, err);
    const errRes: Response = {
      ...new Response(),
      ok: false,
      status: 500
    };
    return errRes;
  }
}

export const historyEnsure = async (): Promise<CosmosDBHealth> => {
  // Get authentication headers
  const authHeaders = await getAuthHeaders();

  const response = await fetch('/history/ensure', {
    method: 'GET',
    headers: {
      ...authHeaders
    }
  })
    .then(async res => {
      const respJson = await res.json()
      let formattedResponse
      if (respJson.message) {
        formattedResponse = CosmosDBStatus.Working
      } else {
        if (res.status === 500) {
          formattedResponse = CosmosDBStatus.NotWorking
        } else if (res.status === 401) {
          formattedResponse = CosmosDBStatus.InvalidCredentials
        } else if (res.status === 422) {
          formattedResponse = respJson.error
        } else {
          formattedResponse = CosmosDBStatus.NotConfigured
        }
      }
      if (!res.ok) {
        return {
          cosmosDB: false,
          status: formattedResponse
        }
      } else {
        return {
          cosmosDB: true,
          status: formattedResponse
        }
      }
    })
    .catch(err => {
      console.error('There was an issue fetching your data.')
      return {
        cosmosDB: false,
        status: err
      }
    })
  return response
}

export const frontendSettings = async (): Promise<FrontendSettings | null> => {
  // Get authentication headers
  const authHeaders = await getAuthHeaders();

  const response = await fetch('/frontend_settings', {
    method: 'GET',
    headers: {
      ...authHeaders
    }
  })
    .then(res => {
      return res.json()
    })
    .catch(_err => {
      console.error('There was an issue fetching your data.')
      return null
    })

  return response
}
export const historyMessageFeedback = async (messageId: string, feedback: string): Promise<Response> => {
  // Get authentication headers
  const authHeaders = await getAuthHeaders();

  const response = await fetch('/history/message_feedback', {
    method: 'POST',
    body: JSON.stringify({
      message_id: messageId,
      message_feedback: feedback
    }),
    headers: {
      'Content-Type': 'application/json',
      ...authHeaders
    }
  })
    .then(res => {
      return res
    })
    .catch(_err => {
      console.error('There was an issue logging feedback.')
      const errRes: Response = {
        ...new Response(),
        ok: false,
        status: 500
      }
      return errRes
    })
  return response
}
