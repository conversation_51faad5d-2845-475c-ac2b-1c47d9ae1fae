/* Add the new styles for indexing status display */
.indexingStatus {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 0.85rem;
  margin: 16px auto 8px;
  color: #666;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  text-align: center;
  max-width: 90%;
}

.statusText {
  font-weight: 500;
  margin-bottom: 4px;
}

.statusTime {
  font-style: italic;
  font-size: 0.8rem;
  color: #888;
} 

/* WebSocket status indicator */
.status-indicator {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-left: 8px;
  font-size: 24px;
  line-height: 10px;
}

.status-indicator.connected {
  color: #22c55e; /* Green color for connected state */
}

.status-indicator.disconnected {
  color: #ef4444; /* Red color for disconnected state */
}

/* WebSocket status indicator for global scope */
:global(.ws-status-indicator) {
  display: inline-block;
  margin-left: 8px;
  font-size: 16px;
  line-height: 1;
}

:global(.ws-status-indicator.connected) {
  color: #22c55e; /* Green color for connected state */
}

:global(.ws-status-indicator.disconnected) {
  color: #ef4444; /* Red color for disconnected state */
}

:global(.right-panel-status) {
  display: flex;
  align-items: center;
}

/* Connection controls container */
.connection-controls {
  display: flex;
  align-items: center;
  margin-left: auto;
}

/* Update status */
.update-status {
  font-size: 12px;
  color: #6b7280;
  text-align: right;
  padding: 5px 10px;
  margin-top: 5px;
  border-top: 1px solid #e5e7eb;
}

.update-time {
  font-style: italic;
}

/* WebSocket connection indicators in file list header */
.files-count {
  display: flex;
  align-items: center;
}

/* For the refresh button */
.refresh-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  margin-left: 10px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-button:hover {
  background-color: #f3f4f6;
}

.refresh-button.loading svg {
  animation: spin 1s linear infinite;
}

.refresh-button.stale {
  color: #f59e0b;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}