import React from 'react';
import styles from './Header.module.css';

interface Project {
  id: string;
  name: string;
  // Add other fields as needed
}

interface HeaderProps {
  project: Project;
}

const Header: React.FC<HeaderProps> = ({ project }) => {
  return (
    <header className={styles.header}>
      <div className={styles.titleArea}>
        <h2>{project.name}</h2>
      </div>
      
      <div className={styles.actions}>
        <div className={styles.searchBox}>
          <span className={styles.searchIcon}>🔍</span>
          <input type="text" placeholder="Search..." />
        </div>
        
        <div className={styles.userControls}>
          <button className={styles.notificationButton}>
            🔔
          </button>
          <div className={styles.userProfile}>
            <span className={styles.userAvatar}>👤</span>
            <span className={styles.userName}>User</span>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header; 