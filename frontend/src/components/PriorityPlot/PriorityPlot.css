/* Priority Plot Container */
.priority-plot-container {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  position: relative;
}

.priority-plot-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start; /* Changed from center to flex-start to prevent overlapping */
  margin-bottom: 20px;
  flex-wrap: wrap; /* Allow wrapping on smaller screens */
  gap: 10px; /* Add gap between wrapped elements */
}

.priority-plot-title {
  display: flex;
  flex-direction: column;
  max-width: 60%;
}

.priority-plot-header h1 {
  font-size: 22px; /* Slightly smaller font size */
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.2;
}

.selected-file-name {
  font-size: 16px; /* Smaller font size */
  font-weight: 400;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%; /* Use full width of the container */
  margin-top: 4px;
}

.error-banner {
  background-color: #fee2e2;
  border: 1px solid #ef4444;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-message {
  color: #b91c1c;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  line-height: 1.5;
}

/* Initiative Detail Modal */
.initiative-detail-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2500; /* Increased z-index to be above all other elements */
}

.initiative-detail-modal {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  display: flex;
  flex-direction: column;
}

.initiative-detail-close-button {
  position: absolute;
  top: 12px;
  right: 12px;
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  padding: 4px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.initiative-detail-close-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #333;
}

.initiative-detail-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e0e0e0;
}

.initiative-detail-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0;
  padding-right: 24px;
}

.initiative-detail-content {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
  max-height: calc(90vh - 80px); /* Subtract header height to ensure content fits in viewport */
}

.initiative-detail-section {
  margin-bottom: 24px;
}

.initiative-detail-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
}

.initiative-detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.initiative-detail-field {
  display: flex;
  flex-direction: column;
}

.initiative-detail-field label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.initiative-detail-field div {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.initiative-detail-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 12px;
}

.initiative-detail-metric {
  padding: 12px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.initiative-detail-metric label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.initiative-detail-metric-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.initiative-detail-reasoning {
  background-color: #f9fafb;
  padding: 16px;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  white-space: pre-wrap;
}

.priority-plot-actions {
  display: flex;
  gap: 10px;
}

.download-button, .refresh-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #0078d4;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600; /* Make text bolder */
  transition: background-color 0.2s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Add subtle shadow for depth */
}

.download-button:hover, .refresh-button:hover {
  background-color: #106ebe;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2); /* Enhance shadow on hover */
}

.download-button {
  background-color: #4a9eff; /* Lighter blue color */
  letter-spacing: 0.3px; /* Improve text readability */
  font-weight: 600; /* Slightly less bold */
  text-shadow: none; /* Remove text shadow for better readability */
}

.download-button:hover {
  background-color: #3a8eef;
}

.refresh-button {
  background-color: #107c10;
}

.refresh-button:hover {
  background-color: #0b5a0b;
}

.refresh-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.priority-plot-content {
  display: flex;
  flex-direction: row; /* Changed to row to have initiatives on left and chart on right */
  gap: 20px;
  flex: 1;
}

.priority-plot-left-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 40%; /* Set width to 40% for initiative list */
  min-width: 350px; /* Ensure minimum width */
  overflow-y: auto; /* Allow scrolling for long lists */
}

.priority-plot-right-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 60%; /* Set width to 60% for chart */
  flex: 1;
}

.chart-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
  height: 550px; /* Increased height for better visibility */
  min-height: 450px;
  display: flex;
  flex-direction: column;
}

.priority-plot-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 16px;
}

.priority-plot-card h2 {
  font-size: 18px;
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 16px;
  color: #333;
}

.chart-container {
  flex: 1;
  position: relative;
  width: 100%;
  min-height: 450px;
  margin-top: 10px;
}

.file-list-container {
  max-height: 300px;
  overflow-y: auto;
}

.files-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  overflow: hidden;
}

.file-icon {
  color: #0078d4;
  flex-shrink: 0;
}

.file-name {
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: color 0.2s, background-color 0.2s;
}

.action-button:hover {
  background-color: #f0f0f0;
  color: #0078d4;
}

.action-button.download-button:hover {
  color: #0078d4;
}

.action-icon {
  width: 16px;
  height: 16px;
}

.no-files {
  padding: 32px 16px;
  text-align: center;
}

.no-files h3 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.no-files p {
  color: #666;
  margin: 0;
}

.processing-message {
  margin-top: 12px;
  text-align: center;
  color: #0078d4;
}

.error-message {
  margin-top: 12px;
  text-align: center;
  color: #d13438;
}

.loading-message {
  padding: 16px;
  text-align: center;
  color: #666;
}

.no-initiatives {
  padding: 32px 16px;
  text-align: center;
}

.no-initiatives h3 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.no-initiatives p {
  color: #666;
  margin: 0;
}

.initiatives-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 600px; /* Taller list to match screenshot */
  overflow-y: auto;
  padding-right: 8px;
}

.initiative-item {
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  background-color: #f9f9f9;
  position: relative;
}

.initiative-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.initiative-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.initiative-header h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #333;
  line-height: 1.3;
  flex: 1;
}

.initiative-actions {
  display: flex;
  gap: 4px;
  margin-left: 8px;
  flex-shrink: 0;
}

.initiative-action-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: color 0.2s, background-color 0.2s;
}

.initiative-action-button:hover {
  background-color: #f0f0f0;
  color: #0078d4;
}

.initiative-dimension {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.initiative-dimension span {
  font-weight: 600;
  margin-right: 4px;
}

.initiative-expanded-content {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #eee;
  font-size: 14px;
}

.initiative-expanded-content h4 {
  font-size: 14px;
  font-weight: 600;
  margin: 8px 0 4px 0;
  color: #333;
}

.initiative-expanded-content p {
  margin: 0 0 12px 0;
  color: #555;
  line-height: 1.4;
}

.initiative-metrics {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  background-color: rgba(255, 255, 255, 0.7);
  padding: 10px;
  border-radius: 6px;
  border-top: 1px solid #eee; /* Add separator line */
  margin-top: 8px;
}

.initiative-metrics > div {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.initiative-metrics label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.initiative-metrics div div {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.impact-value {
  font-size: 18px !important;
  font-weight: 700 !important;
  color: #e74c3c !important;
}

/* Priority Plot Button */
.priority-plot-button {
  background-color: #0078d4;
  color: white;
}

.priority-plot-button:hover {
  background-color: #106ebe;
}

/* Main Priority Plot Button */
.main-priority-plot-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  background-color: #0078d4;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.main-priority-plot-button:hover {
  background-color: #106ebe;
}

/* Priority Plot Modal */
.priority-plot-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000; /* Increased z-index to be above the top bar */
}

.priority-plot-modal {
  background-color: white;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  max-width: 100vw;
  max-height: 100vh;
}

.priority-plot-modal-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f8f8;
}

.priority-plot-modal-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #323130;
}

.priority-plot-close-button {
  background: none;
  border: none;
  cursor: pointer;
  color: #605e5c;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.priority-plot-close-button.absolute-close {
  position: absolute;
  top: 60px; /* Increased top position to be below the top bar */
  right: 20px;
  z-index: 2001; /* Ensure it's above the modal overlay */
  background: none;
  width: 40px;
  height: 40px;
  color: #333;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.priority-plot-close-button:hover {
  color: #e41e3f;
}

.priority-plot-close-button:active {
  color: #c41e3f;
}

.priority-plot-modal-content {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.priority-plot-iframe {
  width: 100%;
  height: 100%;
  border: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.priority-plot-iframe.loaded {
  opacity: 1;
}

.priority-plot-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: white;
}

.processing-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.processing-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  background-color: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.processing-message span {
  font-size: 16px;
  font-weight: 500;
  color: #0078d4;
}

.priority-plot-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f2f1;
  border-top: 4px solid #0078d4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Media queries for responsive design */
@media (max-width: 1024px) {
  .priority-plot-modal-header {
    padding: 12px 16px;
  }

  .priority-plot-close-button.absolute-close {
    top: 40px;
    right: 16px;
  }
}

@media (max-width: 768px) {
  .priority-plot-modal-header {
    padding: 10px 12px;
  }

  .priority-plot-modal-header h2 {
    font-size: 18px;
  }

  .priority-plot-close-button.absolute-close {
    top: 30px;
    right: 12px;
    width: 36px;
    height: 36px;
  }
}

@media (max-width: 480px) {
  .priority-plot-modal-header {
    padding: 8px 10px;
  }

  .priority-plot-modal-header h2 {
    font-size: 16px;
  }

  .priority-plot-close-button.absolute-close {
    top: 24px;
    right: 10px;
    width: 32px;
    height: 32px;
  }

  .priority-plot-close-button.absolute-close svg {
    width: 24px;
    height: 24px;
  }
}

/* Initiative Edit Form Styles */
.initiative-edit-form {
  padding: 16px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.initiative-edit-fields {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.initiative-edit-field {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.initiative-edit-field label {
  font-size: 12px;
  font-weight: 500;
  color: #666;
}

.initiative-edit-field input,
.initiative-edit-field textarea {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.initiative-edit-field textarea {
  resize: vertical;
  min-height: 80px;
}

.initiative-edit-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.initiative-edit-color {
  display: flex;
  align-items: center;
  gap: 8px;
}

.initiative-edit-color input[type="color"] {
  width: 40px;
  height: 40px;
  padding: 0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
}

.initiative-edit-hint {
  font-size: 11px;
  color: #888;
  margin-top: 4px;
}

.initiative-edit-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
}

.initiative-edit-cancel,
.initiative-edit-save {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
}

.initiative-edit-cancel {
  background-color: #f0f0f0;
  color: #666;
}

.initiative-edit-cancel:hover {
  background-color: #e0e0e0;
  color: #333;
}

.initiative-edit-save {
  background-color: #4caf50;
  color: white;
}

.initiative-edit-save:hover {
  background-color: #3d9140;
}

/* New Initiative Button */
.new-initiative-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  padding: 10px;
  background-color: #f5f5f5;
  border: 1px dashed #ccc;
  border-radius: 6px;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 16px;
}

.new-initiative-button:hover {
  background-color: #e8e8e8;
  border-color: #aaa;
  color: #333;
}

/* Plot Legend Styles */
.plot-legend {
  background-color: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.plot-legend h3 {
  font-size: 20px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 16px;
}

.plot-legend-items {
  display: flex;
  justify-content: center;
  gap: 64px;
}

.plot-legend-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.plot-legend-item span {
  font-size: 16px;
  font-weight: 500;
}

.plot-legend-gradient {
  display: flex;
  width: 128px;
  height: 32px;
  border: 1px solid #ddd;
}

.plot-legend-gradient-segment {
  flex: 1;
  background-color: black;
}

.plot-legend-circles {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  height: 64px;
}

.plot-legend-circle {
  background-color: #4caf50;
  border-radius: 50%;
}

.plot-legend-circle.small {
  width: 16px;
  height: 16px;
}

.plot-legend-circle.medium {
  width: 32px;
  height: 32px;
}

.plot-legend-circle.large {
  width: 64px;
  height: 64px;
}
