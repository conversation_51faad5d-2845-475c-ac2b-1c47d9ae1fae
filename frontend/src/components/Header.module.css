.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  background-color: white;
  border-bottom: 1px solid #eaeaea;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.titleArea h2 {
  margin: 0;
  font-size: 22px;
  font-weight: 500;
  color: #333;
}

.actions {
  display: flex;
  align-items: center;
}

.searchBox {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 8px 16px;
  margin-right: 20px;
}

.searchIcon {
  margin-right: 8px;
  color: #666;
}

.searchBox input {
  border: none;
  background: transparent;
  outline: none;
  width: 200px;
  font-size: 14px;
}

.userControls {
  display: flex;
  align-items: center;
}

.notificationButton {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  margin-right: 16px;
  padding: 5px;
}

.userProfile {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 5px;
}

.userAvatar {
  font-size: 18px;
  margin-right: 8px;
}

.userName {
  font-size: 14px;
  font-weight: 500;
} 