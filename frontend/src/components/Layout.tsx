import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Sidebar from './Sidebar';
import Header from './Header';
import styles from './Layout.module.css';

// Define basic project interface
interface Project {
  id: string;
  name: string;
  description: string;
  // Add other fields as needed
}

const Layout: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const [project, setProject] = useState<Project | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    // Load project data
    const loadProject = async () => {
      setIsLoading(true);
      try {
        // First try to get from local storage (from demo data)
        const storedProjects = JSON.parse(localStorage.getItem('projects') || '[]');
        const foundProject = storedProjects.find((p: Project) => p.id === projectId);
        
        if (foundProject) {
          setProject(foundProject);
        } else {
          // For demo projects
          const demoProjects = [
            { id: 'project1', name: 'MDM company X', description: 'AI-powered analytics for marketing campaigns' },
            { id: 'project2', name: 'AI maturity company Y', description: 'Financial data analysis and reporting' },
            { id: 'project3', name: 'Scoping company C DIvision 1', description: 'AI assistant for customer inquiries' },
            { id: 'project4', name: 'Scoping company C Division 2', description: 'Research findings and analysis' },
          ];
          
          const demoProject = demoProjects.find(p => p.id === projectId);
          
          if (demoProject) {
            setProject(demoProject);
          } else {
            setError('Project not found');
          }
        }
      } catch (error) {
        console.error('Error loading project:', error);
        setError('Failed to load project data');
      } finally {
        setIsLoading(false);
      }
    };

    if (projectId) {
      loadProject();
    } else {
      setError('No project ID provided');
      setIsLoading(false);
    }
  }, [projectId]);

  if (isLoading) {
    return <div className={styles.loadingContainer}>Loading project...</div>;
  }

  if (error || !project) {
    return (
      <div className={styles.errorContainer}>
        <h2>{error || 'Project not found'}</h2>
        <button onClick={() => navigate('/projects')}>Back to Projects</button>
      </div>
    );
  }

  return (
    <div className={styles.layoutContainer}>
      <Sidebar project={project} />
      <div className={styles.mainContent}>
        <Header project={project} />
        <div className={styles.contentArea}>
          <h1>{project.name}</h1>
          <p>{project.description}</p>
          {/* Content will be rendered here */}
          <div className={styles.projectContent}>
            {/* Project components will be loaded here */}
            <p>Project dashboard and content will be displayed here.</p>
            <p>Environment variables are not required for this basic view.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Layout; 