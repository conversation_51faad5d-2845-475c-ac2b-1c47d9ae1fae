import React, { ReactNode } from 'react';
import { useUser } from '../state/UserProvider';
import { UserRole, getRolePermissions, RolePermissions } from '../models/roles';
import { MessageBar, MessageBarType, Text, Stack } from '@fluentui/react';

interface RoleBasedAccessProps {
  allowedRoles: UserRole[];
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * Component that renders its children only if the current user has one of the allowed roles
 */
export const RoleBasedAccess: React.FC<RoleBasedAccessProps> = ({
  allowedRoles,
  children,
  fallback
}) => {
  const { currentUser } = useUser();

  // If no user is logged in or user role is not in allowed roles, show fallback or access denied
  if (!currentUser || !allowedRoles.includes(currentUser.role)) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <MessageBar
        messageBarType={MessageBarType.error}
        isMultiline={true}
      >
        <Text>You do not have permission to access this content.</Text>
      </MessageBar>
    );
  }

  // User has an allowed role, render children
  return <>{children}</>;
};

interface PermissionBasedAccessProps {
  requiredPermission: keyof RolePermissions;
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * Component that renders its children only if the current user has the required permission
 */
export const PermissionBasedAccess: React.FC<PermissionBasedAccessProps> = ({
  requiredPermission,
  children,
  fallback
}) => {
  const { currentUser } = useUser();

  // Use permissions from the user context if available, otherwise fall back to role-based permissions
  const permissions = currentUser?.permissions ||
    (currentUser ? getRolePermissions(currentUser.role) : getRolePermissions(UserRole.REGULAR_USER));

  // If user doesn't have the required permission, show fallback or access denied
  if (!permissions[requiredPermission]) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <MessageBar
        messageBarType={MessageBarType.error}
        isMultiline={true}
      >
        <Text>You do not have permission to access this content.</Text>
      </MessageBar>
    );
  }

  // User has the required permission, render children
  return <>{children}</>;
};

/**
 * Component that renders a full-page access denied message
 */
export const AccessDeniedPage: React.FC<{ message?: string }> = ({
  message = 'You do not have permission to access this page. Please contact your administrator if you believe this is an error.'
}) => {
  return (
    <Stack
      horizontalAlign="center"
      verticalAlign="center"
      styles={{ root: { height: '80vh', padding: 20 } }}
    >
      <Stack
        tokens={{ childrenGap: 16 }}
        styles={{ root: { maxWidth: 600, textAlign: 'center' } }}
      >
        <Text variant="xxLarge" styles={{ root: { fontWeight: 600 } }}>
          Access Denied
        </Text>
        <Text variant="large">
          {message}
        </Text>
      </Stack>
    </Stack>
  );
};
