/* frontend/src/components/common/Button.module.css */
.uploadButtonRoot {
  width: 200px;
  border-radius: 4px;
  height: 32px;
  background: radial-gradient(109.81% 107.82% at 100.1% 90.19%, #0f6cbd 33.63%,#2d87c3 70.31%,#8dddd8 100%);
  color: white;
  padding: 8px 16px;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px; /* Add gap to space between icon and text */
}

.uploadButtonRoot .ms-Button-icon {
  color: white;
  margin-right: 8px; /* Increase margin to the right of the icon */
}

.uploadButtonRoot span {
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: #ffffff;
}

.shareButtonRoot {
  width: 86px;
  height: 32px;
  border-radius: 4px;
  background: radial-gradient(109.81% 107.82% at 100.1% 90.19%, #0f6cbd 33.63%, #2d87c3 70.31%, #8dddd8 100%);
  padding: 5px 12px;
}

.shareButtonRoot:hover {
  background: linear-gradient(135deg, #0f6cbd 0%, #2d87c3 51.04%, #8dddd8 100%);
}

.shareButtonRoot span {
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: #ffffff;
}

.shareButtonRoot i,
.shareButtonRoot:hover i {
  color: #ffffff !important;
}

.historyButtonRoot {
  width: 180px;
  border: 1px solid #d1d1d1;
}

.historyButtonRoot:hover {
  border: 1px solid #d1d1d1;
}

.historyButtonRoot:active {
  border: 1px solid #d1d1d1;
}

@media (max-width: 480px) {
  .shareButtonRoot {
    width: auto;
    padding: 5px 8px;
  }

  .historyButtonRoot {
    width: auto;
    padding: 0 8px;
  }
  .uploadButtonRoot {
    width: auto;
    padding: 0 8px;
  }
}
