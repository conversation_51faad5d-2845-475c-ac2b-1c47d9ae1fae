// frontend/src/state/AppProvider.tsx
import React, {
  createContext,
  ReactNode,
  useEffect,
  useReducer
} from 'react'

import {
  ChatHistoryLoadingState,
  Conversation,
  CosmosDBHealth,
  CosmosDBStatus,
  Feedback,
  FrontendSettings,
  frontendSettings,
  historyEnsure,
  historyList
} from '../api'

import { appStateReducer } from './AppReducer'
import { msalInstance, msalReady } from '../auth/msal-config'
import { useState } from 'react'

export interface AppState {
  isChatHistoryOpen: boolean
  chatHistoryLoadingState: ChatHistoryLoadingState
  isCosmosDBAvailable: CosmosDBHealth
  chatHistory: Conversation[] | null
  filteredChatHistory: Conversation[] | null
  currentChat: Conversation | null
  frontendSettings: FrontendSettings | null
  feedbackState: { [answerId: string]: Feedback.Neutral | Feedback.Positive | Feedback.Negative }
  isLoading: boolean;
  answerExecResult: { [answerId: string]: [] }
}

export type Action =
  | { type: 'TOGGLE_CHAT_HISTORY' }
  | { type: 'SET_COSMOSDB_STATUS'; payload: CosmosDBHealth }
  | { type: 'UPDATE_CHAT_HISTORY_LOADING_STATE'; payload: ChatHistoryLoadingState }
  | { type: 'UPDATE_CURRENT_CHAT'; payload: Conversation | null }
  | { type: 'UPDATE_FILTERED_CHAT_HISTORY'; payload: Conversation[] | null }
  | { type: 'UPDATE_CHAT_HISTORY'; payload: Conversation }
  | { type: 'UPDATE_CHAT_TITLE'; payload: Conversation }
  | { type: 'DELETE_CHAT_ENTRY'; payload: string }
  | { type: 'DELETE_CHAT_HISTORY' }
  | { type: 'DELETE_CURRENT_CHAT_MESSAGES'; payload: string }
  | { type: 'FETCH_CHAT_HISTORY'; payload: Conversation[] | null }
  | { type: 'FETCH_FRONTEND_SETTINGS'; payload: FrontendSettings | null }
  | {
    type: 'SET_FEEDBACK_STATE'
    payload: { answerId: string; feedback: Feedback.Positive | Feedback.Negative | Feedback.Neutral }
  }
  | { type: 'GET_FEEDBACK_STATE'; payload: string }
  | { type: 'SET_ANSWER_EXEC_RESULT'; payload: { answerId: string, exec_result: [] } }

const initialState: AppState = {
  isChatHistoryOpen: false,
  chatHistoryLoadingState: ChatHistoryLoadingState.Loading,
  chatHistory: null,
  filteredChatHistory: null,
  currentChat: null,
  isCosmosDBAvailable: {
    cosmosDB: false,
    status: CosmosDBStatus.NotConfigured
  },
  frontendSettings: null,
  feedbackState: {},
  isLoading: true,
  answerExecResult: {},
}

export const AppStateContext = createContext<
  | {
    state: AppState
    dispatch: React.Dispatch<Action>
  }
  | undefined
>(undefined)

type AppStateProviderProps = {
  children: ReactNode
}

export const AppStateProvider: React.FC<AppStateProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(appStateReducer, initialState)

  // Track if we've already initialized the app
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    const initializeApp = async () => {
      // Only initialize once
      if (isInitialized) {
        console.log('App already initialized, skipping initialization');
        return;
      }

      try {
        console.log('Starting app initialization...');
        
        // Wait for MSAL to be fully initialized
        console.log('Waiting for MSAL initialization...');
        await msalReady;
        console.log('MSAL initialization completed');

        // Check authentication state
        const accounts = msalInstance.getAllAccounts();
        console.log('MSAL accounts found:', accounts.length);
        
        if (accounts.length > 0) {
          const account = accounts[0];
          console.log('Active account:', account.username);
          msalInstance.setActiveAccount(account);
          localStorage.setItem('isAuthenticated', 'true');
          localStorage.setItem('msalAccount', JSON.stringify(account));
        } else {
          console.log('No active accounts found, user is not authenticated');
          localStorage.removeItem('isAuthenticated');
          localStorage.removeItem('msalAccount');
        }

        // Fetch frontend settings
        console.log('Starting frontend settings initialization...');
        const settings = await frontendSettings();
        console.log('Raw frontend settings:', settings);
        
        // Mark initialization as complete
        setIsInitialized(true);

        if (!settings) {
          console.error('Frontend settings fetch failed - settings is null');
          return;
        }

        if (!settings.azure_storage) {
          console.error('Azure Storage configuration is missing from settings:', settings);
          return;
        }

        // Validate Azure Storage settings
        const { container_name, account_name, container_sas_token } = settings.azure_storage;
        console.log('Azure Storage Configuration:', {
          hasContainerName: Boolean(container_name),
          containerName: container_name || '[empty]',
          hasAccountName: Boolean(account_name),
          accountName: account_name || '[empty]',
          hasSasToken: Boolean(container_sas_token),
          sasTokenLength: container_sas_token?.length || 0,
          sasTokenPrefix: container_sas_token ? `${container_sas_token.slice(0, 10)}...` : '[empty]'
        });

        // Only dispatch if we have valid settings
        if (container_name && account_name && container_sas_token) {
          console.log('Dispatching valid frontend settings to state');
          dispatch({ type: 'FETCH_FRONTEND_SETTINGS', payload: settings });
        } else {
          const missing = [];
          if (!container_name) missing.push('container_name');
          if (!account_name) missing.push('account_name');
          if (!container_sas_token) missing.push('container_sas_token');
          console.error('Invalid Azure Storage configuration - missing:', missing.join(', '));
        }

        // Then fetch chat history
        // Note: In the AppProvider, we don't have access to the ProjectContext yet
        // Project-specific filtering will happen in the ChatHistoryListItem component
        const fetchChatHistory = async (offset = 0): Promise<Conversation[] | null> => {
          const result = await historyList(offset)
            .then(response => {
              if (response) {
                dispatch({ type: 'FETCH_CHAT_HISTORY', payload: response })
              } else {
                dispatch({ type: 'FETCH_CHAT_HISTORY', payload: null })
              }
              return response
            })
            .catch(_err => {
              dispatch({ type: 'UPDATE_CHAT_HISTORY_LOADING_STATE', payload: ChatHistoryLoadingState.Fail })
              return null
            })
          return result
        }

        // Check CosmosDB availability
        const response = await historyEnsure()
        dispatch({ type: 'SET_COSMOSDB_STATUS', payload: response })
        if (response.cosmosDB) {
          await fetchChatHistory()
        }
        dispatch({ type: 'UPDATE_CHAT_HISTORY_LOADING_STATE', payload: ChatHistoryLoadingState.Success })
      } catch (error) {
        console.error('Error initializing app:', error);
        dispatch({ type: 'UPDATE_CHAT_HISTORY_LOADING_STATE', payload: ChatHistoryLoadingState.Fail })
      }
    }

    initializeApp();
  }, [])

  return <AppStateContext.Provider value={{ state, dispatch }}>{children}</AppStateContext.Provider>
}
