import { PublicClientApplication, AccountInfo, AuthenticationResult, InteractionRequiredAuthError } from '@azure/msal-browser';
import { msalInstance } from '../auth/msal-config';
import { navigateToProjects, navigateToLogin } from '../utils/navigation';
import axios, { AxiosError } from 'axios';

// Custom error classes for better error handling
class AppAuthError extends Error {
  constructor(message: string, public readonly code?: string, public readonly originalError?: unknown) {
    super(message);
    this.name = 'AppAuthError';
    Object.setPrototypeOf(this, AppAuthError.prototype);
  }
}

class NetworkError extends AppAuthError {
  constructor(message: string, originalError?: unknown) {
    super(message, 'NETWORK_ERROR', originalError);
    this.name = 'NetworkError';
    Object.setPrototypeOf(this, NetworkError.prototype);
  }
}

class TokenAcquisitionError extends AppAuthError {
  constructor(message: string, originalError?: unknown) {
    super(message, 'TOKEN_ACQUISITION_FAILED', originalError);
    this.name = 'TokenAcquisitionError';
    Object.setPrototypeOf(this, TokenAcquisitionError.prototype);
  }
}

class TokenVerificationError extends AppAuthError {
  constructor(message: string, originalError?: unknown) {
    super(message, 'TOKEN_VERIFICATION_FAILED', originalError);
    this.name = 'TokenVerificationError';
    Object.setPrototypeOf(this, TokenVerificationError.prototype);
  }
}

export class AuthService {
  private instance: PublicClientApplication;
  private navigateToProjects: () => void;
  private navigateToLogin: () => void;
  private retryCount: number = 0;
  private maxRetries: number = 3;
  private isHandlingFailure: boolean = false;

  constructor(instance: PublicClientApplication) {
    this.instance = instance;
    this.navigateToProjects = navigateToProjects;
    this.navigateToLogin = navigateToLogin;
  }

  public async handleAuthentication(): Promise<void> {
    try {
      console.log('Starting authentication flow...');
      const accounts = this.instance.getAllAccounts();
      
      if (accounts.length === 0) {
        console.log('No accounts found, redirecting to login');
        this.navigateToLogin();
        return;
      }

      const activeAccount = accounts[0];
      console.log(`Authenticating user: ${activeAccount.username}`);
      
      try {
        const token = await this.acquireTokenWithFallback(activeAccount);
        
        if (token) {
          const isVerified = await this.verifyTokenWithBackend(token);
          
          if (isVerified) {
            this.instance.setActiveAccount(activeAccount);
            localStorage.setItem('isAuthenticated', 'true');
            localStorage.setItem('msalAccount', JSON.stringify(activeAccount));
            localStorage.setItem('accessToken', token);
            document.cookie = `msalToken=${token}; Secure; SameSite=Strict; Path=/`;
            
            console.log('Authentication successful, navigating to projects');
            this.navigateToProjects();
            return;
          }
        }
      } catch (error) {
        await this.handleAuthFailure(
          new TokenAcquisitionError('Failed to acquire or verify token', error)
        );
        return;
      }
      
      await this.handleAuthFailure(new AppAuthError('Authentication failed'));
    } catch (error) {
      console.error('Unexpected error during authentication:', error);
      await this.handleAuthFailure(
        error instanceof Error 
          ? error 
          : new AppAuthError('An unknown error occurred during authentication')
      );
    }
  }

  public async handleCallback(): Promise<void> {
    try {
      console.log('Handling authentication callback...');
      
      const response = await this.instance.handleRedirectPromise();
      
      if (!response || !response.account) {
        throw new AppAuthError('No account information in redirect response');
      }
      
      console.log(`Received authentication response for user: ${response.account.username}`);
      
      try {
        const token = await this.acquireTokenWithFallback(response.account);
        
        if (!token) {
          throw new TokenAcquisitionError('Failed to acquire token after redirect');
        }
        
        const isVerified = await this.verifyTokenWithBackend(token);
        
        if (!isVerified) {
          throw new TokenVerificationError('Backend token verification failed');
        }
        
        this.instance.setActiveAccount(response.account);
        localStorage.setItem('isAuthenticated', 'true');
        localStorage.setItem('msalAccount', JSON.stringify(response.account));
        localStorage.setItem('accessToken', token);
        document.cookie = `msalToken=${token}; Secure; SameSite=Strict; Path=/`;
        
        console.log('Callback processing successful, navigating to projects');
        this.navigateToProjects();
      } catch (error) {
        await this.handleAuthFailure(
          error instanceof Error 
            ? error 
            : new AppAuthError('Failed to process authentication callback', undefined, error)
        );
      }
    } catch (error) {
      console.error('Error handling authentication callback:', error);
      await this.handleAuthFailure(
        error instanceof Error 
          ? error 
          : new AppAuthError('An unknown error occurred during callback processing', undefined, error)
      );
    }
  }

  private async acquireTokenWithFallback(account: AccountInfo): Promise<string | null> {
    const scopes = ['User.Read', 'openid', 'profile', 'offline_access'];
    
    try {
      console.log('Attempting silent token acquisition...');
      
      const silentResponse = await this.instance.acquireTokenSilent({
        account,
        scopes,
        forceRefresh: false
      });
      
      if (silentResponse?.accessToken) {
        console.log('Successfully acquired token silently');
        return silentResponse.accessToken;
      }
      
      throw new TokenAcquisitionError('No access token in silent response');
    } catch (silentError) {
      console.warn('Silent token acquisition failed, trying popup...', silentError);
      
      try {
        console.log('Attempting interactive token acquisition...');
        
        const popupResponse = await this.instance.acquireTokenPopup({
          account,
          scopes,
          prompt: 'select_account'
        });
        
        if (popupResponse?.accessToken) {
          console.log('Successfully acquired token via popup');
          return popupResponse.accessToken;
        }
        
        throw new TokenAcquisitionError('No access token in popup response');
      } catch (popupError) {
        console.error('Popup token acquisition failed:', popupError);
        throw new TokenAcquisitionError('Failed to acquire token via popup', popupError);
      }
    }
  }

  private async verifyTokenWithBackend(token: string): Promise<boolean> {
    const maxAttempts = 3;
    let attempt = 0;
    const baseDelay = 1000;
    const backendUrl = process.env.REACT_APP_BACKEND_URL || 'http://localhost:3001';
    
    while (attempt < maxAttempts) {
      try {
        const response = await axios.post(
          `${backendUrl}/api/auth/verify-token`,
          { token },
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            timeout: 5000
          }
        );
        
        if (response.status === 200 && response.data.valid) {
          return true;
        }
      } catch (error) {
        console.warn(`Token verification attempt ${attempt + 1} failed:`, error);
        
        if (attempt === maxAttempts - 1) {
          throw new TokenVerificationError(
            'Token verification failed after multiple attempts',
            error
          );
        }
        
        const delay = baseDelay * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
      
      attempt++;
    }
    
    return false;
  }

  private async clearAuthState(): Promise<void> {
    try {
      localStorage.removeItem('isAuthenticated');
      localStorage.removeItem('msalAccount');
      localStorage.removeItem('accessToken');
      
      document.cookie = 'msalToken=; Max-Age=0; Path=/; Secure; SameSite=Strict';
      
      const accounts = this.instance.getAllAccounts();
      if (accounts.length > 0) {
        try {
          await this.instance.logoutPopup({
            account: accounts[0],
            postLogoutRedirectUri: window.location.origin
          });
        } catch (error) {
          console.warn('Error during logout:', error);
        }
      }
      
      this.instance.setActiveAccount(null);
    } catch (error) {
      console.error('Error clearing auth state:', error);
    }
  }

  private async handleAuthFailure(error: Error): Promise<void> {
    if (this.isHandlingFailure) {
      console.warn('Already handling auth failure, skipping duplicate handling');
      return;
    }
    
    this.isHandlingFailure = true;
    
    try {
      console.error('Authentication failure:', error);
      
      if (this.retryCount < this.maxRetries) {
        this.retryCount++;
        const delay = Math.pow(2, this.retryCount) * 1000;
        
        console.log(`Retrying authentication in ${delay / 1000} seconds (attempt ${this.retryCount}/${this.maxRetries})`);
        
        await new Promise(resolve => setTimeout(resolve, delay));
        
        try {
          await this.handleAuthentication();
          return;
        } catch (retryError) {
          console.error('Retry attempt failed:', retryError);
        }
      }
      
      console.error('Max retries reached, clearing auth state and redirecting to login');
      
      await this.clearAuthState();
      this.navigateToLogin();
    } finally {
      this.isHandlingFailure = false;
    }
  }

  public async login(): Promise<void> {
    try {
      await this.handleAuthentication();
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  }

  public async logout(): Promise<void> {
    try {
      await this.clearAuthState();
      this.navigateToLogin();
    } catch (error) {
      console.error('Error during logout:', error);
      throw error;
    }
  }

  public getAccessToken(): string | null {
    return localStorage.getItem('accessToken');
  }

  public isAuthenticated(): boolean {
    return localStorage.getItem('isAuthenticated') === 'true' && !!this.getAccessToken();
  }

  public async getAuthHeaders(): Promise<Record<string, string>> {
    const token = this.getAccessToken();
    if (!token) {
      throw new AppAuthError('No access token available');
    }
    
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  }
}

export const authService = new AuthService(msalInstance);
