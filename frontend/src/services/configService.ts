import axios from 'axios';

export interface StorageConfig {
  account_name: string;
  container_name: string;
  container_sas_token: string;
}

export interface FrontendConfig {
  auth_enabled?: boolean;
  feedback_enabled?: boolean;
  ui?: {
    title: string;
    chat_title: string;
    chat_description: string;
    logo?: string;
    chat_logo?: string;
    show_share_button?: boolean;
    show_chat_history_button?: boolean;
  };
  sanitize_answer?: boolean;
  azure_storage?: StorageConfig;
}

let cachedConfig: FrontendConfig | null = null;
let configPromise: Promise<FrontendConfig> | null = null;

const defaultConfig: FrontendConfig = {
  auth_enabled: false,
  feedback_enabled: false,
  sanitize_answer: false,
  azure_storage: {
    account_name: '',
    container_name: '',
    container_sas_token: ''
  }
};

export const fetchConfig = async (): Promise<FrontendConfig> => {
  console.log('fetchConfig: Starting configuration fetch');
  
  // Return existing promise if we're already fetching
  if (configPromise) {
    console.log('fetchConfig: Using existing promise');
    return configPromise;
  }

  // Return cached config if available
  if (cachedConfig) {
    console.log('fetchConfig: Using cached config:', {
      hasAzureStorage: Boolean(cachedConfig.azure_storage),
      config: cachedConfig
    });
    return cachedConfig;
  }

  // Create new promise for fetching config
  configPromise = new Promise<FrontendConfig>(async (resolve, reject) => {
    try {
      console.log('fetchConfig: Making request to /frontend_settings');
      const response = await axios.get('/frontend_settings');
      
      console.log('fetchConfig: Received response:', {
        status: response.status,
        hasData: Boolean(response.data),
        data: response.data
      });

      if (!response.data) {
        throw new Error('No configuration data received');
      }
      
      // Validate and normalize the storage configuration
      const config = response.data;
      console.log('fetchConfig: Processing storage config:', {
        rawConfig: config,
        hasAzureStorage: Boolean(config.azure_storage)
      });

      if (config.azure_storage) {
        config.azure_storage = {
          account_name: config.azure_storage.account_name || '',
          container_name: config.azure_storage.container_name || '',
          container_sas_token: config.azure_storage.container_sas_token || ''
        };
        
        console.log('fetchConfig: Normalized storage configuration:', {
          hasAccountName: Boolean(config.azure_storage.account_name),
          hasContainerName: Boolean(config.azure_storage.container_name),
          hasSasToken: Boolean(config.azure_storage.container_sas_token),
          accountName: config.azure_storage.account_name,
          containerName: config.azure_storage.container_name,
          tokenLength: config.azure_storage.container_sas_token?.length || 0
        });
      } else {
        console.warn('fetchConfig: No azure_storage configuration found in response');
      }
      
      cachedConfig = config;
      resolve(config);
    } catch (error) {
      console.error('fetchConfig: Error fetching frontend settings:', error);
      if (axios.isAxiosError(error)) {
        console.error('fetchConfig: Axios error details:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data
        });
      }
      resolve(defaultConfig); // Resolve with default config on error
    } finally {
      configPromise = null; // Clear the promise
    }
  });

  return configPromise;
};

export const getStorageConfig = async (): Promise<StorageConfig> => {
  console.log('getStorageConfig: Starting storage config retrieval');
  const config = await fetchConfig();
  
  console.log('getStorageConfig: Retrieved config:', {
    hasConfig: Boolean(config),
    hasAzureStorage: Boolean(config.azure_storage),
    storageConfig: config.azure_storage
  });

  if (!config.azure_storage?.account_name || 
      !config.azure_storage?.container_name || 
      !config.azure_storage?.container_sas_token) {
    console.warn('getStorageConfig: Incomplete storage configuration:', {
      hasAccountName: Boolean(config.azure_storage?.account_name),
      hasContainerName: Boolean(config.azure_storage?.container_name),
      hasSasToken: Boolean(config.azure_storage?.container_sas_token)
    });
  }
  
  const storageConfig = {
    account_name: config.azure_storage?.account_name || '',
    container_name: config.azure_storage?.container_name || '',
    container_sas_token: config.azure_storage?.container_sas_token || ''
  };

  console.log('getStorageConfig: Returning storage config:', {
    hasAccountName: Boolean(storageConfig.account_name),
    hasContainerName: Boolean(storageConfig.container_name),
    hasSasToken: Boolean(storageConfig.container_sas_token),
    accountName: storageConfig.account_name,
    containerName: storageConfig.container_name
  });

  return storageConfig;
};

export const clearConfigCache = () => {
  cachedConfig = null;
  configPromise = null;
}; 