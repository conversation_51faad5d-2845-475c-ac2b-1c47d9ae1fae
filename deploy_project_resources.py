#!/usr/bin/env python3
"""
Deploy project resources using Bicep templates and ACR-based Function App.
This script is called by app.py when a new project is created.

This script is responsible for:
1. Deploying the core infrastructure via Bicep (storage, search, etc.)
2. Deploying the Function App from ACR
3. Updating the deployment status in CosmosDB
"""

import os
import sys
import json
import time
import logging
import asyncio
import subprocess
import uuid
from datetime import datetime, timezone, timedelta

# Import update_project_with_deployment function
try:
    from update_project_with_deployment import update_project_with_deployment
except ImportError:
    logging.warning("update_project_with_deployment module not found. Project updates with deployment data will be disabled.")
    update_project_with_deployment = None

# Import requests with error handling
try:
    import requests
except ImportError:
    requests = None
    logging.warning("requests module not found. API status updates will be disabled.")

# Constants for ACR deployment
ACR_NAME = "functionappaiscope"
FUNCTIONS_CONTAINER_IMAGE_NAME = "functionapp"
FUNCTIONS_CONTAINER_IMAGE_TAG = "latest"
SHARED_SEARCH_SERVICE_NAME = "search-shared-service"
SHARED_OPENAI_SERVICE_NAME = "your-openai-service"

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,  # Changed to DEBUG level for more detailed logs
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# Create a file handler for detailed logs
os.makedirs('logs', exist_ok=True)
detailed_log_file = f"logs/deploy_project_resources_detailed_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
file_handler = logging.FileHandler(detailed_log_file)
file_handler.setLevel(logging.DEBUG)
file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(file_formatter)
logging.getLogger().addHandler(file_handler)

logging.info(f"Detailed logs will be written to {detailed_log_file}")

# ACR Configuration
ACR_NAME = os.environ.get("ACR_NAME", "functionappaiscope")
FUNCTIONS_CONTAINER_IMAGE_NAME = os.environ.get("FUNCTIONS_CONTAINER_IMAGE_NAME", "functionapp")
FUNCTIONS_CONTAINER_IMAGE_TAG = os.environ.get("FUNCTIONS_CONTAINER_IMAGE_TAG", "latest")
SHARED_SEARCH_SERVICE_NAME = os.environ.get("SHARED_SEARCH_SERVICE_NAME", "")
SHARED_OPENAI_SERVICE_NAME = os.environ.get("SHARED_OPENAI_SERVICE_NAME", "openai-service")

async def update_deployment_status(project_id, status_data, api_url="http://localhost:50505"):
    """
    Update the deployment status via the API endpoint.

    Args:
        project_id (str): The ID of the project
        status_data (dict): Dictionary containing the status update
        api_url (str): The base URL of the API
    """
    update_url = f"{api_url}/api/projects/{project_id}/deployment-status"

    try:
        # Add headers to indicate we're using Azure CLI credentials
        headers = {
            "Content-Type": "application/json",
            "X-Azure-CLI-Credentials": "true"
        }

        # Send the update
        logging.info(f"Sending status update to {update_url}")

        try:
            if requests is None:
                logging.warning(f"Requests module not available - skipping API update to {update_url}")
                logging.info(f"Status data that would have been sent: {status_data}")
                return True  # Return True to allow deployment to continue

            response = requests.post(update_url, json=status_data, headers=headers, timeout=5)

            if response.status_code != 200:
                logging.error(f"Error updating deployment status: {response.status_code} - {response.text}")
                return False
            else:
                logging.info(f"Successfully updated deployment status: {response.json()}")
                return True
        except Exception as e:
            if requests is not None and hasattr(requests, 'exceptions') and isinstance(e, requests.exceptions.ConnectionError):
                logging.warning(f"Could not connect to API at {update_url} - continuing without status updates")
                # Log the status data for debugging
                logging.info(f"Status data that would have been sent: {status_data}")
                return True  # Return True to allow deployment to continue
            else:
                logging.error(f"Error making request to update deployment status: {e}")
                return False
    except Exception as e:
        logging.error(f"Error updating deployment status: {e}")
        return False


async def update_project_resources(project_id, resource_data, api_url="http://localhost:50505"):
    """
    Update the project in CosmosDB with the actual resource names from Azure deployment.

    Args:
        project_id (str): The ID of the project
        resource_data (dict): Dictionary containing the resource names
        api_url (str): The base URL of the API
    """
    update_url = f"{api_url}/api/projects/{project_id}/resources"

    try:
        # Add headers to indicate we're using Azure CLI credentials
        headers = {
            "Content-Type": "application/json",
            "X-Azure-CLI-Credentials": "true"
        }

        # Send the update
        logging.info(f"Sending resource update to {update_url}")
        response = requests.post(update_url, json=resource_data, headers=headers)

        if response.status_code != 200:
            logging.error(f"Error updating project resources: {response.status_code} - {response.text}")
            return False
        else:
            logging.info(f"Successfully updated project resources: {response.json()}")
            return True
    except Exception as e:
        logging.error(f"Error updating project resources: {e}")
        return False


def run_main_bicep_deployment(project_id, project_name, region_id, resource_group="rg-internal-ai", location="westeurope",
                       function_app_id=None, template_file=None, deploy_event_grid_only=False, storage_account_id=None):
    """
    Runs the main Bicep deployment or a specific template deployment.
    Returns the deployment outputs.

    Args:
        project_id (str): The ID of the project
        project_name (str): The name of the project
        region_id (str): The Azure region ID (for tagging)
        resource_group (str): The resource group name
        location (str): The Azure region to deploy to
        function_app_id (str, optional): The resource ID of the function app to connect to the event grid system topic
        template_file (str, optional): The specific template file to deploy (default: None, which uses the default template)
        deploy_event_grid_only (bool, optional): Whether to deploy only the event grid system topic (default: False)
        storage_account_id (str, optional): The storage account ID to use for the event grid system topic (required if deploy_event_grid_only is True)
    """
    logging.info(f"Starting Bicep deployment for project {project_id} in RG {resource_group}")
    if deploy_event_grid_only:
        logging.info("Deploying only the event grid system topic")

    script_dir = os.path.dirname(os.path.abspath(__file__))
    deploy_script = os.path.join(script_dir, "deploy_project_resources.sh")

    # Make sure the script is executable
    try:
        os.chmod(deploy_script, 0o755)
    except Exception as e:
        error_msg = f"Error making deployment script executable: {e}"
        logging.error(error_msg)
        raise Exception(error_msg)

    # Build the command
    cmd = [
        deploy_script,
        project_id,
        project_name,
        "--region-id",
        region_id
    ]

    # Add function app ID if provided
    if function_app_id:
        cmd.extend(["--function-app-id", function_app_id])
        logging.info(f"Function app ID provided: {function_app_id}")
    else:
        logging.info("No function app ID provided. Event grid system topic will be created without a subscription to a function app.")

    # Add template file if provided
    if template_file:
        cmd.extend(["--template-file", template_file])
        logging.info(f"Using template file: {template_file}")

    # Add storage account ID if provided (for event grid system topic deployment)
    if deploy_event_grid_only and storage_account_id:
        cmd.extend(["--storage-account-id", storage_account_id])
        logging.info(f"Using storage account ID: {storage_account_id}")
    elif deploy_event_grid_only and not storage_account_id:
        error_msg = "Storage account ID is required when deploying only the event grid system topic"
        logging.error(error_msg)
        raise Exception(error_msg)

    # Set environment variables for the script
    env = os.environ.copy()
    env["RESOURCE_GROUP"] = resource_group
    env["LOCATION"] = location

    logging.info(f"Running command: {' '.join(cmd)}")

    try:
        # Log the command and environment variables
        logging.debug(f"Command to execute: {' '.join(cmd)}")
        logging.debug(f"Environment variables: RESOURCE_GROUP={resource_group}, LOCATION={location}")

        # Run the deployment script and capture output
        logging.info(f"Starting subprocess for Bicep deployment at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        process = subprocess.run(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            env=env,
            check=False
        )
        logging.info(f"Subprocess completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} with return code: {process.returncode}")

        # Save the full output to log files for detailed analysis
        stdout_log_file = f"logs/deployment_stdout_{project_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        stderr_log_file = f"logs/deployment_stderr_{project_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

        with open(stdout_log_file, 'w') as f:
            f.write(process.stdout)
        logging.info(f"Saved full stdout to {stdout_log_file}")

        with open(stderr_log_file, 'w') as f:
            f.write(process.stderr if process.stderr else "No stderr output")
        logging.info(f"Saved full stderr to {stderr_log_file}")

        # Log summary of the output
        stdout_lines = process.stdout.splitlines()
        stderr_lines = process.stderr.splitlines() if process.stderr else []

        logging.debug(f"Stdout has {len(stdout_lines)} lines")
        if len(stdout_lines) > 0:
            logging.debug(f"First 5 lines of stdout: {stdout_lines[:5]}")
            logging.debug(f"Last 5 lines of stdout: {stdout_lines[-5:] if len(stdout_lines) >= 5 else stdout_lines}")

        logging.debug(f"Stderr has {len(stderr_lines)} lines")
        if len(stderr_lines) > 0:
            logging.debug(f"First 5 lines of stderr: {stderr_lines[:5]}")
            logging.debug(f"Last 5 lines of stderr: {stderr_lines[-5:] if len(stderr_lines) >= 5 else stderr_lines}")

        # Check if resources were actually created despite a non-zero return code
        resources_created = False
        resource_data = {}

        # Extract resource names from the output regardless of return code
        for line in process.stdout.splitlines():
            if "Storage Account:" in line and "not found" not in line.lower():
                storage_account_name = line.split("Storage Account:")[1].strip()
                if storage_account_name:
                    resource_data["storage_account_name"] = storage_account_name
                    logging.info(f"Found storage account name in output: {storage_account_name}")
            elif "Search Service:" in line and "not found" not in line.lower():
                search_service_name = line.split("Search Service:")[1].strip()
                if search_service_name:
                    resource_data["search_service_name"] = search_service_name
                    logging.info(f"Found search service name in output: {search_service_name}")
            elif "Function App:" in line and "URL" not in line and "not found" not in line.lower():
                function_app_name = line.split("Function App:")[1].strip()
                if function_app_name:
                    resource_data["function_app_name"] = function_app_name
                    logging.info(f"Found function app name in output: {function_app_name}")

        # Check if critical resources exist in Azure
        if "storage_account_name" in resource_data and resource_data["storage_account_name"]:
            try:
                # Check if storage account exists
                check_storage_cmd = [
                    "az", "storage", "account", "show",
                    "--name", resource_data["storage_account_name"],
                    "--resource-group", resource_group,
                    "--query", "name",
                    "-o", "tsv"
                ]
                storage_check = subprocess.run(
                    check_storage_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    check=False
                )
                if storage_check.returncode == 0 and storage_check.stdout.strip():
                    logging.info(f"Verified storage account exists: {resource_data['storage_account_name']}")
                    resources_created = True
                else:
                    logging.warning(f"Storage account {resource_data['storage_account_name']} not found in Azure")
            except Exception as e:
                logging.warning(f"Error checking storage account: {e}")

        if "search_service_name" in resource_data and resource_data["search_service_name"]:
            try:
                # Check if search service exists
                check_search_cmd = [
                    "az", "search", "service", "show",
                    "--name", resource_data["search_service_name"],
                    "--resource-group", resource_group,
                    "--query", "name",
                    "-o", "tsv"
                ]
                search_check = subprocess.run(
                    check_search_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    check=False
                )
                if search_check.returncode == 0 and search_check.stdout.strip():
                    logging.info(f"Verified search service exists: {resource_data['search_service_name']}")
                    resources_created = True
                else:
                    logging.warning(f"Search service {resource_data['search_service_name']} not found in Azure")
            except Exception as e:
                logging.warning(f"Error checking search service: {e}")

        # If resources were created but script returned non-zero, consider it a success
        if process.returncode != 0:
            error_message = f"Main Bicep deployment script returned non-zero code: {process.returncode}"
            if process.stderr:
                error_message += f"\nStderr summary: {process.stderr[:500]}..." if len(process.stderr) > 500 else f"\nStderr: {process.stderr}"

            if resources_created:
                logging.warning(error_message)
                logging.warning("However, resources were successfully created in Azure. Continuing deployment.")
                logging.warning(f"See full logs in {stdout_log_file} and {stderr_log_file}")
            else:
                logging.error(error_message)
                logging.error(f"See full logs in {stdout_log_file} and {stderr_log_file}")
                raise Exception(error_message)

        logging.info(f"Main Bicep deployment successful for project {project_id}")

        # Extract resource names from the output
        resource_data = {}
        for line in process.stdout.splitlines():
            if "Storage Account:" in line:
                resource_data["storage_account_name"] = line.split("Storage Account:")[1].strip()
                logging.info(f"Extracted storage account name: {resource_data['storage_account_name']}")
            elif "Search Service:" in line:
                resource_data["search_service_name"] = line.split("Search Service:")[1].strip()
                logging.info(f"Extracted search service name: {resource_data['search_service_name']}")
            elif "Function App:" in line and "URL" not in line:
                resource_data["function_app_name"] = line.split("Function App:")[1].strip()
                logging.info(f"Extracted function app name: {resource_data['function_app_name']}")

        # Look for JSON output markers in the stdout
        json_start_marker = "JSON_OUTPUT_START"
        json_end_marker = "JSON_OUTPUT_END"

        json_start = process.stdout.find(json_start_marker)
        json_end = process.stdout.find(json_end_marker)

        if json_start >= 0 and json_end > json_start:
            # Extract the JSON between the markers
            json_content = process.stdout[json_start + len(json_start_marker):json_end].strip()
            try:
                deployment_outputs = json.loads(json_content)
                logging.info(f"Successfully parsed JSON output from deployment script")

                # Extract resource names from the JSON output if available
                if "resources" in deployment_outputs:
                    resources = deployment_outputs["resources"]
                    if "storage_account_name" in resources:
                        resource_data["storage_account_name"] = resources["storage_account_name"]
                    if "search_service_name" in resources:
                        resource_data["search_service_name"] = resources["search_service_name"]
                    if "function_app_name" in resources:
                        resource_data["function_app_name"] = resources["function_app_name"]

                return deployment_outputs, resource_data
            except json.JSONDecodeError as e:
                logging.warning(f"Could not parse JSON output between markers: {e}")

        # Fallback: Try to find any JSON in the output
        try:
            # Look for JSON output in the stdout
            json_start = process.stdout.find("{")
            json_end = process.stdout.rfind("}")
            if json_start >= 0 and json_end > json_start:
                json_str = process.stdout[json_start:json_end+1]
                try:
                    deployment_outputs = json.loads(json_str)
                    logging.info(f"Successfully parsed JSON output from deployment script (fallback method)")
                    return deployment_outputs, resource_data
                except json.JSONDecodeError:
                    logging.warning(f"Could not parse JSON output from main Bicep deployment (fallback method)")
            else:
                logging.warning("Could not find JSON output in deployment script stdout")

            # If we got here, we couldn't parse JSON but deployment was successful
            # Return what we have
            return {}, resource_data
        except Exception as json_error:
            logging.warning(f"Error parsing JSON output: {json_error}")
            return {}, resource_data
    except Exception as e:
        error_message = f"Error running main Bicep deployment: {e}"
        logging.error(error_message)
        raise Exception(error_message)


def generate_deployment_summary(project_id, project_name, region_id, resource_group, start_time, resource_data, main_bicep_outputs, status="success", auto_update_project=True):
    """
    Generate a deployment summary JSON file with all the deployment information and optionally update the project in CosmosDB.

    Args:
        project_id (str): The ID of the project
        project_name (str): The name of the project
        region_id (str): The Azure region ID
        resource_group (str): The resource group name
        start_time (datetime): The start time of the deployment
        resource_data (dict): Dictionary containing resource names from deployment
        main_bicep_outputs (dict): The outputs from the main Bicep deployment
        status (str): The deployment status (success, partial_success, or failed)
        auto_update_project (bool): Whether to automatically update the project in CosmosDB

    Returns:
        str: Path to the generated summary file
    """
    logging.info(f"Generating deployment summary for project {project_id} with status {status}")

    # Calculate deployment time
    end_time = datetime.now(timezone.utc)
    deployment_time = end_time - start_time
    deployment_time_str = f"{deployment_time.total_seconds():.1f}s"

    # Initialize summary dictionary
    summary = {
        "project_id": project_id,
        "project_name": project_name,
        "region_id": region_id,
        "resources": {
            "storage_account_name": "",
            "storage_account_sas_token": "",
            "uploads_container": "",
            "input_container": "",
            "output_container": "",
            "search_service_name": "",
            "search_index_name": "",
            "search_indexer_name": "",
            "search_key": "",
            "search_api_version": "2021-04-30-Preview",
            "search_datasource_name": "",
            "function_app_name": "",
            "function_app_url": "",
            "function_key_maturity": "",
            "function_key_executive_summary": "",
            "function_key_powerpoint": "",
            "event_grid_system_topic_name": "",
            "event_grid_subscription_name": "",
            "azure_function_maturity_assessment_url": "",
            "azure_function_executive_summary_url": ""
        },
        "status": status,
        "deployment_time": deployment_time_str,
        "timestamp": end_time.strftime("%Y-%m-%dT%H:%M:%SZ")
    }

    # Fill in resource data primarily from main_bicep_outputs['resources'] (using snake_case keys from shell script JSON)
    bicep_resources = main_bicep_outputs.get("resources", {})
    logging.debug(f"Populating summary from main_bicep_outputs resources: {bicep_resources}")
    summary["resources"]["storage_account_name"] = bicep_resources.get("storage_account_name", "")
    summary["resources"]["uploads_container"] = bicep_resources.get("uploads_container", "")
    summary["resources"]["input_container"] = bicep_resources.get("input_container", "")
    summary["resources"]["output_container"] = bicep_resources.get("output_container", "")
    summary["resources"]["search_service_name"] = bicep_resources.get("search_service_name", "")
    summary["resources"]["search_index_name"] = bicep_resources.get("search_index_name", "")
    summary["resources"]["search_indexer_name"] = bicep_resources.get("search_indexer_name", "")
    summary["resources"]["search_datasource_name"] = bicep_resources.get("search_datasource_name", "")
    # Note: search_key is typically not an output of the main bicep, retrieved later if needed

    # Fill in/overwrite with data collected during the Python script execution (resource_data)
    # This is important for Function App and Event Grid details populated after the main Bicep run
    logging.debug(f"Populating/overwriting summary from resource_data: {resource_data}")
    if resource_data.get("storage_account_name"):
         summary["resources"]["storage_account_name"] = resource_data["storage_account_name"]
    if resource_data.get("search_service_name"):
         summary["resources"]["search_service_name"] = resource_data["search_service_name"]
    if resource_data.get("function_app_name"):
        summary["resources"]["function_app_name"] = resource_data["function_app_name"]
        summary["resources"]["function_app_url"] = f"https://{resource_data['function_app_name']}.azurewebsites.net"
    if resource_data.get("event_grid_system_topic_name"):
        summary["resources"]["event_grid_system_topic_name"] = resource_data["event_grid_system_topic_name"]
    if resource_data.get("event_grid_subscription_name"):
        summary["resources"]["event_grid_subscription_name"] = resource_data["event_grid_subscription_name"]

    # Generate SAS token for the main project storage account
    if summary["resources"]["storage_account_name"]:
        try:
            # Generate SAS token with all permissions for all services, valid for 1 year
            expiry_date = (datetime.now(timezone.utc) + timedelta(days=365)).strftime("%Y-%m-%dT%H:%M:%SZ")
            cmd = [
                "az", "storage", "account", "generate-sas",
                "--account-name", summary["resources"]["storage_account_name"],
                "--resource-types", "sco",
                "--services", "bfqt",
                "--permissions", "rwdlacuptf",
                "--expiry", expiry_date,
                "--https-only",
                "--output", "tsv"
            ]
            process = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, check=False)
            if process.returncode == 0 and process.stdout.strip():
                sas_token = process.stdout.strip()
                summary["resources"]["storage_account_sas_token"] = sas_token
                logging.info(f"Generated SAS token for storage account {summary['resources']['storage_account_name']}")
            else:
                logging.warning(f"Failed to generate SAS token: {process.stderr}")
        except Exception as e:
            logging.error(f"Error generating SAS token: {e}")

    # Get function keys if function app exists
    if summary["resources"]["function_app_name"]:
        try:
            # Use correct function names as provided by user
            function_names = ["HttpTriggerAppMaturityAssessment", "HttpTriggerAppExecutiveSummary", "HttpTriggerPowerPointGenerator"]
            function_key_fields = ["function_key_maturity", "function_key_executive_summary", "function_key_powerpoint"]
            function_url_fields = ["azure_function_maturity_assessment_url", "azure_function_executive_summary_url", None]
            function_endpoints = ["HttpTriggerAppMaturityAssessment", "HttpTriggerAppExecutiveSummary", None]

            # Ensure search key is populated (might be needed by functions, retrieve if not in bicep outputs)
            if not summary["resources"]["search_key"] and summary["resources"]["search_service_name"]:
                 try:
                     logging.info(f"Retrieving search key for summary for service: {summary['resources']['search_service_name']}")
                     search_key_cmd = [
                         "az", "search", "admin-key", "show",
                         "--service-name", summary["resources"]["search_service_name"],
                         "--resource-group", resource_group,
                         "--query", "primaryKey", "-o", "tsv"
                     ]
                     search_key_proc = subprocess.run(search_key_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, check=False)
                     if search_key_proc.returncode == 0 and search_key_proc.stdout.strip():
                         summary["resources"]["search_key"] = search_key_proc.stdout.strip()
                         logging.info("Successfully retrieved search key for summary.")
                     else:
                         logging.warning(f"Could not retrieve search key for summary: {search_key_proc.stderr}")
                 except Exception as sk_e:
                     logging.warning(f"Error retrieving search key for summary: {sk_e}")


            logging.info(f"Attempting to retrieve function keys for {summary['resources']['function_app_name']}...")
            for i, function_name in enumerate(function_names):
                logging.debug(f"Retrieving key for function: {function_name}")
                cmd = [
                    "az", "functionapp", "function", "keys", "list",
                    "--name", summary["resources"]["function_app_name"],
                    "--resource-group", resource_group,
                    "--function-name", function_name,
                    "--query", "default",
                    "--output", "tsv"
                ]
                process = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, check=False)
                if process.returncode == 0 and process.stdout.strip():
                    function_key = process.stdout.strip()
                    summary["resources"][function_key_fields[i]] = function_key
                    logging.info(f"Retrieved function key for {function_name}")

                    # Also set the function URL if applicable
                    if function_url_fields[i] and function_endpoints[i]:
                        function_url = f"https://{summary['resources']['function_app_name']}.azurewebsites.net/api/{function_endpoints[i]}"
                        summary["resources"][function_url_fields[i]] = function_url
                        logging.info(f"Set function URL for {function_name}: {function_url}")
                elif process.returncode != 0 and "NotFound" in process.stderr:
                    logging.warning(f"Function '{function_name}' not found in Function App '{summary['resources']['function_app_name']}'. Skipping key retrieval.")
                    summary["resources"][function_key_fields[i]] = "N/A (Function not found)"
                else:
                    # Log other errors more generally
                    logging.warning(f"Failed to retrieve function key for {function_name}. Return code: {process.returncode}. Stderr: {process.stderr.strip()}")
                    summary["resources"][function_key_fields[i]] = "Error retrieving key"
        except Exception as e:
            logging.error(f"Exception occurred while retrieving function keys: {e}")

    # Create a timestamp for the file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Write summary to file with timestamp
    summary_file_path = f"logs/deployment_summary_{timestamp}_{project_id}.json"
    os.makedirs('logs', exist_ok=True)

    with open(summary_file_path, 'w') as f:
        json.dump(summary, f, indent=2)

    logging.info(f"Deployment summary written to {summary_file_path}")

    # Also save a copy to the standard location without timestamp for easier access
    standard_summary_path = f"deployment_summary_{project_id}.json"
    with open(standard_summary_path, 'w') as f:
        json.dump(summary, f, indent=2)

    logging.info(f"Deployment summary also written to {standard_summary_path}")

    # Automatically update the project in CosmosDB if requested
    if auto_update_project:
        try:
            # Import the update_project_with_deployment function
            try:
                from update_project_with_deployment import update_project_with_deployment
                logging.info(f"Automatically updating project {project_id} with deployment summary")
                success = update_project_with_deployment(project_id, summary)
                if success:
                    logging.info(f"Successfully updated project {project_id} with deployment summary")
                else:
                    logging.error(f"Failed to update project {project_id} with deployment summary")
            except ImportError:
                logging.warning("update_project_with_deployment module not found. Project updates with deployment data will be disabled.")
        except Exception as e:
            logging.error(f"Error updating project with deployment summary: {e}")

    return summary_file_path


def check_function_app_exists(project_id, resource_group):
    """
    Check if a Function App exists for the given project ID.

    Args:
        project_id (str): The ID of the project
        resource_group (str): The resource group name

    Returns:
        str: The name of the Function App if found, None otherwise
    """
    logging.info(f"Checking if Function App exists for project {project_id}")

    try:
        # Run az functionapp list command
        cmd = [
            "az", "functionapp", "list",
            "--resource-group", resource_group,
            "--query", f"[?contains(name, '{project_id}')].name",
            "-o", "tsv"
        ]

        process = subprocess.run(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            check=False
        )

        if process.returncode == 0 and process.stdout.strip():
            function_app_name = process.stdout.strip()
            logging.info(f"Found Function App: {function_app_name}")
            return function_app_name
        else:
            logging.warning(f"No Function App found for project {project_id}")
            return None

    except Exception as e:
        logging.error(f"Error checking Function App existence: {e}")
        return None


def deploy_acr_function_app(project_id, project_name, resource_group, location, main_bicep_outputs, resource_data):
    """
    Deploy the Function App from ACR.

    Args:
        project_id (str): The ID of the project
        project_name (str): The name of the project
        resource_group (str): The resource group name
        location (str): The Azure region
        main_bicep_outputs (dict): The outputs from the main Bicep deployment
        resource_data (dict): Dictionary containing resource names from main deployment

    Returns:
        str: The name of the deployed Function App
    """
    logging.info(f"Starting ACR Function App deployment for project {project_id}")
    logging.debug(f"ACR Function App deployment parameters:")
    logging.debug(f"  project_id: {project_id}")
    logging.debug(f"  project_name: {project_name}")
    logging.debug(f"  resource_group: {resource_group}")
    logging.debug(f"  location: {location}")
    logging.debug(f"  main_bicep_outputs keys: {list(main_bicep_outputs.keys()) if main_bicep_outputs else 'None'}")
    logging.debug(f"  resource_data: {resource_data}")

    script_dir = os.path.dirname(os.path.abspath(__file__))
    acr_script_path = os.path.join(script_dir, "scripts/ACR_deployment/deploy_function_app_from_acr.sh")
    # acr_params_file_path is the template, temp_acr_params_file_path is what we generate and use
    temp_acr_params_file_path = os.path.join(script_dir, f"scripts/ACR_deployment/parameters_{project_id}.json")

    logging.info(f"Starting ACR Function App deployment for project {project_id}")

    # Make sure the ACR deployment script is executable
    try:
        os.chmod(acr_script_path, 0o755)
    except Exception as e:
        error_msg = f"Error making ACR deployment script executable: {e}"
        logging.error(error_msg)
        raise Exception(error_msg)

    # 1. Gather parameters for the ACR script's parameters.json

    # Generate Function App name and related names
    random_suffix = uuid.uuid4().hex[:4]
    function_app_name = f"func-{project_id}-{random_suffix}"
    app_service_plan_name = f"plan-{function_app_name}"

    # Generate Function App's dedicated storage account name
    # Ensure it's compliant: lowercase, no hyphens, 3-24 chars.
    clean_project_id_for_storage = project_id.replace('-', '').lower()
    func_storage_account_name_base = f"stfunc{clean_project_id_for_storage}{random_suffix}"
    func_storage_account_name = func_storage_account_name_base[:24] # Max 24 chars
    logging.info(f"Generated Function App name: {function_app_name}")
    logging.info(f"Generated Function App's dedicated storage account name: {func_storage_account_name}")

    # Create the Function App's dedicated storage account
    try:
        logging.info(f"Creating dedicated storage account {func_storage_account_name} for Function App {function_app_name} in RG {resource_group} and location {location}...")
        storage_create_cmd = [
            "az", "storage", "account", "create",
            "--name", func_storage_account_name,
            "--resource-group", resource_group,
            "--location", location,
            "--sku", "Standard_LRS",
            "--kind", "StorageV2",
            "--output", "json"
        ]
        storage_create_process = subprocess.run(storage_create_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, check=False)
        if storage_create_process.returncode != 0:
            logging.error(f"Failed to create dedicated storage account {func_storage_account_name}: {storage_create_process.stderr}")
            # Check if it was due to already existing (though unlikely with unique suffix)
            if "StorageAccountAlreadyTaken" in storage_create_process.stderr or "NameNotAvailable" in storage_create_process.stderr:
                 logging.warning(f"Storage account {func_storage_account_name} might already exist or name is taken. Attempting to proceed.")
            elif "ResourceGroupNotFound" in storage_create_process.stderr:
                logging.error(f"Resource group {resource_group} not found. Cannot create storage account.")
                raise Exception(f"Resource group {resource_group} not found.")
            else:
                raise Exception(f"Failed to create dedicated storage account {func_storage_account_name}: {storage_create_process.stderr}")
        else:
            logging.info(f"Successfully created/verified dedicated storage account {func_storage_account_name}.")
            # Wait a bit for propagation if newly created
            time.sleep(10)


        # Retrieve connection string for the dedicated storage account
        logging.info(f"Retrieving connection string for storage account {func_storage_account_name}...")
        conn_str_cmd = [
            "az", "storage", "account", "show-connection-string",
            "--name", func_storage_account_name,
            "--resource-group", resource_group,
            "--query", "connectionString",
            "-o", "tsv"
        ]
        conn_str_process = subprocess.run(conn_str_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, check=True)
        func_storage_connection_string = conn_str_process.stdout.strip()
        if not func_storage_connection_string:
            raise Exception(f"Failed to retrieve connection string for {func_storage_account_name}")
        logging.info(f"Successfully retrieved connection string for {func_storage_account_name}.")

    except Exception as e:
        logging.error(f"Error during dedicated storage account setup for Function App: {e}")
        raise

    # Retrieve Search API Key
    search_api_key = ""
    main_search_service_name = main_bicep_outputs.get("searchServiceName", {}).get("value", resource_data.get("search_service_name", SHARED_SEARCH_SERVICE_NAME))
    if main_search_service_name and main_search_service_name != SHARED_SEARCH_SERVICE_NAME : # Only get key if not shared or placeholder
        try:
            logging.info(f"Retrieving Search API key for service: {main_search_service_name} in RG {resource_group}...")
            search_key_cmd = [
                "az", "search", "admin-key", "show",
                "--service-name", main_search_service_name,
                "--resource-group", resource_group,
                "--query", "primaryKey",
                "-o", "tsv"
            ]
            search_key_process = subprocess.run(search_key_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, check=False)
            if search_key_process.returncode == 0 and search_key_process.stdout.strip():
                search_api_key = search_key_process.stdout.strip()
                logging.info(f"Successfully retrieved Search API key for {main_search_service_name}.")
            else:
                logging.warning(f"Could not retrieve Search API key for {main_search_service_name}: {search_key_process.stderr}. Proceeding without it for Bicep.")
        except Exception as e:
            logging.warning(f"Error retrieving Search API key: {e}. Proceeding without it for Bicep.")
    else:
        logging.info(f"Using shared/placeholder Search Service: {main_search_service_name}. API key will be placeholder or from Key Vault if Bicep is configured for it.")


    # Retrieve OpenAI API Key (Using SHARED_OPENAI_SERVICE_NAME for now as per existing logic)
    openai_api_key = "" # Placeholder, Bicep should ideally use KeyVault for this
    # The current scripts don't robustly fetch a real OpenAI key here for Bicep,
    # relying on it being set in App Settings later or by Bicep itself (e.g. from KeyVault).
    # For now, we pass a placeholder or expect Bicep to handle it.
    logging.info(f"OpenAI Service Name for Bicep parameters: {SHARED_OPENAI_SERVICE_NAME}. API key will be placeholder or from Key Vault if Bicep is configured for it.")


    # Prepare parameters for scripts/ACR_deployment/function_app_deployment.bicep
    # Extract values from the 'resources' key in the JSON output of the first script
    # The keys in the JSON output from deploy_project_resources.sh are snake_case
    bicep_resources = main_bicep_outputs.get("resources", {})
    logging.debug(f"Extracted resources from main Bicep output for Function App params: {bicep_resources}")

    bicep_func_app_params = {
        "functionAppName": {"value": function_app_name},
        "location": {"value": location},
        "appServicePlanName": {"value": app_service_plan_name},
        "projectId": {"value": project_id},
        "projectName": {"value": project_name},
        # Correctly access nested resource values from the shell script's JSON output
        "uploadsContainer": {"value": bicep_resources.get("uploads_container", "")},
        "inputContainer": {"value": bicep_resources.get("input_container", "")},
        "outputContainer": {"value": bicep_resources.get("output_container", "")},
        "searchIndexName": {"value": bicep_resources.get("search_index_name", "")},
        "searchIndexerName": {"value": bicep_resources.get("search_indexer_name", "")},
        "searchDatasourceName": {"value": bicep_resources.get("search_datasource_name", "")},
        "searchServiceName": {"value": main_search_service_name}, # Already correctly derived above
        "searchApiKey": {"value": search_api_key if search_api_key else "placeholderSearchApiKey"}, # Pass placeholder if not found, Bicep should handle
        "openAiServiceName": {"value": SHARED_OPENAI_SERVICE_NAME}, # From constants
        "openAiApiKey": {"value": openai_api_key if openai_api_key else "placeholderOpenAiApiKey"}, # Pass placeholder
        "openAiModelDeployment": {"value": os.environ.get("SHARED_OPENAI_DEPLOYMENT", "gpt-35-turbo")}, # Example, adjust as needed
        "storageConnectionString": {"value": func_storage_connection_string}, # For Function App's own storage
        "acrName": {"value": ACR_NAME}, # From constants
        "containerImageName": {"value": FUNCTIONS_CONTAINER_IMAGE_NAME}, # From constants
        "containerImageTag": {"value": FUNCTIONS_CONTAINER_IMAGE_TAG} # From constants
    }

    project_params_data_for_shell = {
        "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#",
        "contentVersion": "*******",
        "parameters": bicep_func_app_params
    }

    # Write the temporary parameters file that the shell script will use
    # Ensure the directory exists
    os.makedirs(os.path.join(script_dir, "scripts/ACR_deployment"), exist_ok=True)
    with open(temp_acr_params_file_path, 'w') as f_project_params:
        json.dump(project_params_data_for_shell, f_project_params, indent=2)

    logging.info(f"Generated temporary ACR parameters file with consolidated parameters: {temp_acr_params_file_path}")

    # Prepare environment for the bash script
    script_env = os.environ.copy()
    script_env["TARGET_LOCATION"] = location
    script_env["RESOURCE_GROUP"] = resource_group
    # The shell script will read PARAMETERS_FILE to know which parameters JSON to use for its Bicep deployment
    script_env["PARAMETERS_FILE"] = temp_acr_params_file_path

    try:
        # Set up retry logic for ACR deployment
        max_retries = 3
        retry_count = 0
        success = False

        while retry_count < max_retries and not success:
            retry_count += 1

            # Run the ACR deployment script
            logging.info(f"Running ACR deployment script (attempt {retry_count}/{max_retries}): {acr_script_path}")
            logging.debug(f"ACR deployment environment variables: TARGET_LOCATION={location}, RESOURCE_GROUP={resource_group}, PARAMETERS_FILE={temp_acr_params_file_path}")

            # Verify the parameters file exists
            if not os.path.exists(temp_acr_params_file_path):
                logging.error(f"Parameters file does not exist: {temp_acr_params_file_path}")
                raise FileNotFoundError(f"Parameters file not found: {temp_acr_params_file_path}")

            # Log the content of the parameters file for debugging
            try:
                with open(temp_acr_params_file_path, 'r') as f:
                    params_content = f.read()
                logging.debug(f"Parameters file content: {params_content}")
            except Exception as e:
                logging.warning(f"Could not read parameters file: {e}")

            # Start time for ACR deployment
            acr_start_time = datetime.now()
            logging.info(f"Starting ACR deployment at {acr_start_time.strftime('%Y-%m-%d %H:%M:%S')}")

            # Set a timeout for the subprocess (30 minutes)
            timeout_seconds = 1800  # 30 minutes

            try:
                logging.info(f"Running ACR deployment script with timeout of {timeout_seconds} seconds")
                process_acr = subprocess.run(
                    [acr_script_path],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    env=script_env,
                    check=False,
                    timeout=timeout_seconds
                )
            except subprocess.TimeoutExpired as timeout_error:
                logging.error(f"ACR deployment script timed out after {timeout_seconds} seconds")
                # Save timeout error to log files
                timeout_stdout_log_file = f"logs/acr_deployment_timeout_stdout_{project_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_attempt{retry_count}.log"
                timeout_stderr_log_file = f"logs/acr_deployment_timeout_stderr_{project_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_attempt{retry_count}.log"

                with open(timeout_stdout_log_file, 'w') as f:
                    f.write(timeout_error.stdout if hasattr(timeout_error, 'stdout') and timeout_error.stdout else "No stdout available")
                with open(timeout_stderr_log_file, 'w') as f:
                    f.write(timeout_error.stderr if hasattr(timeout_error, 'stderr') and timeout_error.stderr else "No stderr available")

                logging.info(f"Saved timeout logs to {timeout_stdout_log_file} and {timeout_stderr_log_file}")

                # Check if Function App was created despite the timeout
                function_app_name = check_function_app_exists(project_id, resource_group)
                if function_app_name:
                    logging.warning(f"Function App {function_app_name} exists despite deployment script timeout")
                    return function_app_name

                # If we should retry, continue to the next iteration
                if retry_count < max_retries:
                    logging.warning(f"Retrying ACR deployment (attempt {retry_count+1}/{max_retries}) after timeout")
                    continue
                else:
                    error_message = f"ACR Function App deployment script timed out after {timeout_seconds} seconds for project {project_id} after {max_retries} attempts"
                    logging.error(error_message)
                    raise Exception(error_message)
            except Exception as e:
                logging.error(f"Error executing ACR deployment script: {str(e)}")

                # Check if Function App was created despite the error
                function_app_name = check_function_app_exists(project_id, resource_group)
                if function_app_name:
                    logging.warning(f"Function App {function_app_name} exists despite deployment script error")
                    return function_app_name

                # If we should retry, continue to the next iteration
                if retry_count < max_retries:
                    logging.warning(f"Retrying ACR deployment (attempt {retry_count+1}/{max_retries}) after error: {str(e)}")
                    continue
                else:
                    error_message = f"Error executing ACR deployment script for project {project_id} after {max_retries} attempts: {str(e)}"
                    logging.error(error_message)
                    raise Exception(error_message)

            # End time for ACR deployment
            acr_end_time = datetime.now()
            acr_duration = (acr_end_time - acr_start_time).total_seconds()
            logging.info(f"ACR deployment completed at {acr_end_time.strftime('%Y-%m-%d %H:%M:%S')} (took {acr_duration} seconds)")
            logging.info(f"ACR deployment return code: {process_acr.returncode}")

            # Save ACR deployment output to log files
            acr_stdout_log_file = f"logs/acr_deployment_stdout_{project_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_attempt{retry_count}.log"
            acr_stderr_log_file = f"logs/acr_deployment_stderr_{project_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_attempt{retry_count}.log"

            with open(acr_stdout_log_file, 'w') as f:
                f.write(process_acr.stdout)
            logging.info(f"Saved ACR deployment stdout to {acr_stdout_log_file}")

            with open(acr_stderr_log_file, 'w') as f:
                f.write(process_acr.stderr if process_acr.stderr else "No stderr output")
            logging.info(f"Saved ACR deployment stderr to {acr_stderr_log_file}")

            # Log summary of the output
            acr_stdout_lines = process_acr.stdout.splitlines()
            acr_stderr_lines = process_acr.stderr.splitlines() if process_acr.stderr else []

            logging.debug(f"ACR stdout has {len(acr_stdout_lines)} lines")
            if len(acr_stdout_lines) > 0:
                logging.debug(f"First 5 lines of ACR stdout: {acr_stdout_lines[:5]}")
                logging.debug(f"Last 5 lines of ACR stdout: {acr_stdout_lines[-5:] if len(acr_stdout_lines) >= 5 else acr_stdout_lines}")

            logging.debug(f"ACR stderr has {len(acr_stderr_lines)} lines")
            if len(acr_stderr_lines) > 0:
                logging.debug(f"First 5 lines of ACR stderr: {acr_stderr_lines[:5]}")
                logging.debug(f"Last 5 lines of ACR stderr: {acr_stderr_lines[-5:] if len(acr_stderr_lines) >= 5 else acr_stderr_lines}")

            if process_acr.returncode == 0:
                logging.info(f"ACR Function App deployment script successful for project {project_id} on attempt {retry_count}")
                success = True
                break
            else:
                error_message = f"ACR Function App deployment script failed for project {project_id}. Return code: {process_acr.returncode}"
                if process_acr.stderr:
                    error_message += f"\nStderr summary: {process_acr.stderr[:500]}..." if len(process_acr.stderr) > 500 else f"\nStderr: {process_acr.stderr}"

                # Check if we should retry
                if retry_count < max_retries:
                    logging.warning(f"{error_message}")
                    logging.warning(f"Retrying ACR deployment (attempt {retry_count+1}/{max_retries}) after a short delay...")

                    # Check if Function App was created despite the error
                    function_app_name = check_function_app_exists(project_id, resource_group)
                    if function_app_name:
                        logging.warning(f"Function App {function_app_name} exists despite deployment script failure")
                        return function_app_name

                    # Wait before retrying
                    retry_delay = retry_count * 10  # Exponential backoff
                    logging.info(f"Waiting {retry_delay} seconds before retry...")
                    time.sleep(retry_delay)
                else:
                    logging.error(error_message)
                    logging.error(f"See full logs in {acr_stdout_log_file} and {acr_stderr_log_file}")
                    logging.error(f"Failed after {max_retries} attempts")

                    # Check if Function App was created despite the error
                    function_app_name = check_function_app_exists(project_id, resource_group)
                    if function_app_name:
                        logging.warning(f"Function App {function_app_name} exists despite deployment script failure")
                        return function_app_name

                    raise Exception(error_message)

        logging.info(f"ACR Function App deployment script successful for project {project_id}")

        # Get the Function App name from the output file
        function_app_name = None
        func_app_name_file = "./scripts/ACR_deployment/function_app_name.txt"
        if os.path.exists(func_app_name_file):
            with open(func_app_name_file, 'r') as f:
                function_app_name = f.read().strip()
            logging.info(f"Retrieved Function App Name: {function_app_name} for project {project_id}")
            os.remove(func_app_name_file)  # Clean up
        else:
            logging.warning(f"Could not find {func_app_name_file} to retrieve Function App name for {project_id}")

        return function_app_name

    # The finally block related to original_acr_params_content was removed as it's no longer needed.
    # Cleanup of temp_acr_params_file_path happens implicitly when the script exits or can be added here if needed.
    finally:
        # Clean up temporary project-specific params file
        if os.path.exists(temp_acr_params_file_path):
             try:
                 os.remove(temp_acr_params_file_path)
                 logging.info(f"Cleaned up temporary parameters file: {temp_acr_params_file_path}")
             except Exception as e:
                 logging.warning(f"Could not clean up temporary parameters file {temp_acr_params_file_path}: {e}")


async def deploy_project_resources(project_id, project_name, region_id="westeurope", api_url="http://localhost:50505", resource_group="rg-internal-ai", location="westeurope", function_app_id=None):
    """
    Deploy Azure resources for a project using Bicep templates and ACR-based Function App.

    Args:
        project_id (str): The ID of the project
        project_name (str): The name of the project
        region_id (str): The Azure region ID (for tagging)
        api_url (str): The base URL of the API
        resource_group (str): The resource group name
        location (str): The Azure region to deploy to
        function_app_id (str, optional): The resource ID of the function app to connect to the event grid system topic

    Returns:
        bool: True if deployment was successful, False otherwise
    """
    deployment_success = False
    resource_data = {} # Initialize resource_data
    main_bicep_outputs = {} # Initialize main_bicep_outputs

    # Record deployment start time for calculating total deployment time
    deployment_start_time = datetime.now(timezone.utc)

    try:
        # Update deployment status to "in_progress"
        status_data = {
            "status": "in_progress",
            "message": "Starting infrastructure deployment..."
        }
        await update_deployment_status(project_id, status_data, api_url)
        logging.info("Updated deployment status to 'in_progress'")

        # Step 1: Deploy main Bicep (without Event Grid System Topic)
        try:
            logging.info(f"Starting main Bicep deployment for project {project_id}")

            # First deploy the main infrastructure without event grid
            main_bicep_outputs, resource_data = run_main_bicep_deployment(
                project_id,
                project_name,
                region_id,
                resource_group,
                location,
                None  # No function app ID yet
            )

            # Log the outputs and resource data for debugging
            logging.info(f"Main Bicep deployment outputs: {json.dumps(main_bicep_outputs, indent=2)}")
            logging.info(f"Resource data extracted: {json.dumps(resource_data, indent=2)}")

            # Update deployment status after main infrastructure deployment
            status_data = {
                "status": "infrastructure_complete",
                "message": "Main infrastructure deployed successfully.",
                "details": {
                    "infrastructure_complete": True
                }
            }
            await update_deployment_status(project_id, status_data, api_url)

            # Update project resources in CosmosDB
            if resource_data:
                update_result = await update_project_resources(project_id, resource_data, api_url)
                if update_result:
                    logging.info(f"Successfully updated project {project_id} with resource names in CosmosDB")
                else:
                    logging.warning(f"Failed to update project {project_id} with resource names in CosmosDB, but continuing")

            # Mark deployment as successful since the main infrastructure was deployed
            deployment_success = True

            # Step 2: Deploy ACR Function App
            try:
                logging.info(f"Starting ACR Function App deployment for project {project_id}")
                status_data = {
                    "status": "function_app_deployment",
                    "message": "Main infrastructure deployed. Starting Function App deployment.",
                    "details": {
                        "infrastructure_complete": True,
                        "function_app_deployment_started": True
                    }
                }
                await update_deployment_status(project_id, status_data, api_url)

                deployed_function_app_name = deploy_acr_function_app(
                    project_id,
                    project_name,
                    resource_group,
                    location,
                    main_bicep_outputs,
                    resource_data
                )

                if deployed_function_app_name:
                    # Update resource data with function app name
                    resource_data["function_app_name"] = deployed_function_app_name
                    resource_data["function_app_url"] = f"https://{deployed_function_app_name}.azurewebsites.net"

                    # Get the function app ID
                    retrieved_function_app_id = None
                    try:
                        cmd = [
                            "az", "functionapp", "show",
                            "--name", deployed_function_app_name,
                            "--resource-group", resource_group,
                            "--query", "id",
                            "-o", "tsv"
                        ]
                        process = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, check=False)
                        if process.returncode == 0 and process.stdout.strip():
                            retrieved_function_app_id = process.stdout.strip()
                            logging.info(f"Retrieved function app ID: {retrieved_function_app_id}")
                        else:
                            logging.warning(f"Could not retrieve function app ID: {process.stderr}")
                    except Exception as e:
                        logging.error(f"Error retrieving function app ID: {e}")

                    # Step 3: Deploy Event Grid System Topic (only if function app ID was retrieved)
                    event_grid_complete = False
                    max_event_grid_retries = 3
                    event_grid_retry_count = 0

                    if retrieved_function_app_id:
                        logging.info(f"Attempting to deploy event grid system topic and subscription using function app ID: {retrieved_function_app_id}")
                        storage_account_id = None
                        if "storage_account_name" in resource_data and resource_data["storage_account_name"]:
                            try:
                                cmd = [
                                    "az", "storage", "account", "show",
                                    "--name", resource_data["storage_account_name"],
                                    "--resource-group", resource_group,
                                    "--query", "id",
                                    "-o", "tsv"
                                ]
                                process = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, check=False)
                                if process.returncode == 0 and process.stdout.strip():
                                    storage_account_id = process.stdout.strip()
                                    logging.info(f"Retrieved storage account ID: {storage_account_id}")
                                else:
                                    logging.warning(f"Could not retrieve storage account ID: {process.stderr}")

                                    # Try to get storage account ID using a different method
                                    logging.info("Trying alternative method to get storage account ID...")
                                    cmd = [
                                        "az", "storage", "account", "list",
                                        "--resource-group", resource_group,
                                        "--query", f"[?name=='{resource_data['storage_account_name']}'].id",
                                        "-o", "tsv"
                                    ]
                                    process = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, check=False)
                                    if process.returncode == 0 and process.stdout.strip():
                                        storage_account_id = process.stdout.strip()
                                        logging.info(f"Retrieved storage account ID using alternative method: {storage_account_id}")
                            except Exception as e:
                                logging.error(f"Error retrieving storage account ID: {e}")

                            if storage_account_id:
                                # Retry loop for event grid deployment
                                while event_grid_retry_count < max_event_grid_retries and not event_grid_complete:
                                    event_grid_retry_count += 1
                                    logging.info(f"Attempting event grid deployment (attempt {event_grid_retry_count}/{max_event_grid_retries})...")

                                    try:
                                        # Verify function app exists and is running before deploying event grid
                                        logging.info(f"Verifying function app {deployed_function_app_name} exists and is running...")
                                        func_app_check_cmd = [
                                            "az", "functionapp", "show",
                                            "--name", deployed_function_app_name,
                                            "--resource-group", resource_group,
                                            "--query", "state",
                                            "-o", "tsv"
                                        ]
                                        func_app_check = subprocess.run(func_app_check_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, check=False)
                                        if func_app_check.returncode == 0 and func_app_check.stdout.strip() == "Running":
                                            logging.info(f"Function app {deployed_function_app_name} is running")
                                        else:
                                            logging.warning(f"Function app {deployed_function_app_name} is not in Running state: {func_app_check.stdout.strip()}")
                                            if event_grid_retry_count < max_event_grid_retries:
                                                logging.info(f"Waiting 30 seconds before retry {event_grid_retry_count + 1}...")
                                                time.sleep(30)
                                                continue

                                        # We assume the EventGridTriggerBlobIndexer function already exists in the function app
                                        # We don't need to verify or create it, just proceed with event grid deployment
                                        logging.info(f"Proceeding with event grid deployment assuming EventGridTriggerBlobIndexer function exists in function app {deployed_function_app_name}...")

                                        # Generate event grid system topic name
                                        sanitized_name = project_name.lower().replace(' ', '-')
                                        # Remove any characters that are not alphanumeric or hyphens
                                        import re
                                        sanitized_name = re.sub(r'[^a-z0-9-]', '', sanitized_name)
                                        # Generate a unique suffix
                                        import hashlib
                                        unique_suffix = hashlib.md5(project_id.encode()).hexdigest()[:4]
                                        event_grid_system_topic_name = f"evgt-{sanitized_name}-{unique_suffix}"
                                        logging.info(f"Generated event grid system topic name: {event_grid_system_topic_name}")

                                        # Create a temporary parameters file with the event grid system topic name
                                        import tempfile
                                        params_file = tempfile.NamedTemporaryFile(delete=False, suffix='.json')
                                        params_file_path = params_file.name
                                        params_data = {
                                            "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#",
                                            "contentVersion": "*******",
                                            "parameters": {
                                                "eventGridSystemTopicName": {"value": event_grid_system_topic_name},
                                                "location": {"value": location},
                                                "functionAppResourceId": {"value": retrieved_function_app_id},
                                                "storageAccountId": {"value": storage_account_id},
                                                "tags": {"value": {
                                                    "project-id": project_id,
                                                    "region-id": region_id,
                                                    "project-name": project_name
                                                }}
                                            }
                                        }
                                        with open(params_file_path, 'w') as f:
                                            json.dump(params_data, f, indent=2)
                                        logging.info(f"Created parameters file: {params_file_path}")

                                        # Deploy event grid using az CLI directly
                                        try:
                                            logging.info("Deploying event grid system topic using az CLI directly...")
                                            deploy_cmd = [
                                                "az", "deployment", "group", "create",
                                                "--resource-group", resource_group,
                                                "--name", f"event-grid-deployment-{int(time.time())}",
                                                "--template-file", "modules/event_grid.bicep",
                                                "--parameters", f"@{params_file_path}",
                                                "--query", "properties.outputs",
                                                "-o", "json"
                                            ]
                                            deploy_process = subprocess.run(deploy_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, check=False)

                                            if deploy_process.returncode == 0 and deploy_process.stdout.strip():
                                                try:
                                                    event_grid_outputs = json.loads(deploy_process.stdout)
                                                    logging.info(f"Event grid deployment outputs: {json.dumps(event_grid_outputs, indent=2)}")

                                                    # Update resource data with event grid information
                                                    if "eventGridSystemTopicName" in event_grid_outputs:
                                                        topic_name = event_grid_outputs["eventGridSystemTopicName"]["value"]
                                                        resource_data["event_grid_system_topic_name"] = topic_name
                                                        logging.info(f"Event Grid System Topic created: {topic_name}")

                                                    if "eventSubscriptionName" in event_grid_outputs:
                                                        subscription_name = event_grid_outputs["eventSubscriptionName"]["value"]
                                                        resource_data["event_grid_subscription_name"] = subscription_name
                                                        logging.info(f"Event Subscription created: {subscription_name}")

                                                    event_grid_complete = True
                                                except json.JSONDecodeError:
                                                    logging.warning(f"Could not parse JSON output from event grid deployment: {deploy_process.stdout}")
                                                    event_grid_outputs = {}
                                            else:
                                                logging.error(f"Event grid deployment failed: {deploy_process.stderr}")
                                                event_grid_outputs = {}

                                            # Clean up the temporary parameters file
                                            os.unlink(params_file_path)
                                        except Exception as e:
                                            logging.error(f"Error deploying event grid system topic: {e}")
                                            event_grid_outputs = {}
                                        logging.info(f"Event grid system topic and subscription deployment outputs: {json.dumps(event_grid_outputs, indent=2)}")

                                        # Verify the event grid system topic was created
                                        topic_name_output = event_grid_outputs.get("eventGridSystemTopicName", {})
                                        topic_name = topic_name_output.get("value") if isinstance(topic_name_output, dict) else None

                                        if topic_name:
                                            resource_data["event_grid_system_topic_name"] = topic_name
                                            logging.info(f"Extracted Event Grid Topic Name: {topic_name}")

                                            # Verify the event grid system topic exists in Azure
                                            logging.info(f"Verifying Event Grid Topic '{topic_name}' exists...")
                                            topic_check_cmd = [
                                                "az", "eventgrid", "system-topic", "show",
                                                "--name", topic_name,
                                                "--resource-group", resource_group,
                                                "--query", "name",
                                                "-o", "tsv"
                                            ]
                                            topic_check = subprocess.run(topic_check_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, check=False)
                                            if topic_check.returncode == 0 and topic_check.stdout.strip() == topic_name:
                                                logging.info(f"Verified event grid system topic '{topic_name}' exists")
                                            else:
                                                logging.warning(f"Could not verify event grid system topic '{topic_name}' exists: {topic_check.stderr}")
                                                if event_grid_retry_count < max_event_grid_retries:
                                                    logging.info(f"Waiting 30 seconds before retry {event_grid_retry_count + 1}...")
                                                    time.sleep(30)
                                                    continue # Retry the event grid deployment
                                                else:
                                                    logging.error(f"Failed to verify event grid topic '{topic_name}' after {max_event_grid_retries} attempts.")
                                                    # Don't break, let it try subscription verification if possible
                                        else:
                                             logging.warning("Event Grid Topic Name not found in deployment outputs.")


                                        # Verify the event subscription was created
                                        subscription_name_output = event_grid_outputs.get("eventSubscriptionName", {})
                                        subscription_name = subscription_name_output.get("value") if isinstance(subscription_name_output, dict) else None

                                        if subscription_name:
                                            resource_data["event_grid_subscription_name"] = subscription_name
                                            logging.info(f"Extracted Event Grid Subscription Name: {subscription_name}")

                                            # Verify the event subscription exists in Azure, requires topic name
                                            if topic_name:
                                                logging.info(f"Verifying Event Grid Subscription '{subscription_name}' exists under topic '{topic_name}'...")
                                                subscription_check_cmd = [
                                                    "az", "eventgrid", "system-topic", "event-subscription", "show",
                                                    "--name", subscription_name,
                                                    "--system-topic-name", topic_name, # Use verified topic_name string
                                                    "--resource-group", resource_group,
                                                    "--query", "name",
                                                    "-o", "tsv"
                                                ]
                                                subscription_check = subprocess.run(subscription_check_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, check=False)
                                                if subscription_check.returncode == 0 and subscription_check.stdout.strip() == subscription_name:
                                                    logging.info(f"Verified event subscription '{subscription_name}' exists")
                                                    event_grid_complete = True # Mark as complete only if verified
                                                    # break # Break the retry loop as we successfully verified
                                                else:
                                                    logging.warning(f"Could not verify event subscription '{subscription_name}' exists: {subscription_check.stderr}")
                                                    if event_grid_retry_count < max_event_grid_retries:
                                                        logging.info(f"Waiting 30 seconds before retry {event_grid_retry_count + 1}...")
                                                        time.sleep(30)
                                                        continue # Retry the event grid deployment
                                                    else:
                                                        logging.error(f"Failed to verify event grid subscription '{subscription_name}' after {max_event_grid_retries} attempts.")
                                            else:
                                                logging.warning(f"Cannot verify subscription '{subscription_name}' because topic name was not found.")
                                        else:
                                            logging.warning("Event Grid Subscription Name not found in deployment outputs.")

                                        # If we reached here and verification passed, break the loop
                                        if event_grid_complete:
                                            logging.info(f"Event Grid deployment and verification successful on attempt {event_grid_retry_count}.")
                                            break

                                    except Exception as event_grid_error:
                                        logging.error(f"Error during event grid deployment/verification (attempt {event_grid_retry_count}/{max_event_grid_retries}): {event_grid_error}")
                                        break
                                    except Exception as event_grid_error:
                                        logging.error(f"Error deploying event grid system topic (attempt {event_grid_retry_count}/{max_event_grid_retries}): {event_grid_error}")
                                        if event_grid_retry_count < max_event_grid_retries:
                                            logging.info(f"Waiting 30 seconds before retry {event_grid_retry_count + 1}...")
                                            time.sleep(30)
                                        else:
                                            logging.error(f"Failed to deploy event grid system topic after {max_event_grid_retries} attempts")

                                if not event_grid_complete:
                                    logging.error("Event grid deployment failed after all retry attempts")
                            else:
                                logging.error("Could not attempt event grid system topic/subscription deployment because storage account ID could not be retrieved")
                        else:
                            logging.error("Could not attempt event grid system topic/subscription deployment because storage account name was not found in resource data")
                    else:
                         logging.warning("Skipping Event Grid deployment because Function App ID was not retrieved.")

                    # Update project resources in CosmosDB with function app and potentially event grid info
                    update_result = await update_project_resources(project_id, resource_data, api_url)
                    if update_result:
                        logging.info(f"Successfully updated project {project_id} with function app and event grid info in CosmosDB")
                    else:
                        logging.warning(f"Failed to update project {project_id} with function app and event grid info in CosmosDB")

                    # Generate deployment summary and automatically update the project in CosmosDB
                    try:
                        summary_file_path = generate_deployment_summary(
                            project_id,
                            project_name,
                            region_id,
                            resource_group,
                            deployment_start_time,
                            resource_data,
                            main_bicep_outputs,
                            status="success",
                            auto_update_project=True
                        )
                        logging.info(f"Deployment summary generated at {summary_file_path} and project updated in CosmosDB")
                    except Exception as e:
                        logging.error(f"Error generating deployment summary: {e}")
                        logging.warning("Continuing with deployment status update despite summary generation error")

                    # Update deployment status to completed
                    status_data = {
                        "status": "completed",
                        "message": "ACR Function App deployed successfully." + (" Event Grid deployment attempted." if retrieved_function_app_id else ""),
                        "details": {
                            "infrastructure_complete": True,
                            "function_app_complete": True,
                            "event_grid_complete": event_grid_complete,
                            "overall_complete": True,
                            "ready_for_function_deployment": False
                        }
                    }
                    await update_deployment_status(project_id, status_data, api_url)
                else:
                    # Function App deployment failed, but main infrastructure is still good
                    logging.warning("Function App deployment failed or name could not be retrieved")

                    # Generate deployment summary with partial success status and automatically update the project in CosmosDB
                    try:
                        summary_file_path = generate_deployment_summary(
                            project_id,
                            project_name,
                            region_id,
                            resource_group,
                            deployment_start_time,
                            resource_data,
                            main_bicep_outputs,
                            status="partial_success",
                            auto_update_project=True
                        )
                        logging.info(f"Deployment summary generated at {summary_file_path} with partial success status and project updated in CosmosDB")
                    except Exception as e:
                        logging.error(f"Error generating deployment summary: {e}")
                        logging.warning("Continuing with deployment status update despite summary generation error")

                    status_data = {
                        "status": "partial_success",
                        "message": "Main infrastructure deployed successfully, but Function App deployment failed.",
                        "details": {
                            "infrastructure_complete": True,
                            "function_app_complete": False,
                            "overall_complete": False,
                            "error_type": "function_app_deployment_error"
                        }
                    }
                    await update_deployment_status(project_id, status_data, api_url)
            except Exception as func_app_error:
                # Function App deployment exception
                logging.error(f"Error deploying Function App: {func_app_error}")

                # Generate deployment summary with partial success status and automatically update the project in CosmosDB
                try:
                    summary_file_path = generate_deployment_summary(
                        project_id,
                        project_name,
                        region_id,
                        resource_group,
                        deployment_start_time,
                        resource_data,
                        main_bicep_outputs,
                        status="partial_success",
                        auto_update_project=True
                    )
                    logging.info(f"Deployment summary generated at {summary_file_path} with partial success status and project updated in CosmosDB")
                except Exception as e:
                    logging.error(f"Error generating deployment summary: {e}")
                    logging.warning("Continuing with deployment status update despite summary generation error")

                status_data = {
                    "status": "partial_success",
                    "message": f"Main infrastructure deployed successfully, but Function App deployment failed: {str(func_app_error)}",
                    "details": {
                        "infrastructure_complete": True,
                        "function_app_complete": False,
                        "overall_complete": False,
                        "error_type": "function_app_deployment_error",
                        "error_details": str(func_app_error)
                    }
                }
                await update_deployment_status(project_id, status_data, api_url)

        except Exception as e:
            error_message = f"Error during main infrastructure deployment: {str(e)}"
            logging.error(error_message)

            # Generate deployment summary with failed status and automatically update the project in CosmosDB
            try:
                summary_file_path = generate_deployment_summary(
                    project_id,
                    project_name,
                    region_id,
                    resource_group,
                    deployment_start_time,
                    resource_data,
                    main_bicep_outputs,
                    status="failed",
                    auto_update_project=True
                )
                logging.info(f"Deployment summary generated at {summary_file_path} with failed status and project updated in CosmosDB")
            except Exception as summary_error:
                logging.error(f"Error generating deployment summary: {summary_error}")
                logging.warning("Continuing with deployment status update despite summary generation error")

            # Update deployment status to failed
            status_data = {
                "status": "failed",
                "message": error_message,
                "details": {
                    "error_type": "deployment_error",
                    "error_details": str(e)
                }
            }
            await update_deployment_status(project_id, status_data, api_url)
            deployment_success = False

    except Exception as e:
        logging.error(f"Error in deploy_project_resources: {e}")

        # Try to generate a deployment summary even in case of failure and automatically update the project in CosmosDB
        try:
            summary_file_path = generate_deployment_summary(
                project_id,
                project_name,
                region_id,
                resource_group,
                deployment_start_time,
                resource_data,
                main_bicep_outputs,
                status="failed",
                auto_update_project=True
            )
            logging.info(f"Deployment summary generated at {summary_file_path} despite deployment failure and project updated in CosmosDB")
        except Exception as summary_error:
            logging.error(f"Error generating deployment summary after deployment failure: {summary_error}")

        # Update status to failed
        try:
            status_data = {
                "status": "failed",
                "message": f"Error in deployment process: {str(e)}",
                "details": {
                    "error_type": "unexpected_error",
                    "error_details": str(e)
                }
            }
            await update_deployment_status(project_id, status_data, api_url)
        except Exception as update_error:
            logging.error(f"Error updating deployment status: {update_error}")

        deployment_success = False

    return deployment_success


# If run directly, execute the deployment
if __name__ == "__main__":
    # Set up logging to file for debugging
    debug_log_file = "logs/deploy_project_resources_debug.log"
    debug_handler = logging.FileHandler(debug_log_file)
    debug_handler.setLevel(logging.DEBUG)
    debug_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    debug_handler.setFormatter(debug_formatter)
    logging.getLogger().addHandler(debug_handler)

    logging.info(f"Starting deploy_project_resources.py with args: {sys.argv}")

    if len(sys.argv) < 3:
        print("Usage: deploy_project_resources.py <project_id> <project_name> [--region-id <region_id>] [--resource-group <resource_group>] [--location <location>] [--function-app-id <function_app_id>] [api_url]")
        print("Example: deploy_project_resources.py my-project-id my-project-name --region-id westeurope --resource-group rg-internal-ai --location westeurope --function-app-id /subscriptions/xxx/resourceGroups/rg-internal-ai/providers/Microsoft.Web/sites/func-my-project-id http://localhost:50505")
        sys.exit(1)

    project_id = sys.argv[1]
    project_name = sys.argv[2]

    # Parse remaining arguments
    region_id = "westeurope"  # Default value
    resource_group = "rg-internal-ai"  # Default value
    location = "westeurope"  # Default value
    api_url = "http://localhost:50505"  # Default value
    function_app_id = None  # Default value

    # Parse remaining arguments
    i = 3
    while i < len(sys.argv):
        if sys.argv[i] == "--region-id" and i + 1 < len(sys.argv):
            region_id = sys.argv[i + 1]
            i += 2
        elif sys.argv[i] == "--resource-group" and i + 1 < len(sys.argv):
            resource_group = sys.argv[i + 1]
            i += 2
        elif sys.argv[i] == "--location" and i + 1 < len(sys.argv):
            location = sys.argv[i + 1]
            i += 2
        elif sys.argv[i] == "--function-app-id" and i + 1 < len(sys.argv):
            function_app_id = sys.argv[i + 1]
            i += 2
        elif not sys.argv[i].startswith("--"):
            # Assume this is the API URL if it's not a flag
            api_url = sys.argv[i]
            i += 1
        else:
            # Skip unknown arguments
            i += 1

    # Run the deployment
    asyncio.run(deploy_project_resources(project_id, project_name, region_id, api_url, resource_group, location, function_app_id))
