Collecting azure-mgmt-costmanagement
  Downloading azure_mgmt_costmanagement-4.0.1-py3-none-any.whl (180 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 180.9/180.9 kB 4.5 MB/s eta 0:00:00
Requirement already satisfied: azure-common~=1.1 in ./.venv/lib/python3.10/site-packages (from azure-mgmt-costmanagement) (1.1.28)
Requirement already satisfied: azure-mgmt-core<2.0.0,>=1.3.2 in ./.venv/lib/python3.10/site-packages (from azure-mgmt-costmanagement) (1.5.0)
Requirement already satisfied: isodate<1.0.0,>=0.6.1 in ./.venv/lib/python3.10/site-packages (from azure-mgmt-costmanagement) (0.7.2)
Requirement already satisfied: azure-core>=1.31.0 in ./.venv/lib/python3.10/site-packages (from azure-mgmt-core<2.0.0,>=1.3.2->azure-mgmt-costmanagement) (1.34.0)
Requirement already satisfied: six>=1.11.0 in ./.venv/lib/python3.10/site-packages (from azure-core>=1.31.0->azure-mgmt-core<2.0.0,>=1.3.2->azure-mgmt-costmanagement) (1.17.0)
Requirement already satisfied: requests>=2.21.0 in ./.venv/lib/python3.10/site-packages (from azure-core>=1.31.0->azure-mgmt-core<2.0.0,>=1.3.2->azure-mgmt-costmanagement) (2.31.0)
Requirement already satisfied: typing-extensions>=4.6.0 in ./.venv/lib/python3.10/site-packages (from azure-core>=1.31.0->azure-mgmt-core<2.0.0,>=1.3.2->azure-mgmt-costmanagement) (4.13.2)
Requirement already satisfied: charset-normalizer<4,>=2 in ./.venv/lib/python3.10/site-packages (from requests>=2.21.0->azure-core>=1.31.0->azure-mgmt-core<2.0.0,>=1.3.2->azure-mgmt-costmanagement) (3.4.2)
Requirement already satisfied: certifi>=2017.4.17 in ./.venv/lib/python3.10/site-packages (from requests>=2.21.0->azure-core>=1.31.0->azure-mgmt-core<2.0.0,>=1.3.2->azure-mgmt-costmanagement) (2025.4.26)
Requirement already satisfied: urllib3<3,>=1.21.1 in ./.venv/lib/python3.10/site-packages (from requests>=2.21.0->azure-core>=1.31.0->azure-mgmt-core<2.0.0,>=1.3.2->azure-mgmt-costmanagement) (2.1.0)
Requirement already satisfied: idna<4,>=2.5 in ./.venv/lib/python3.10/site-packages (from requests>=2.21.0->azure-core>=1.31.0->azure-mgmt-core<2.0.0,>=1.3.2->azure-mgmt-costmanagement) (3.10)
Installing collected packages: azure-mgmt-costmanagement
Successfully installed azure-mgmt-costmanagement-4.0.1
