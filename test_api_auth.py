#!/usr/bin/env python3
"""
Test script to validate API authentication with Azure Entra ID.
This script tests the backend API endpoints that require authentication.
"""

import os
import sys
import json
import logging
import requests
from azure.identity import ClientSecretCredential

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def get_entra_token():
    """Get an access token for Entra ID authentication."""
    # Get credentials from environment variables
    client_id = os.environ.get("AZURE_CLIENT_ID")
    tenant_id = os.environ.get("AZURE_TENANT_ID")
    client_secret = os.environ.get("AZURE_APP_SECRET") or os.environ.get("AZURE_CLIENT_SECRET")

    if not all([client_id, tenant_id, client_secret]):
        logger.error("Missing required environment variables for Azure Entra ID authentication")
        return None

    try:
        # Create a credential object
        credential = ClientSecretCredential(
            tenant_id=tenant_id,
            client_id=client_id,
            client_secret=client_secret
        )

        # Get token for Microsoft Graph API
        token = credential.get_token("https://graph.microsoft.com/.default")
        return token.token
    except Exception as e:
        logger.error(f"Error getting Entra ID token: {e}")
        return None

def test_api_endpoints(base_url="http://localhost:50508"):
    """
    Test the backend API endpoints that require authentication.
    
    Args:
        base_url: The base URL of the backend API
    
    Returns:
        bool: True if all tests pass, False otherwise
    """
    # Get an access token
    token = get_entra_token()
    if not token:
        logger.error("Failed to get Entra ID token")
        return False
    
    logger.info(f"Successfully obtained token: {token[:10]}...{token[-10:]}")
    
    # Set up headers with the token
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Test endpoints
    endpoints = [
        "/api/auth/me",  # Get current user info
        "/api/rbac/regions",  # Get regions (if RBAC is enabled)
        "/api/health"  # Health check endpoint
    ]
    
    all_passed = True
    
    for endpoint in endpoints:
        try:
            logger.info(f"Testing endpoint: {endpoint}")
            url = f"{base_url}{endpoint}"
            
            response = requests.get(url, headers=headers)
            
            if response.status_code == 200:
                logger.info(f"Successfully accessed {endpoint}")
                logger.info(f"Response: {json.dumps(response.json(), indent=2)}")
            else:
                logger.error(f"Failed to access {endpoint}: {response.status_code}")
                logger.error(f"Response: {response.text}")
                all_passed = False
        except Exception as e:
            logger.error(f"Error testing endpoint {endpoint}: {e}")
            all_passed = False
    
    return all_passed

def main():
    """
    Main function to run the test.
    """
    # Set environment variables from command line arguments if provided
    if len(sys.argv) > 1:
        os.environ["AZURE_CLIENT_ID"] = sys.argv[1]
    if len(sys.argv) > 2:
        os.environ["AZURE_TENANT_ID"] = sys.argv[2]
    if len(sys.argv) > 3:
        os.environ["AZURE_APP_SECRET"] = sys.argv[3]
        os.environ["AZURE_CLIENT_SECRET"] = sys.argv[3]
    
    # Get base URL from command line or use default
    base_url = sys.argv[4] if len(sys.argv) > 4 else "http://localhost:50508"
    
    # Test the API endpoints
    success = test_api_endpoints(base_url)
    
    if success:
        logger.info("API authentication test completed successfully!")
        return 0
    else:
        logger.error("API authentication test failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
