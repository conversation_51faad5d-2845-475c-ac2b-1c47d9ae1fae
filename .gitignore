# Virtual environments
.venv/
venv/

# Node modules
frontend/node_modules/


# Python cache files
__pycache__/
*.py[cod]

# IDE and editor files

*.swp
*.swo
.idea/

# OS generated files
.DS_Store
Thumbs.db

# Azure-related files
.azure/
# .devcontainer/ is now tracked

# Jupyter Notebook checkpoints
.ipynb_checkpoints/

# Build outputs
static/assets/
*.map

# Logs and debug files
*.log
*.log.*
npm-debug.log\*
logs/


# Temporary files
*.tmp
*.bak

# Zone.Identifier files (often created when downloading files on Windows)
*:Zone.Identifier

# Keep static directory, but ignore its contents
static/*
!static/.gitkeep

# Ignore pycache in all directories
**/__pycache__/

# Ignore dist directories (common for frontend builds)
**/dist/

# Ignore test coverage reports
coverage/
.coverage

# Ignore package-lock.json for npm
package-lock.json

# Ignore compiled Python files
*.pyc

# Ignore local development configuration files
*.local

# Ignore any secrets or sensitive files
*.pem
*.key
setup-ssh.sh
.devcontainer/devcontainer.json


# Ignore uploads and watching folders
priorityplot-main - Copy\uploads
priorityplot-main - Copy\watch
.github/
.git/
.vscode/
.idea/
# .devcontainer/ is now tracked
