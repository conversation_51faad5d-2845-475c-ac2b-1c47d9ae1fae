#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add a user as SUPER_ADMIN to the CosmosDB database.
This is useful for setting up initial admin users after Entra ID login.
"""
import asyncio
import sys
import os
from backend.rbac.cosmosdb_rbac_service import CosmosRbacClient
from backend.settings import app_settings
from backend.models.rbac import UserRole

async def add_super_admin_user(user_email: str, user_name: str, user_id: str = None):
    """Add a user as SUPER_ADMIN to the database."""
    print(f"Adding user {user_name} ({user_email}) as SUPER_ADMIN...")
    
    # Initialize CosmosDB client
    if not app_settings.chat_history:
        print("Error: CosmosDB settings not configured")
        return False
        
    cosmos_endpoint = f"https://{app_settings.chat_history.account}.documents.azure.com:443/"
    
    client = CosmosRbacClient(
        cosmos_endpoint,
        app_settings.chat_history.account_key,
        app_settings.chat_history.database
    )
    
    try:
        # Initialize the client
        if not await client.initialize():
            print("Failed to initialize CosmosDB client")
            return False
            
        # Create user data
        user_data = {
            "name": user_name,
            "email": user_email,
            "role": UserRole.SUPER_ADMIN.value,
            "avatar": f"https://ui-avatars.com/api/?name={user_name.replace(' ', '+')}&background=random"
        }
        
        if user_id:
            user_data["id"] = user_id
            
        # Create or update the user
        created_user = await client.create_user(user_data)
        print(f"Successfully added user: {created_user}")
        
        return True
        
    except Exception as e:
        print(f"Error adding user: {e}")
        return False
    finally:
        await client.close()

async def main():
    """Main function."""
    if len(sys.argv) < 3:
        print("Usage: python add_super_admin_user.py <email> <name> [user_id]")
        print("Example: python add_super_admin_user.py '<EMAIL>' 'Admin User' '12345'")
        sys.exit(1)
        
    email = sys.argv[1]
    name = sys.argv[2]
    user_id = sys.argv[3] if len(sys.argv) > 3 else None
    
    success = await add_super_admin_user(email, name, user_id)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    # Add the project root to the Python path
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    asyncio.run(main())