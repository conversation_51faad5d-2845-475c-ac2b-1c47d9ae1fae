import os
import asyncio
from dotenv import load_dotenv
from backend.rbac.cosmosdb_rbac_service import CosmosRbacClient

async def test_users():
    load_dotenv()
    cosmos_endpoint = f'https://{os.environ.get("AZURE_COSMOSDB_ACCOUNT")}.documents.azure.com:443/'
    cosmos_client = CosmosRbacClient(
        cosmosdb_endpoint=cosmos_endpoint, 
        credential=os.environ.get('AZURE_COSMOSDB_ACCOUNT_KEY'), 
        database_name=os.environ.get('AZURE_COSMOSDB_DATABASE')
    )
    
    print('Initializing CosmosDB client...')
    init_success = await cosmos_client.initialize()
    print(f'Initialization success: {init_success}')
    
    if init_success:
        print('Testing connection...')
        success, message = await cosmos_client.ensure()
        print(f'Connection test: {success}, Message: {message}')
        
        if success:
            print('Getting all users...')
            users = []
            query = "SELECT * FROM c WHERE c.type = 'user'"
            async for item in cosmos_client.users_container.query_items(query=query, parameters=[]):
                users.append(item)
            
            print(f'Found {len(users)} users')
            for user in users:
                print(f"User ID: {user.get('id')}, Name: {user.get('name')}, Role: {user.get('role')}")
            
    await cosmos_client.close()

if __name__ == "__main__":
    asyncio.run(test_users())
