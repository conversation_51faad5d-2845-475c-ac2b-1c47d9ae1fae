#!/usr/bin/env python3

import asyncio
import json
import logging
import os
import sys
from datetime import datetime, timezone
from azure.cosmos.aio import CosmosClient
from azure.cosmos import exceptions
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

async def direct_update_deployment_status(project_id, region="westeurope"):
    """
    Directly update the deployment status in CosmosDB.

    Args:
        project_id (str): The project ID.
        region (str): The region of the project (used as partition key).
    """
    logger.info(f"Directly updating deployment status for project {project_id} in region {region}")

    # Get CosmosDB connection details from environment variables
    cosmos_account = os.environ.get("AZURE_COSMOSDB_ACCOUNT")
    cosmos_key = os.environ.get("AZURE_COSMOSDB_ACCOUNT_KEY")
    cosmos_database = os.environ.get("AZURE_COSMOSDB_DATABASE")
    cosmos_conversations_container = os.environ.get("AZURE_COSMOSDB_CONVERSATIONS_CONTAINER")

    if not cosmos_account or not cosmos_key or not cosmos_database:
        logger.error("AZURE_COSMOSDB_ACCOUNT, AZURE_COSMOSDB_ACCOUNT_KEY, and AZURE_COSMOSDB_DATABASE environment variables must be set")
        return False

    # Construct the CosmosDB endpoint URL
    cosmos_endpoint = f"https://{cosmos_account}.documents.azure.com:443/"

    logger.info(f"Using CosmosDB endpoint: {cosmos_endpoint}")
    logger.info(f"Using CosmosDB database: {cosmos_database}")
    logger.info(f"Using CosmosDB conversations container: {cosmos_conversations_container}")

    # Create a CosmosDB client
    async with CosmosClient(cosmos_endpoint, credential=cosmos_key) as client:
        # Get the database and container
        database = client.get_database_client(cosmos_database)

        # Get the conversations container
        conversations_container = database.get_container_client(cosmos_conversations_container)

        try:
            # Create a completed deployment status
            now = datetime.now(timezone.utc).isoformat()
            deployment_status = {
                "status": "completed",
                "message": "Azure resources deployment completed successfully",
                "updated_at": now,
                "details": {
                    "storage": {"containers": True},
                    "storage_complete": True,
                    "search": {"index": True, "indexer": True, "datasource": True},
                    "search_complete": True,
                    "function": {"function_app": True, "event_grid": True, "maturity_assessment": True, "executive_summary": True},
                    "function_complete": True,
                    "overall_complete": True,
                    "completion_percentage": 100,
                    "force_completed": True
                }
            }

            # Try to find the project in the conversations container
            query = "SELECT * FROM c WHERE c.id = @projectId AND c.type = 'project'"
            parameters = [{"name": "@projectId", "value": project_id}]

            conversation_projects = []
            async for item in conversations_container.query_items(
                query=query,
                parameters=parameters
            ):
                conversation_projects.append(item)

            if conversation_projects:
                conversation_project = conversation_projects[0]
                logger.info(f"Found project {project_id} in conversations container")

                # Update the project document
                conversation_project["deployment_status"] = deployment_status

                # Update the timestamp field based on the field name in the document
                if "updatedAt" in conversation_project:
                    conversation_project["updatedAt"] = now
                elif "updated_at" in conversation_project:
                    conversation_project["updated_at"] = now

                # Get the partition key from the document
                partition_key = conversation_project.get("userId", "anonymous")
                logger.info(f"Using partition key: {partition_key}")

                # Replace the item in the container
                try:
                    # First try without partition_key parameter since we're using SDK 4.5.1
                    # which doesn't support partition_key in replace_item
                    logger.info("Using replace_item without partition_key parameter")
                    await conversations_container.replace_item(
                        item=conversation_project["id"],
                        body=conversation_project
                    )
                except Exception as e:
                    logger.error(f"Error replacing item: {e}")
                    # If that fails, try with partition_key parameter as a fallback
                    try:
                        logger.info("Retrying with partition_key parameter")
                        await conversations_container.replace_item(
                            item=conversation_project["id"],
                            body=conversation_project,
                            partition_key=partition_key
                        )
                    except Exception as e2:
                        logger.error(f"Error with fallback approach: {e2}")
                        raise
                logger.info(f"Updated deployment status for project {project_id} in conversations container")

                # Try to find if there's a projects container
                try:
                    # Check if there's a projects container
                    projects_container = database.get_container_client("projects")

                    # Try to find the project in the projects container
                    try:
                        # First try with the region as partition key
                        project = await projects_container.read_item(item=project_id, partition_key=region)
                        logger.info(f"Found project {project_id} in projects container with region {region}")

                        # Update the project document
                        project["deployment_status"] = deployment_status
                        project["updated_at"] = now

                        # Replace the item in the container
                        try:
                            # First try without partition_key parameter since we're using SDK 4.5.1
                            logger.info("Using replace_item without partition_key parameter")
                            await projects_container.replace_item(
                                item=project_id,
                                body=project
                            )
                        except Exception as e:
                            logger.error(f"Error replacing item: {e}")
                            # If that fails, try with partition_key parameter as a fallback
                            try:
                                logger.info("Retrying with partition_key parameter")
                                await projects_container.replace_item(
                                    item=project_id,
                                    body=project,
                                    partition_key=region
                                )
                            except Exception as e2:
                                logger.error(f"Error with fallback approach: {e2}")
                                raise
                        logger.info(f"Updated deployment status for project {project_id} in projects container")
                    except exceptions.CosmosResourceNotFoundError:
                        logger.warning(f"Project {project_id} not found in projects container with region {region}")

                        # Try to find the project using a query
                        query = "SELECT * FROM c WHERE c.id = @projectId AND c.type = 'project'"
                        parameters = [{"name": "@projectId", "value": project_id}]

                        projects = []
                        async for item in projects_container.query_items(
                            query=query,
                            parameters=parameters
                        ):
                            projects.append(item)

                        if projects:
                            project = projects[0]
                            project_region = project.get("region")
                            logger.info(f"Found project {project_id} in projects container with region {project_region}")

                            # Update the project document
                            project["deployment_status"] = deployment_status
                            project["updated_at"] = now

                            # Replace the item in the container
                            try:
                                # First try without partition_key parameter since we're using SDK 4.5.1
                                logger.info("Using replace_item without partition_key parameter")
                                await projects_container.replace_item(
                                    item=project_id,
                                    body=project
                                )
                            except Exception as e:
                                logger.error(f"Error replacing item: {e}")
                                # If that fails, try with partition_key parameter as a fallback
                                try:
                                    logger.info("Retrying with partition_key parameter")
                                    await projects_container.replace_item(
                                        item=project_id,
                                        body=project,
                                        partition_key=project_region
                                    )
                                except Exception as e2:
                                    logger.error(f"Error with fallback approach: {e2}")
                                    raise
                            logger.info(f"Updated deployment status for project {project_id} in projects container")
                        else:
                            logger.warning(f"Project {project_id} not found in projects container")
                except Exception as e:
                    logger.warning(f"Error accessing projects container: {e}")

                return True
            else:
                logger.error(f"Project {project_id} not found in conversations container")

                # Try to find if there's a projects container
                try:
                    # Check if there's a projects container
                    projects_container = database.get_container_client("projects")

                    # Try to find the project in the projects container
                    try:
                        # First try with the region as partition key
                        project = await projects_container.read_item(item=project_id, partition_key=region)
                        logger.info(f"Found project {project_id} in projects container with region {region}")

                        # Update the project document
                        project["deployment_status"] = deployment_status
                        project["updated_at"] = now

                        # Replace the item in the container
                        try:
                            # First try without partition_key parameter since we're using SDK 4.5.1
                            logger.info("Using replace_item without partition_key parameter")
                            await projects_container.replace_item(
                                item=project_id,
                                body=project
                            )
                        except Exception as e:
                            logger.error(f"Error replacing item: {e}")
                            # If that fails, try with partition_key parameter as a fallback
                            try:
                                logger.info("Retrying with partition_key parameter")
                                await projects_container.replace_item(
                                    item=project_id,
                                    body=project,
                                    partition_key=region
                                )
                            except Exception as e2:
                                logger.error(f"Error with fallback approach: {e2}")
                                raise
                        logger.info(f"Updated deployment status for project {project_id} in projects container")

                        return True
                    except exceptions.CosmosResourceNotFoundError:
                        logger.warning(f"Project {project_id} not found in projects container with region {region}")

                        # Try to find the project using a query
                        query = "SELECT * FROM c WHERE c.id = @projectId AND c.type = 'project'"
                        parameters = [{"name": "@projectId", "value": project_id}]

                        projects = []
                        async for item in projects_container.query_items(
                            query=query,
                            parameters=parameters
                        ):
                            projects.append(item)

                        if projects:
                            project = projects[0]
                            project_region = project.get("region")
                            logger.info(f"Found project {project_id} in projects container with region {project_region}")

                            # Update the project document
                            project["deployment_status"] = deployment_status
                            project["updated_at"] = now

                            # Replace the item in the container
                            try:
                                # First try without partition_key parameter since we're using SDK 4.5.1
                                logger.info("Using replace_item without partition_key parameter")
                                await projects_container.replace_item(
                                    item=project_id,
                                    body=project
                                )
                            except Exception as e:
                                logger.error(f"Error replacing item: {e}")
                                # If that fails, try with partition_key parameter as a fallback
                                try:
                                    logger.info("Retrying with partition_key parameter")
                                    await projects_container.replace_item(
                                        item=project_id,
                                        body=project,
                                        partition_key=project_region
                                    )
                                except Exception as e2:
                                    logger.error(f"Error with fallback approach: {e2}")
                                    raise
                            logger.info(f"Updated deployment status for project {project_id} in projects container")

                            return True
                        else:
                            logger.error(f"Project {project_id} not found in projects container")
                            return False
                except Exception as e:
                    logger.error(f"Error accessing projects container: {e}")
                    return False
        except Exception as e:
            logger.error(f"Error updating deployment status: {e}")
            return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        logger.error("Usage: python direct_update_deployment_status.py <project_id> [region]")
        sys.exit(1)

    project_id = sys.argv[1]
    region = sys.argv[2] if len(sys.argv) > 2 else "westeurope"

    # Run the async function
    asyncio.run(direct_update_deployment_status(project_id, region))
