Integrating the `deploy_function_app_from_acr.sh` script into your existing project creation process involves modifying how your Function App infrastructure is defined and how the functions are deployed. The goal is to replace the current Python code deployment (via `func azure functionapp publish`) with a container-based deployment from Azure Container Registry (ACR).

Here's a step-by-step approach focusing on Option 2 described in the thought process, which offers a modular integration:

**Overall Strategy:**

1.  The main Bicep (e.g., `project_resources_no_eventgrid.bicep` compiled to `project_resources.json`) will no longer define the Azure Function App itself. It will continue to deploy other project-specific resources (Storage, Search, Event Grid if used, etc.).
2.  `deploy_project_resources.py` will first call `deploy_project_resources.sh` to deploy these non-Function App resources.
3.  Then, `deploy_project_resources.py` will gather necessary parameters and execute `scripts/ACR_deployment/deploy_function_app_from_acr.sh`. This script will deploy the Function App using its own Bicep template (`scripts/ACR_deployment/function_app_deployment.bicep`), configured to run a container from ACR.
4.  The existing `auto_deploy_functions.py` and `scripts/deploy_project_functions.py` will no longer be needed for this flow, as functions are now part of the deployed container.
5.  Status updates to CosmosDB will be managed by `deploy_project_resources.py` after the ACR script completes.

**Detailed Integration Steps:**

**1. Modify the Main Bicep Template (e.g., `project_resources_no_eventgrid.bicep`)**

* **Remove Function App Resource:** Locate and delete the resource definition for `Microsoft.Web/sites` that corresponds to the Azure Function App.
* **Remove App Service Plan (if specific):** If an App Service Plan (`Microsoft.Web/serverfarms`) is defined exclusively for this Function App, remove it. The ACR script's Bicep (`scripts/ACR_deployment/function_app_deployment.bicep`) will handle the necessary hosting plan (likely a consumption plan or a plan suitable for containers).
* **Ensure Necessary Outputs:** Verify that any outputs from this main Bicep deployment that might be needed by the ACR script (e.g., search service name if provisioned here and shared, OpenAI service name, resource group name, location) are still correctly defined. The `deploy_function_app_from_acr.sh` script expects some parameters like `searchServiceName` and `openAiServiceName` to be in its `parameters.json`.

**2. Prepare `scripts/ACR_deployment/deploy_function_app_from_acr.sh`**

* **Parameterize Resource Group and Location:** The script currently has hardcoded `RESOURCE_GROUP="rg-internal-ai"` and `LOCATION="westeurope"`. These should be made dynamic. Modify the script to accept these as environment variables or command-line arguments. For example, at the beginning of the script:
    ```bash
    # Configuration
    RESOURCE_GROUP="${TARGET_RESOURCE_GROUP:-rg-internal-ai}" # Use env var or default
    LOCATION="${TARGET_LOCATION:-westeurope}"                 # Use env var or default
    # ... rest of the script
    ```
    `deploy_project_resources.py` will then set `TARGET_RESOURCE_GROUP` and `TARGET_LOCATION` as environment variables.
* **Parameter File Handling:** The script reads `scripts/ACR_deployment/parameters.json`. `deploy_project_resources.py` will dynamically update this file. Ensure the bash script reads from this path.

**3. Modify `deploy_project_resources.py`**

This script will become the primary orchestrator for both the main Bicep deployment and the ACR Function App deployment.

```python
# deploy_project_resources.py
import subprocess
import json
import os
import logging
import time # For retries or waits if necessary
from backend.services.cosmosdb_rbac_service import CosmosRbacClient # Assuming this path
from backend.config import AppConfig # Assuming this path for shared configs

# Configure logging
logger = logging.getLogger(__name__)

# --- (Existing functions like update_deployment_status, get_project_config might be here or imported) ---
# You'll need functions to update CosmosDB. Simplified example:
def update_cosmos_deployment_status(project_id: str, status: str, detail: str = "", function_app_name: str = None, overall_complete: bool = False):
    try:
        cosmos_rbac_client = CosmosRbacClient() # Initialize your CosmosDB client
        project_data = cosmos_rbac_client.get_project(project_id)
        if not project_data:
            logger.error(f"Project {project_id} not found in CosmosDB for status update.")
            return

        # Update status
        project_data['deployment_status']['status'] = status
        project_data['deployment_status']['message'] = detail
        project_data['deployment_status']['last_updated'] = time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime())

        if function_app_name:
            project_data['resources']['function_app_name'] = function_app_name
            # Potentially construct function_app_url if needed
            # project_data['resources']['function_app_url'] = f"https://{function_app_name}.azurewebsites.net"

        if overall_complete:
            project_data['deployment_status']['overall_complete'] = True
            # If this is the final step, mark ready_for_function_deployment as False or remove it
            project_data['deployment_status']['ready_for_function_deployment'] = False


        cosmos_rbac_client.update_project_deployment_status(project_id, project_data['deployment_status'])
        if function_app_name: # If resources were updated
             cosmos_rbac_client.update_project_resources(project_id, project_data['resources'])

        logger.info(f"Updated CosmosDB deployment status for {project_id} to {status}. Overall complete: {overall_complete}")

    except Exception as e:
        logger.error(f"Error updating CosmosDB for project {project_id}: {e}", exc_info=True)


def run_main_bicep_deployment(project_id: str, resource_group_name: str, location: str, deployment_name: str, bicep_file_path: str, params_file_path: str) -> dict:
    """
    Runs the main Bicep deployment (which no longer includes the Function App).
    Returns the deployment outputs.
    """
    logger.info(f"Starting main Bicep deployment for project {project_id} in RG {resource_group_name}")
    deploy_sh_script = "./deploy_project_resources.sh" # Or the relevant script
    
    # Ensure deploy_project_resources.sh is executable and correctly configured
    # It should output the ARM deployment outputs as JSON to stdout
    # Example of how deploy_project_resources.sh might be called if it takes parameters:
    # cmd = [deploy_sh_script, resource_group_name, location, deployment_name, bicep_file_path, params_file_path]
    # For simplicity, assuming deploy_project_resources.sh handles its parameters internally or via env vars set here
    
    env = os.environ.copy()
    env["PROJECT_ID"] = project_id
    env["RESOURCE_GROUP_NAME"] = resource_group_name
    env["LOCATION"] = location
    # Add other env vars needed by deploy_project_resources.sh

    process = subprocess.run([deploy_sh_script], capture_output=True, text=True, check=False, env=env)

    if process.returncode != 0:
        error_message = f"Main Bicep deployment failed for project {project_id}. Stderr: {process.stderr}"
        logger.error(error_message)
        update_cosmos_deployment_status(project_id, "failed", error_message)
        raise Exception(error_message)
    
    logger.info(f"Main Bicep deployment successful for project {project_id}. Stdout: {process.stdout}")
    
    try:
        # Assuming the script outputs ARM deployment result as JSON string
        deployment_outputs = json.loads(process.stdout) # Or parse as needed
        return deployment_outputs.get("properties", {}).get("outputs", {})
    except json.JSONDecodeError:
        logger.warning(f"Could not parse JSON output from main Bicep deployment for {project_id}. Assuming no critical outputs needed or handled differently.")
        return {}


def deploy_acr_function_app(project_id: str, resource_group_name: str, location: str, main_bicep_outputs: dict):
    acr_script_path = "./scripts/ACR_deployment/deploy_function_app_from_acr.sh"
    acr_params_file_path = "./scripts/ACR_deployment/parameters.json" # Path to the template parameters.json
    temp_acr_params_file_path = f"./scripts/ACR_deployment/parameters_{project_id}.json" # Temporary, project-specific

    logger.info(f"Starting ACR Function App deployment for project {project_id}")

    # 1. Gather parameters for the ACR script's parameters.json
    app_config = AppConfig() # Load shared configurations
    acr_name = app_config.ACR_NAME
    container_image_name = app_config.FUNCTIONS_CONTAINER_IMAGE_NAME
    container_image_tag = app_config.FUNCTIONS_CONTAINER_IMAGE_TAG

    # Get shared service names if needed by the ACR Bicep, e.g., from main_bicep_outputs or AppConfig
    search_service_name = main_bicep_outputs.get("searchServiceName", {}).get("value") # Example
    if not search_service_name:
        search_service_name = app_config.SHARED_SEARCH_SERVICE_NAME # Fallback to global

    openai_service_name = main_bicep_outputs.get("openAiServiceName", {}).get("value") # Example
    if not openai_service_name:
        openai_service_name = app_config.SHARED_OPENAI_SERVICE_NAME # Fallback to global

    try:
        with open(acr_params_file_path, 'r') as f_template:
            params_data_template = json.load(f_template)
    except FileNotFoundError:
        error_msg = f"ACR parameters template file not found: {acr_params_file_path}"
        logger.error(error_msg)
        update_cosmos_deployment_status(project_id, "failed", error_msg)
        raise Exception(error_msg)
    
    # Create a copy to modify for this specific project
    project_params_data = params_data_template.copy()

    # Update with dynamic values
    project_params_data["parameters"]["projectId"] = {"value": project_id}
    project_params_data["parameters"]["acrName"] = {"value": acr_name}
    project_params_data["parameters"]["containerImageName"] = {"value": container_image_name}
    project_params_data["parameters"]["containerImageTag"] = {"value": container_image_tag}
    if search_service_name:
        project_params_data["parameters"]["searchServiceName"] = {"value": search_service_name}
    if openai_service_name:
        project_params_data["parameters"]["openAiServiceName"] = {"value": openai_service_name}
    # Add any other necessary parameters

    with open(temp_acr_params_file_path, 'w') as f_project_params:
        json.dump(project_params_data, f_project_params, indent=2)
    
    logger.info(f"Generated temporary ACR parameters file: {temp_acr_params_file_path}")

    # Prepare environment for the bash script
    script_env = os.environ.copy()
    script_env["TARGET_RESOURCE_GROUP"] = resource_group_name
    script_env["TARGET_LOCATION"] = location
    # If deploy_function_app_from_acr.sh was modified to take the params file path as an argument:
    # cmd_acr = [acr_script_path, temp_acr_params_file_path]
    # Otherwise, it will use its default "./scripts/ACR_deployment/parameters.json".
    # For this example, assume we need to temporarily replace the default parameters.json
    
    original_acr_params_content = None
    if os.path.exists(acr_params_file_path):
        with open(acr_params_file_path, 'r') as f_orig:
            original_acr_params_content = f_orig.read()
    
    try:
        # Overwrite the script's default parameters.json with our project-specific one
        with open(acr_params_file_path, 'w') as f_target:
             json.dump(project_params_data, f_target, indent=2)

        process_acr = subprocess.run([acr_script_path], capture_output=True, text=True, check=False, env=script_env, shell=False) # shell=False is safer

        if process_acr.returncode != 0:
            error_message = f"ACR Function App deployment script failed for project {project_id}. Stderr: {process_acr.stderr}"
            logger.error(error_message)
            update_cosmos_deployment_status(project_id, "failed", error_message)
            raise Exception(error_message)

        logger.info(f"ACR Function App deployment script successful for project {project_id}. Stdout: {process_acr.stdout}")
        
        function_app_name = None
        func_app_name_file = "./scripts/ACR_deployment/function_app_name.txt" # As created by the bash script
        if os.path.exists(func_app_name_file):
            with open(func_app_name_file, 'r') as f:
                function_app_name = f.read().strip()
            logger.info(f"Retrieved Function App Name: {function_app_name} for project {project_id}")
            os.remove(func_app_name_file) # Clean up
        else:
            logger.warning(f"Could not find {func_app_name_file} to retrieve Function App name for {project_id}.")
        
        return function_app_name

    finally:
        # Restore original parameters.json if it was overwritten
        if original_acr_params_content:
            with open(acr_params_file_path, 'w') as f_restore:
                f_restore.write(original_acr_params_content)
        # Clean up temporary project-specific params file if we didn't overwrite the original
        if os.path.exists(temp_acr_params_file_path) and original_acr_params_content is None : # only remove if we used temp path directly
             os.remove(temp_acr_params_file_path)


if __name__ == "__main__":
    # This is an example of how deploy_project_resources.py might be called
    # In your actual app, this is likely triggered by a thread from rbac_routes.py
    import sys
    if len(sys.argv) < 5:
        print("Usage: python deploy_project_resources.py <project_id> <resource_group_name> <location> <deployment_name_prefix>")
        sys.exit(1)

    project_id_arg = sys.argv[1]
    resource_group_arg = sys.argv[2]
    location_arg = sys.argv[3]
    deployment_name_prefix_arg = sys.argv[4] # e.g., "proj-main"

    # Initialize AppConfig or ensure necessary env vars are set for shared configs
    # AppConfig.load_config() 

    try:
        # Initial status update
        update_cosmos_deployment_status(project_id_arg, "pending_infrastructure", "Starting main infrastructure deployment.")

        # Step 1: Deploy main Bicep (without Function App)
        # Paths to your main Bicep and its parameters file
        main_bicep_file = "./project_resources_no_eventgrid.bicep" # Or .json if pre-compiled
        main_params_file = f"./params/params.{project_id_arg}.json" # Assuming project-specific params are generated
        # Ensure main_params_file is created with project-specific values (storage names, search names etc.)

        main_deployment_outputs = run_main_bicep_deployment(
            project_id_arg,
            resource_group_arg,
            location_arg,
            f"{deployment_name_prefix_arg}-main-{project_id_arg[:4]}",
            main_bicep_file,
            main_params_file
        )
        update_cosmos_deployment_status(project_id_arg, "infrastructure_complete", "Main infrastructure deployed. Starting Function App deployment.")

        # Step 2: Deploy ACR Function App
        deployed_function_app_name = deploy_acr_function_app(project_id_arg, resource_group_arg, location_arg, main_deployment_outputs)
        
        if deployed_function_app_name:
            update_cosmos_deployment_status(project_id_arg, "completed", "ACR Function App deployed successfully.", function_app_name=deployed_function_app_name, overall_complete=True)
        else:
            update_cosmos_deployment_status(project_id_arg, "failed", "ACR Function App deployed but name could not be retrieved.")

    except Exception as e:
        logger.error(f"Project deployment failed for {project_id_arg}: {e}", exc_info=True)
        # The specific functions might have already updated status to failed. This is a fallback.
        update_cosmos_deployment_status(project_id_arg, "failed", f"Overall deployment process failed: {str(e)}")

```

**4. Update `backend/config.py` or `settings.py` (or your equivalent `AppConfig`)**

Add configurations for ACR if they are static:

```python
# backend/config.py (or settings.py)
class AppConfig:
    # ... existing configs
    ACR_NAME = os.environ.get("ACR_NAME", "yourdefaultacrname")
    FUNCTIONS_CONTAINER_IMAGE_NAME = os.environ.get("FUNCTIONS_CONTAINER_IMAGE_NAME", "yourfunctionimage")
    FUNCTIONS_CONTAINER_IMAGE_TAG = os.environ.get("FUNCTIONS_CONTAINER_IMAGE_TAG", "latest")
    SHARED_SEARCH_SERVICE_NAME = os.environ.get("SHARED_SEARCH_SERVICE_NAME") # If you have a shared one
    SHARED_OPENAI_SERVICE_NAME = os.environ.get("SHARED_OPENAI_SERVICE_NAME") # If you have a shared one
    # ...
```

**5. Adjust `backend/rbac/cosmosdb_rbac_service.py` (or where project creation is initiated)**

* The thread that starts `deploy_project_resources.py` needs to pass the necessary arguments.
* The initial project document created in CosmosDB should reflect that function deployment is part of the main infrastructure step now. The `ready_for_function_deployment` flag might become less relevant or set differently.

**6. Remove/Disable Old Function Deployment Mechanism**

* **`auto_deploy_functions.py`:** This script (and its trigger mechanism) will likely no longer be needed if all functions are deployed via the ACR container.
* **`scripts/deploy_project_functions.py`:** This script is also superseded by the ACR deployment.

**7. Testing and Validation**

* Thoroughly test the modified `deploy_project_resources.py` script.
* Verify that all parameters are correctly passed to `deploy_function_app_from_acr.sh`.
* Check Azure portal to confirm resources are deployed as expected.
* Validate that the deployed Function App (running the container) is operational.
* Ensure CosmosDB status updates are accurate throughout the process.

This detailed plan provides a roadmap for the integration. Remember that error handling, logging, and managing the state in CosmosDB are critical for a robust deployment process. The provided Python code is a conceptual guide and will need adaptation to your exact project structure and helper functions.