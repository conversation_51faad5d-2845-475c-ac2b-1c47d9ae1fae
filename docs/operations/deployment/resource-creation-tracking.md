# Automatic Resource Creation and Tracking

This document provides a detailed explanation of how the AI Scope application automatically creates, tracks, and manages Azure resources for projects.

## Resource Creation and Deployment Flow

The process involves several key steps orchestrated primarily by the backend API (`app.py`) and a background deployment script (`deploy_project_resources.py`).

```ascii
┌───────────────┐     ┌───────────────────────────┐      ┌──────────────────┐
│ Frontend UI   │────▶│ Backend API (app.py)      │─────▶│ CosmosDB         │
│ (New Project) │     │ (POST /api/rbac/projects) │      │ (Project Document)│
└───────────────┘     └───────────┬─────────────┘       └──────────────────┘
                                  │ 1. Create Project Doc
                                  │    (incl. initial 'pending' status)
                                  │ 2. Create Initial Azure Resources
                                  │    (Containers, Search, Topic)
                                  │ 3. Trigger Background Deployment
                                  ▼
      ┌───────────────────────────────────────────────────┐
      │ Background Deployment Process                     │
      │ (deploy_project_resources.py via Thread)          │
      └───────────────┬───────────────────────────────────┘
                      │ 4. Deploy Remaining Azure Resources
                      │    (Bicep: Function App, ASP, Event Sub, etc.)
                      │ 5. Deploy Function Code (from ACR)
                      │ 6. Update Status Periodically
                      ▼
      ┌───────────────────────────────────────────────────┐
      │ Status Update Mechanisms                          │
      └───────────────────────────────────────────────────┘
          │                 │                   │
          ▼                 ▼                   ▼
┌──────────────────┐  ┌──────────────┐  ┌────────────────────┐
│ POST API Call    │  │ WebSocket    │  │ AzureResourceMonitor │
│ (/api/projects/{id}/deployment-status)│  │ (/ws/deployment-status/{id}) │  │ (Fallback Check)   │
└──────────────────┘  └──────────────┘  └────────────────────┘
          │                 │                   │
          └─────────▶ CosmosDB ◀──────────┘
                    (deployment_status field)
```

**Key Stages:**

1. **Project Initiation (API Call):**
   - User submits new project form to `/api/rbac/projects`
   - Backend creates project document in CosmosDB with initial 'pending' deployment status
   - Initial Azure resources (storage containers) may be created synchronously
   - Background deployment process is triggered

2. **Background Deployment (Script):**
   - `deploy_project_resources.py` runs as a background thread
   - Deploys Azure resources using Bicep templates
   - Deploys Function App from Azure Container Registry (ACR)
   - Updates deployment status via API calls

3. **Status Tracking & Updates:**
   - **Source of Truth:** The `deployment_status` field in the project document in CosmosDB
   - **Updates:** The deployment script sends status updates via API calls
   - **Verification:** The `AzureResourceMonitor` class runs periodically to check actual Azure resources and update status in CosmosDB if needed
   - **Frontend:** Listens for WebSocket updates at `/ws/deployment-status/{project_id}`

## WebSocket Communication

**Endpoint:** `/ws/deployment-status/{project_id}`

**Message Format:**
```json
{
  "type": "deployment_status_update",
  "data": {
    "status": "in_progress",
    "message": "Deploying storage resources",
    "details": {
      "storage": {
        "storage_account": true,
        "containers": {
          "uploads": true,
          "input": true,
          "output": false
        }
      },
      "storage_complete": false,
      "search": {
        "search_service": true,
        "index": false,
        "indexer": false,
        "datasource": false
      },
      "search_complete": false,
      "function": {
        "function_app": false,
        "event_grid_topic": false,
        "event_grid_system_topic": false,
        "event_grid": false,
        "maturity_assessment": false,
        "executive_summary": false
      },
      "function_complete": false,
      "overall_complete": false,
      "completion_percentage": 30
    },
    "updated_at": "2023-06-15T10:35:00Z"
  },
  "timestamp": "2023-06-15T10:35:00Z"
}
```

## Status Tracking Implementation

### Status Document Structure

The deployment status is stored in the `deployment_status` field of the project document in CosmosDB. The structure is as follows:

```json
{
  "status": "in_progress",  // "pending", "in_progress", "completed", "failed"
  "message": "Deploying storage resources",
  "updated_at": "2023-06-15T10:35:00Z",
  "details": {
    "storage": {
      "storage_account": true,
      "containers": {
        "uploads": true,
        "input": true,
        "output": true
      }
    },
    "storage_complete": true,
    "search": {
      "search_service": true,
      "index": true,
      "indexer": true,
      "datasource": true
    },
    "search_complete": true,
    "function": {
      "function_app": true,
      "event_grid_topic": true,
      "event_grid_system_topic": true,
      "event_grid": true,
      "maturity_assessment": true,
      "executive_summary": true
    },
    "function_complete": true,
    "overall_complete": true,
    "completion_percentage": 100
  }
}
```

### Status Update Flow

1. **Initial Status Creation:**
   - When a project is created, an initial `deployment_status` with `status: "pending"` is added to the project document
   - All resource flags are set to `false`
   - `completion_percentage` is set to 0

2. **Status Updates During Deployment:**
   - The deployment script (`deploy_project_resources.py`) sends status updates via API calls to `/api/projects/{project_id}/deployment-status`
   - The API endpoint updates the `deployment_status` field in CosmosDB
   - The API endpoint broadcasts the updated status via WebSocket

3. **WebSocket Updates:**
   - The frontend establishes a WebSocket connection to `/ws/deployment-status/{project_id}`
   - The backend sends status updates via WebSocket when the status changes
   - The frontend updates the UI based on the received status

4. **Resource Monitor Verification:**
   - The `AzureResourceMonitor` class periodically checks the actual Azure resources
   - If it detects a discrepancy between the actual resources and the recorded status, it can update the status in CosmosDB
   - This serves as a fallback mechanism to ensure the status is accurate

## Resource Types and Creation Methods

The application creates and tracks the following Azure resources:

### Storage Resources
- **Storage Account:** Created via Bicep template
- **Containers:**
  - `{project-name}-{suffix}-uploads`: For user-uploaded documents
  - `{project-name}-{suffix}-input`: For template files
  - `{project-name}-{suffix}-output`: For processed outputs

### Search Resources
- **Search Service:** Created via Bicep template
- **Search Index:** Created via Bicep template or API
- **Search Datasource:** Created via Bicep template or API
- **Search Indexer:** Created via Bicep template or API

### Function Resources
- **App Service Plan:** Created via Bicep template
- **Function App:** Created via Bicep template
- **Function Code:** Deployed from Azure Container Registry (ACR)
- **Event Grid Topic:** Created via Bicep template
- **Event Grid System Topic:** Created via Bicep template
- **Event Grid Subscription:** Created via Bicep template

## Error Handling and Recovery

The deployment process includes several error handling and recovery mechanisms:

1. **Deployment Script Error Handling:**
   - The deployment script catches and logs errors
   - It updates the deployment status to `failed` with error details
   - It attempts to clean up any partially created resources

2. **Resource Monitor Recovery:**
   - The `AzureResourceMonitor` can detect and recover from certain deployment failures
   - If it detects that all resources exist but the status is still `in_progress`, it can update the status to `completed`
   - This helps recover from situations where the deployment script fails to update the status

3. **Manual Recovery:**
   - The application provides a way to manually force the deployment status update
   - This can be done via the API endpoint with the `force_update=true` query parameter
   - This is useful for recovering from deployment failures without redeploying resources

## Monitoring and Diagnostics

The application provides several ways to monitor and diagnose deployment issues:

1. **Deployment Logs:**
   - The deployment script logs detailed information about the deployment process
   - Logs include timestamps, resource names, and error details

2. **Status API:**
   - The `/api/projects/{project_id}/deployment-status` endpoint provides the current deployment status
   - It includes detailed information about each resource

3. **WebSocket Updates:**
   - The WebSocket connection provides real-time updates about the deployment status
   - This is useful for monitoring long-running deployments

## Security Considerations

The deployment process includes several security considerations:

1. **Authentication:**
   - The deployment script uses Azure CLI credentials when running locally
   - It uses Managed Identity or Service Principal when running in Azure

2. **Authorization:**
   - The API endpoints check if the user has permission to access the project
   - The WebSocket connection requires the user to be authenticated

3. **Resource Access:**
   - The deployment script only creates resources in the specified resource group
   - It uses the minimum required permissions to create resources
