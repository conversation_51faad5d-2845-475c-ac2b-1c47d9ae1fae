# Project Creation Process

This document details the end-to-end process of creating a new project in the AI Scope application, from user interaction to resource deployment.

## User Interface Flow

1. **Project Selection Page**
   - User navigates to the Projects page (`/projects`)
   - User clicks "Create new" button to initiate project creation
   - System redirects to the New Project page (`/new-project`)

2. **New Project Form**
   - User enters project name
   - User selects a region (West US 2, West Europe, or Chile North Central)
   - User clicks "Create Project" to submit the form

3. **Deployment Status UI**
   - After submission, the UI transitions to a deployment status view
   - Shows progress of Azure resource creation with status indicators
   - Displays overall progress percentage
   - Provides error handling and support ticket creation if deployment fails

## Backend Process

### 1. Project Metadata Creation

When a user submits the new project form, the following happens:

1. Frontend sends a POST request to `/api/rbac/projects` with project name and region
2. Backend generates:
   - Unique project ID (UUID)
   - Sanitized name for Azure resources
   - Unique suffix for resource naming
   - Container names for uploads, input, and output
   - Function app name
   - Search resource names (index, datasource, indexer)
   - Event Grid Topic name
   - Random business icon for the project
3. Project metadata is stored in Cosmos DB with the following structure:
   ```json
   {
     "id": "project-uuid",
     "name": "Project Name",
     "description": "Project description",
     "created_at": "timestamp",
     "updated_at": "timestamp",
     "storage_account_name": "stprojectname1234",
     "storage_container_uploads": "project-name-1234-uploads",
     "storage_container_input": "project-name-1234-input",
     "storage_container_output": "project-name-1234-output",
     "search_service_name": "search-projectname1234",
     "search_index_name": "project-name-index",
     "search_datasource_name": "project-name-ds",
     "search_indexer_name": "project-name-indexer",
     "function_app_name": "func-name-1234",
     "event_grid_topic_name": "eg-project-name-1234",
     "event_grid_system_topic_name": "egsys-project-name-1234",
     "icon": "📊",
     "role": "owner",
     "environment": {},
     "deployment_status": {
       "status": "pending",
       "message": "Project deployment started",
       "updated_at": "timestamp",
       "details": {
         "storage": {
           "storage_account": false,
           "containers": {
             "uploads": false,
             "input": false,
             "output": false
           }
         },
         "storage_complete": false,
         "search": {
           "search_service": false,
           "index": false,
           "indexer": false,
           "datasource": false
         },
         "search_complete": false,
         "function": {
           "function_app": false,
           "event_grid_topic": false,
           "event_grid_system_topic": false,
           "event_grid": false,
           "maturity_assessment": false,
           "executive_summary": false
         },
         "function_complete": false,
         "overall_complete": false,
         "completion_percentage": 0
       }
     }
   }
   ```

### 2. Azure Resource Creation

After storing the project metadata, the backend initiates Azure resource creation:

1. **Background Deployment Process**
   - The backend starts a background thread running `deploy_project_resources.py`
   - This script handles the deployment of all Azure resources using Bicep templates

2. **Storage Resources**
   - Creates a storage account or uses an existing one
   - Creates three blob containers in the Azure Storage account:
     - Uploads container: For user-uploaded documents
     - Input container: For template files
     - Output container: For processed outputs

3. **Search Resources**
   - Creates an Azure AI Search service or uses an existing one
   - Creates an index with fields for document content and metadata
   - Creates a datasource connecting to the uploads blob container
   - Creates an indexer to process documents from the datasource

4. **Function App Deployment**
   - Creates an App Service Plan or uses an existing one
   - Deploys an Azure Function App from Azure Container Registry (ACR)
   - Configures the Function App with project-specific settings
   - Sets up connections to storage, search, and OpenAI services

5. **Event Grid Resources**
   - Creates an Event Grid Topic for the project
   - Creates an Event Grid System Topic for blob events
   - Creates Event Grid Subscriptions to trigger functions

### 3. Deployment Status Tracking

The deployment process is tracked and reported to the frontend through a combination of REST API endpoints and WebSocket connections:

1. **Initial Status Setup**
   - When a project is created, an initial deployment status with `status: "pending"` is added to the project document
   - All resource flags are set to `false`
   - The frontend establishes a WebSocket connection to `/ws/deployment-status/{project_id}`

2. **Status Update Mechanisms**
   - **REST API Polling**: Frontend periodically calls `/api/projects/{project_id}/deployment-status` endpoint
   - **WebSocket Updates**: Real-time updates are pushed via WebSocket at `/ws/deployment-status/{project_id}`
   - **Deployment Script Feedback**: The `deploy_project_resources.py` script sends status updates at key milestones via API calls
   - **Resource Monitor Verification**: The `AzureResourceMonitor` class periodically checks actual Azure resources and updates status if needed

3. **Resource-Specific Tracking**
   - Each resource (storage, search, function app) has its own status tracking in the `details` field
   - Status values for the overall deployment include: "pending", "in_progress", "completed", "failed"
   - Resource status values are boolean (true/false) indicating whether the resource has been created
   - Percentage completion is calculated based on the number of completed resources

4. **Error Handling**
   - Detailed error information is captured in the `error` field of the deployment status
   - The deployment script catches and logs errors, updating the status to "failed" with error details
   - Failed resources are clearly marked in the UI
   - Support ticket creation option appears when errors occur via the `/api/projects/{project_id}/support-ticket` endpoint

#### Pros of Current Implementation

1. **Real-time Updates**: WebSocket connections provide immediate status updates without polling
2. **Granular Tracking**: Individual resource status tracking provides detailed progress information
3. **Visual Feedback**: Progress indicators give users clear visual feedback on deployment status
4. **Error Resilience**: The system continues tracking even if some resources fail to deploy
5. **Support Integration**: Built-in support ticket creation for failed deployments
6. **Deployment Resumability**: Status tracking enables potential future implementation of resumable deployments

#### Cons of Current Implementation

1. **Dual Update Mechanisms**: Using both REST polling and WebSockets adds complexity and potential inconsistencies
2. **Limited Deployment Metrics**: Current tracking focuses on success/failure rather than detailed metrics (time, resource consumption)
3. **Eventual Consistency Issues**: Status updates may arrive out of order, causing UI flickering
4. **No Deployment Logs**: Detailed deployment logs are not accessible through the UI
5. **Limited Rollback Visibility**: Users cannot see the status of rollback operations when deployment fails
6. **Connection Dependency**: WebSocket disconnections can lead to stale status information
7. **No Deployment Queue**: Multiple simultaneous deployments may cause resource contention and tracking confusion
8. **Manual Refresh Required**: If tracking fails, users must manually refresh to get the latest status

#### Improvement Opportunities

1. **Unified Status Service**: Consolidate status tracking into a dedicated service with a single source of truth
2. **Deployment Queue**: Implement a queue system for managing multiple deployment requests
3. **Enhanced Logging**: Provide access to detailed deployment logs directly in the UI
4. **Resumable Deployments**: Allow failed deployments to be resumed from the point of failure
5. **Status Persistence**: Store deployment status history for audit and troubleshooting
6. **Resource Timing**: Track and display timing information for each deployment step
7. **Predictive Estimates**: Provide estimated completion times based on historical deployment data

## Resource Deployment Implementation

### Function App Deployment

The Function App is deployed using a container image from Azure Container Registry:

1. The backend verifies access to the ACR (`functionappaiscope`)
2. Bicep templates are used to deploy the Function App with project-specific configuration
3. The Function App is configured with:
   - Connection to project storage containers
   - Search service configuration
   - OpenAI service configuration
   - Project-specific environment variables
   - Event Grid subscription configuration

### Bicep Template Deployment

The deployment uses the following Bicep templates:

1. `project_resources.bicep`: Main template that orchestrates the deployment
2. `modules/storage.bicep`: Creates and configures storage resources
3. `modules/search.bicep`: Creates and configures search resources
4. `modules/function_app_acr.bicep`: Deploys the Function App from ACR
5. `modules/event_grid.bicep`: Creates and configures Event Grid resources

The deployment is executed using Azure CLI with the following command:

```bash
az deployment group create \
  --resource-group $RESOURCE_GROUP \
  --template-file project_resources.bicep \
  --parameters @$PARAMS_FILE
```

The deployment script (`deploy_project_resources.sh`) handles the execution of the Bicep deployment and captures the outputs, which include the names of the created resources. These outputs are then used to update the project document in CosmosDB with the actual resource names.

## Authentication and Authorization

During project creation, the application handles authentication differently based on the deployment environment:

1. **Local Development**
   - Uses Azure CLI credentials (`az login`)
   - Accesses Azure resources with the developer's permissions
   - Passes `X-Azure-CLI-Credentials: true` header in API calls
   - WebSocket connections include `x-azure-cli-credentials: true` header

2. **Production Deployment**
   - Uses Managed Identity or Service Principal
   - Accesses Azure resources with the application's assigned permissions
   - Uses Entra ID app registration for authentication

3. **User Authorization**
   - Project creation requires the user to be authenticated
   - Projects are associated with the user who created them
   - Only authorized users can access project resources
   - Role-based access control (RBAC) is implemented for project operations

## Error Handling and Rollback

If errors occur during project creation, the system implements error handling and rollback procedures:

1. **Initial API Call Failures**
   - If Cosmos DB storage fails, the process is aborted before Azure resource creation
   - If initial resource creation fails, the API returns an error response
   - The frontend displays the error message to the user

2. **Background Deployment Failures**
   - If the Bicep deployment fails, the deployment script logs the error
   - The deployment status is updated to "failed" with error details
   - The frontend displays the error message to the user
   - Option to create a support ticket is provided via the `/api/projects/{project_id}/support-ticket` endpoint

3. **Resource Cleanup**
   - If resource creation fails, the deployment script attempts to clean up any partially created resources
   - The cleanup process is logged for troubleshooting
   - If cleanup fails, the error is logged but does not affect the user experience

4. **Recovery Mechanisms**
   - The `AzureResourceMonitor` can detect and recover from certain deployment failures
   - Manual recovery is possible via the API endpoint with the `force_update=true` query parameter

## Post-Creation Process

After successful project creation:

1. User is redirected to the Projects page
2. The new project appears in the project list
3. User can click on the project to access its dedicated workspace
4. Project resources are ready for document upload and processing
