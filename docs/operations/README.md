# Operations Documentation

This section provides documentation on deploying, monitoring, and maintaining the AI Scope application.

## Contents

- **[Deployment](./deployment/)**: Deployment guides and procedures
- **[Monitoring](./monitoring/)**: Monitoring and alerting setup
- **[Cost Management](./cost-management/)**: Cost tracking and optimization

## Deployment Environments

The application supports two main deployment environments:

### Development Environment

- Local deployment using Docker containers
- Uses Azure CLI credentials for resource access
- Simplified deployment for testing and development

### Production Environment

- Deployed as Azure Web App or Container App
- Uses Managed Identity for secure resource access
- Full RBAC implementation for secure multi-tenant usage

## Monitoring Strategy

The application implements comprehensive monitoring:

1. **Application Logs**: Detailed logging of application events and errors
2. **Deployment Status**: Real-time tracking of resource deployment status
3. **Resource Usage**: Monitoring of resource consumption and limits
4. **User Activity**: Tracking of user actions and project access

## Cost Management

The application includes cost management features:

1. **Project-Level Cost Tracking**: Track costs at the project level
2. **Cost Limits**: Set cost limits for projects
3. **Cost Optimization**: Recommendations for optimizing resource usage
