# CosmosDB Implementation

This document provides a comprehensive overview of how Azure Cosmos DB is implemented and utilized within the AI Scope application.

## Architecture Overview

The application uses a single Azure Cosmos DB account with a NoSQL API (formerly known as DocumentDB) to store various types of data:

- Project configurations
- User data and permissions
- Conversation history
- Deployment status tracking
- System configuration

### Database and Container Structure

The application currently uses multiple containers within a single database:

```
CosmosDB Account
└── Database: db_conversation_history
    ├── Container: conversations_project
    │   ├── Partition Key: /userId
    │   └── Items:
    │       ├── type='conversation' (Chat conversations)
    │       └── type='message' (Individual chat messages)
    │
    ├── Container: projects
    │   ├── Partition Key: /region
    │   └── Items: Project configurations
    │
    ├── Container: users
    │   ├── Partition Key: /id
    │   └── Items: User profiles
    │
    ├── Container: regions
    │   ├── Partition Key: /id
    │   └── Items: Region configurations
    │
    ├── Container: teams
    │   ├── Partition Key: /region
    │   └── Items: Team configurations
    │
    └── Container: roleAssignments
        ├── Partition Key: /userId
        └── Items: Role assignments
```

> **Note:** There is an inconsistency in the codebase. The documentation previously described a single-container approach with a partition key on `/type`, but the actual implementation uses multiple containers with different partition keys as shown above. This document has been updated to reflect the current implementation.

### Container Usage in the Application

Each container serves a specific purpose in the application:

1. **conversations_project**: Stores chat conversations and messages, partitioned by user ID
   ```python
   # Reference in CosmosRbacClient initialization
   self.conversations_project_container = self.database_client.get_container_client("conversations_project")

   # Usage example from CosmosRbacClient.get_conversation
   async for item in self.conversations_project_container.query_items(query=query, parameters=parameters):
       conversations.append(item)
   ```

2. **projects**: Stores project configurations, partitioned by region
   ```python
   # Reference in CosmosRbacClient initialization
   self.projects_container = self.database_client.get_container_client("projects")

   # Usage example from CosmosRbacClient.get_project
   project = await self.projects_container.read_item(item=project_id, partition_key=region)
   ```

3. **users**: Stores user profiles, partitioned by user ID
   ```python
   # Reference in CosmosRbacClient initialization
   self.users_container = self.database_client.get_container_client("users")

   # Usage example from CosmosRbacClient.get_user
   user = await self.users_container.read_item(item=user_id, partition_key=user_id)
   ```

4. **regions**: Stores region configurations, partitioned by region ID
   ```python
   # Reference in CosmosRbacClient initialization
   self.regions_container = self.database_client.get_container_client("regions")
   ```

5. **teams**: Stores team configurations, partitioned by region
   ```python
   # Reference in CosmosRbacClient initialization
   self.teams_container = self.database_client.get_container_client("teams")
   ```

6. **roleAssignments**: Stores role assignments, partitioned by user ID
   ```python
   # Reference in CosmosRbacClient initialization
   self.role_assignments_container = self.database_client.get_container_client("roleAssignments")
   ```

## Document Schemas

The following schemas show the document structure for each container in the current implementation.

### Projects Container

```json
// Container: projects
// Partition Key: /region
{
  "id": "project-uuid",
  "type": "project",
  "name": "Project Name",
  "description": "Project description",
  "created_at": "2023-06-15T10:30:00Z",
  "updated_at": "2023-06-15T10:30:00Z",
  "created_by": "user-uuid",
  "region": "westeurope",  // Partition key
  "storage_container_uploads": "project-name-1234-uploads",
  "storage_container_input": "project-name-1234-input",
  "storage_container_output": "project-name-1234-output",
  "search_index_name": "project-name-index",
  "search_datasource_name": "project-name-ds",
  "search_indexer_name": "project-name-indexer",
  "function_app_name": "func-name-1234",
  "function_app_url": "https://func-name-1234.azurewebsites.net",
  "icon": "📊",
  "status": "active",
  "deployment_status": "completed",
  "cost_limit": 100.00,
  "current_cost": 45.75,
  "members": [
    {
      "user_id": "user-uuid-1",
      "role": "owner",
      "added_at": "2023-06-15T10:30:00Z"
    },
    {
      "user_id": "user-uuid-2",
      "role": "contributor",
      "added_at": "2023-06-16T14:20:00Z"
    }
  ],
  "environment": {
    "CUSTOM_VAR1": "value1",
    "CUSTOM_VAR2": "value2"
  }
}
```

### Users Container

```json
// Container: users
// Partition Key: /id
{
  "id": "user-uuid",  // Partition key
  "type": "user",
  "name": "John Doe",
  "email": "<EMAIL>",
  "role": "contributor",
  "region": "westeurope",
  "avatar": "https://example.com/avatar.jpg",
  "created_at": "2023-06-15T10:30:00Z",
  "updated_at": "2023-06-15T10:30:00Z"
}
```

### Regions Container

```json
// Container: regions
// Partition Key: /id
{
  "id": "region-uuid",  // Partition key
  "type": "region",
  "name": "West Europe",
  "description": "European region based in Amsterdam",
  "cost_limit": 5000.00,
  "created_by": "user-uuid",
  "created_at": "2023-06-15T10:30:00Z",
  "updated_at": "2023-06-15T10:30:00Z"
}
```

### Teams Container

```json
// Container: teams
// Partition Key: /region
{
  "id": "team-uuid",
  "type": "team",
  "name": "Engineering Team",
  "description": "Software engineering team",
  "region": "westeurope",  // Partition key
  "created_by": "user-uuid",
  "created_at": "2023-06-15T10:30:00Z",
  "updated_at": "2023-06-15T10:30:00Z",
  "members": [
    {
      "user_id": "user-uuid-1",
      "role": "owner",
      "added_at": "2023-06-15T10:30:00Z"
    },
    {
      "user_id": "user-uuid-2",
      "role": "member",
      "added_at": "2023-06-16T14:20:00Z"
    }
  ]
}
```

### Role Assignments Container

```json
// Container: roleAssignments
// Partition Key: /userId
{
  "id": "assignment-uuid",
  "type": "roleAssignment",
  "userId": "user-uuid",  // Partition key
  "resourceId": "project-uuid",
  "resourceType": "project",
  "role": "contributor",
  "assigned_by": "admin-user-uuid",
  "assigned_at": "2023-06-15T10:30:00Z",
  "updated_at": "2023-06-15T10:30:00Z"
}
```

### Conversations Project Container

```json
// Container: conversations_project
// Partition Key: /userId
{
  "id": "conversation-uuid",
  "type": "conversation",
  "userId": "user-uuid",  // Partition key
  "projectId": "project-uuid",
  "title": "Conversation Title",
  "created_at": "2023-06-15T11:30:00Z",
  "updated_at": "2023-06-15T12:45:00Z",
  "messages": ["message-uuid-1", "message-uuid-2", "message-uuid-3"]
}
```

```json
// Container: conversations_project
// Partition Key: /userId
{
  "id": "message-uuid",
  "type": "message",
  "userId": "user-uuid",  // Partition key
  "conversationId": "conversation-uuid",
  "projectId": "project-uuid",
  "role": "user",
  "content": "What information can you provide about this document?",
  "timestamp": "2023-06-15T11:35:00Z",
  "feedback": null,
  "citations": []
}
```

### Deployment Status (Legacy Structure)

The deployment status was previously stored in a legacy container structure with `type='deployment'`. However, in the current implementation, deployment status is stored as part of the project document in the projects container.

#### Legacy Structure (No Longer Used)

```json
// Legacy structure - no longer used
// Previously stored in "conversations" container with type='deployment'
{
  "id": "deployment-uuid",
  "type": "deployment",
  "project_id": "project-uuid",
  "status": "in_progress",
  "started_at": "2023-06-15T10:30:00Z",
  "updated_at": "2023-06-15T10:35:00Z",
  "completed_at": null,
  "created_by": "user-uuid",
  "resources": [
    {
      "name": "storage_container",
      "status": "completed",
      "started_at": "2023-06-15T10:30:05Z",
      "completed_at": "2023-06-15T10:30:15Z",
      "error": null
    },
    {
      "name": "search_index",
      "status": "in_progress",
      "started_at": "2023-06-15T10:30:20Z",
      "completed_at": null,
      "error": null
    }
  ],
  "overall_progress": 35,
  "error": null,
  "logs": [
    {
      "timestamp": "2023-06-15T10:30:05Z",
      "message": "Starting deployment of project resources",
      "level": "info"
    }
  ]
}
```

#### Current Structure (Embedded in Project Document)

```json
// Current structure - embedded in project document
// Stored in "projects" container as part of the project document
{
  "id": "project-uuid",
  "type": "project",
  "name": "Project Name",
  "region": "westeurope",  // Partition key
  // Other project fields...

  "deployment_status": {
    "status": "in_progress",
    "message": "Deploying project resources",
    "updated_at": "2023-06-15T10:35:00Z",
    "details": {
      "storage": {"containers": true},
      "storage_complete": true,
      "search": {"index": true, "indexer": false, "datasource": true},
      "search_complete": false,
      "function": {"function_app": false},
      "function_complete": false,
      "overall_complete": false,
      "completion_percentage": 65
    }
  }
}
```

#### Files Still Referencing Legacy Structure

The following files still contain references to the legacy deployment status structure:

1. **docs/operations/deployment/resource-creation-tracking.md**:
   - Contains example code that references a container with `type='deployment'`
   - This is documentation only and doesn't affect runtime behavior

2. **backend/web_sockets/deployment_status.py**:
   - Already updated to use the project document's `deployment_status` field
   - No references to the legacy container structure in actual code

The application has already been updated to store deployment status as part of the project document, and no active code is still using the legacy container structure.

## Implementation Steps for Deployment Status Integration

While the application has already been updated to store deployment status in the project document, this section provides a detailed guide on how this integration was implemented and how to ensure all components use the new structure.

### 1. Update Project Schema

The project document schema was updated to include a `deployment_status` field:

```python
# From backend/rbac/cosmosdb_rbac_service.py
async def create_project(self, project_data: Dict[str, Any]) -> Dict[str, Any]:
    """Create a new project in Cosmos DB"""
    project_id = project_data.get('id', str(uuid.uuid4()))
    now = datetime.now(timezone.utc).isoformat()

    # Add default deployment status
    default_deployment_status = {
        "status": "pending",
        "message": "Project deployment started",
        "updated_at": now,
        "details": {
            "storage": {"containers": False},
            "storage_complete": False,
            "search": {"index": False, "indexer": False, "datasource": False},
            "search_complete": False,
            "function": {"function_app": False},
            "function_complete": False,
            "overall_complete": False,
            "completion_percentage": 0
        }
    }

    project_doc = {
        'id': project_id,
        'type': 'project',
        'name': project_data.get('name'),
        'description': project_data.get('description'),
        'region': project_data.get('region'),
        # Other project fields...
        'deployment_status': default_deployment_status,
        'created_at': now,
        'updated_at': now
    }

    resp = await self.projects_container.upsert_item(project_doc)
    return resp
```

### 2. Update Deployment Status API

The API endpoint for updating deployment status was modified to update the project document:

```python
# From app.py
@bp.route("/api/projects/<project_id>/deployment-status", methods=["GET", "POST"])
async def project_deployment_status(project_id):
    """Get or update deployment status for a project."""
    # Get user ID from request or use default
    user_id = request.headers.get('X-User-ID', 'anonymous')

    # Check if we're using Azure CLI credentials
    using_azure_cli = request.headers.get('X-Azure-CLI-Credentials', 'false').lower() == 'true'

    # Get the project document
    project_doc = await current_app.cosmos_conversation_client.get_project(user_id, project_id)
    if not project_doc:
        return jsonify({"error": "Project not found"}), 404

    if request.method == "GET":
        # Return the current deployment status
        deployment_status = project_doc.get("deployment_status", {})
        return jsonify(deployment_status)

    elif request.method == "POST":
        # Update the deployment status
        data = await request.get_json()
        deployment_status = project_doc.get("deployment_status", {})

        # Update the status fields
        deployment_status.update(data)
        deployment_status["updated_at"] = datetime.now(timezone.utc).isoformat()

        # Update the project document
        project_doc["deployment_status"] = deployment_status
        await current_app.cosmos_conversation_client.update_project(user_id, project_id, project_doc)

        # Broadcast the deployment status update via WebSocket
        try:
            await broadcast_deployment_status(project_id, deployment_status)
        except Exception as ws_error:
            logging.error(f"Failed to broadcast deployment status update via WebSocket: {ws_error}")

        return jsonify(deployment_status)
```

### 3. Update WebSocket Service

The WebSocket service was updated to read deployment status from the project document:

```python
# From backend/web_sockets/deployment_status.py
async def handle_deployment_websocket(websocket: WebSocket, project_id: str, user_id: str = "anonymous"):
    """Handle WebSocket connection for deployment status updates."""
    await websocket.accept()

    # Add connection to the project's set
    if project_id not in deployment_connections:
        deployment_connections[project_id] = set()
    deployment_connections[project_id].add(websocket)

    try:
        # Check if we have the project in cache
        if project_id in deployment_status_cache:
            await websocket.send_json({
                "type": "deployment_status_update",
                "data": deployment_status_cache[project_id],
                "timestamp": datetime.now().isoformat()
            })
        # If not in cache, try to get it from the database
        elif cosmos_client:
            # Try to get the project with fallbacks
            project_doc = await get_project_with_fallbacks(
                cosmos_client=cosmos_client,
                project_id=project_id,
                user_id=user_id
            )

            if project_doc and "deployment_status" in project_doc:
                # Update cache
                deployment_status_cache[project_id] = project_doc["deployment_status"]

                # Send to client
                await websocket.send_json({
                    "type": "deployment_status_update",
                    "data": project_doc["deployment_status"],
                    "timestamp": datetime.now().isoformat()
                })
```

### 4. Update Resource Creation Process

The resource creation process was updated to update the project document directly:

```python
# Example implementation for updating resource status
async def update_resource_status(project_id, resource_name, status, error=None):
    """Update the status of a resource in the project's deployment status."""
    # Get the project document
    project = await projects_container.read_item(item=project_id, partition_key=region)

    # Get the deployment status
    deployment_status = project.get("deployment_status", {})
    details = deployment_status.get("details", {})

    # Update the resource status
    if resource_name.startswith("storage_container"):
        details["storage"]["containers"] = (status == "completed")
        if all(details["storage"].values()):
            details["storage_complete"] = True
    elif resource_name.startswith("search_"):
        resource_type = resource_name.split("_")[1]  # index, indexer, datasource
        details["search"][resource_type] = (status == "completed")
        if all(details["search"].values()):
            details["search_complete"] = True
    elif resource_name.startswith("function_"):
        resource_type = resource_name.split("_")[1]  # function_app
        details["function"][resource_type] = (status == "completed")
        if all(details["function"].values()):
            details["function_complete"] = True

    # Calculate overall progress
    completed_categories = sum(1 for k, v in details.items() if k.endswith("_complete") and v)
    total_categories = sum(1 for k in details.keys() if k.endswith("_complete"))
    completion_percentage = int((completed_categories / total_categories) * 100) if total_categories > 0 else 0

    # Update overall status
    details["overall_complete"] = (completion_percentage == 100)
    details["completion_percentage"] = completion_percentage

    # Update deployment status
    deployment_status["details"] = details
    deployment_status["updated_at"] = datetime.now(timezone.utc).isoformat()

    if completion_percentage == 100:
        deployment_status["status"] = "completed"
        deployment_status["message"] = "Deployment completed successfully"
    elif status == "failed":
        deployment_status["status"] = "failed"
        deployment_status["message"] = f"Deployment failed: {error}" if error else "Deployment failed"
        deployment_status["error"] = error
    else:
        deployment_status["status"] = "in_progress"
        deployment_status["message"] = f"Deploying {resource_name}..."

    # Update the project document
    await projects_container.patch_item(
        item=project_id,
        partition_key=region,
        patch_operations=[
            {
                "op": "replace",
                "path": "/deployment_status",
                "value": deployment_status
            },
            {
                "op": "replace",
                "path": "/updated_at",
                "value": datetime.now(timezone.utc).isoformat()
            }
        ]
    )

    # Broadcast the update via WebSocket
    await broadcast_deployment_status(project_id, deployment_status)
```

### 5. Update Frontend Components

The frontend components were updated to handle the new deployment status structure:

```typescript
// Example React component for displaying deployment status
function DeploymentStatus({ projectId }) {
  const [status, setStatus] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Initial fetch of deployment status
    const fetchStatus = async () => {
      try {
        const response = await fetch(`/api/projects/${projectId}/deployment-status`);
        if (!response.ok) throw new Error('Failed to fetch deployment status');

        const data = await response.json();
        setStatus(data);
        setLoading(false);
      } catch (err) {
        setError(err.message);
        setLoading(false);
      }
    };

    // WebSocket connection for real-time updates
    const connectWebSocket = () => {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}/ws/deployment-status/${projectId}`;

      const socket = new WebSocket(wsUrl);

      socket.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.type === 'deployment_status_update') {
          setStatus(data.data);
        }
      };

      return socket;
    };

    fetchStatus();
    const socket = connectWebSocket();

    return () => {
      if (socket) socket.close();
    };
  }, [projectId]);

  if (loading) return <div>Loading deployment status...</div>;
  if (error) return <div>Error: {error}</div>;
  if (!status) return <div>No deployment status available</div>;

  return (
    <div className="deployment-status">
      <h3>Deployment Status: {status.status}</h3>
      <p>{status.message}</p>

      {status.details && (
        <div className="deployment-details">
          <div className="progress-bar">
            <div
              className="progress"
              style={{ width: `${status.details.completion_percentage}%` }}
            />
          </div>
          <p>{status.details.completion_percentage}% complete</p>

          <h4>Resources:</h4>
          <ul>
            <li>
              Storage: {status.details.storage_complete ? '✅' : '⏳'}
              <ul>
                <li>Containers: {status.details.storage.containers ? '✅' : '⏳'}</li>
              </ul>
            </li>
            <li>
              Search: {status.details.search_complete ? '✅' : '⏳'}
              <ul>
                <li>Index: {status.details.search.index ? '✅' : '⏳'}</li>
                <li>Indexer: {status.details.search.indexer ? '✅' : '⏳'}</li>
                <li>Datasource: {status.details.search.datasource ? '✅' : '⏳'}</li>
              </ul>
            </li>
            <li>
              Function: {status.details.function_complete ? '✅' : '⏳'}
              <ul>
                <li>Function App: {status.details.function.function_app ? '✅' : '⏳'}</li>
              </ul>
            </li>
          </ul>
        </div>
      )}

      {status.error && (
        <div className="error-message">
          <h4>Error:</h4>
          <p>{status.error}</p>
        </div>
      )}
    </div>
  );
}
```

### 6. Update Documentation

All documentation was updated to reflect the new deployment status structure:

1. Update API documentation to show the new endpoint behavior
2. Update WebSocket documentation to show the new message format
3. Update deployment process documentation to show how status is tracked
4. Update CosmosDB documentation to show the new schema

### 7. Data Migration (If Needed)

If there are existing deployment status documents in the legacy container, a migration script can be used to move the data to the project documents:

```python
async def migrate_deployment_status():
    """Migrate deployment status from legacy container to project documents."""
    # Get all deployment documents from the legacy container
    query = "SELECT * FROM c WHERE c.type = 'deployment'"
    deployment_docs = []
    async for item in legacy_container.query_items(
        query=query,
        enable_cross_partition_query=True
    ):
        deployment_docs.append(item)

    print(f"Found {len(deployment_docs)} deployment documents to migrate")

    # Process each deployment document
    for doc in deployment_docs:
        project_id = doc.get("project_id")
        if not project_id:
            print(f"Skipping document {doc['id']} with no project_id")
            continue

        try:
            # Get the project document
            project_query = "SELECT * FROM c WHERE c.id = @projectId AND c.type = 'project'"
            project_params = [{"name": "@projectId", "value": project_id}]

            projects = []
            async for item in projects_container.query_items(
                query=project_query,
                parameters=project_params,
                enable_cross_partition_query=True
            ):
                projects.append(item)

            if not projects:
                print(f"Project {project_id} not found for deployment {doc['id']}")
                continue

            project = projects[0]
            region = project.get("region")

            # Convert legacy deployment status to new format
            new_deployment_status = {
                "status": doc.get("status", "unknown"),
                "message": "Migrated from legacy deployment status",
                "updated_at": doc.get("updated_at", datetime.now(timezone.utc).isoformat()),
                "details": {
                    "storage": {"containers": False},
                    "storage_complete": False,
                    "search": {"index": False, "indexer": False, "datasource": False},
                    "search_complete": False,
                    "function": {"function_app": False},
                    "function_complete": False,
                    "overall_complete": doc.get("status") == "completed",
                    "completion_percentage": doc.get("overall_progress", 0)
                }
            }

            # Update resource details based on legacy resources
            for resource in doc.get("resources", []):
                resource_name = resource.get("name", "")
                resource_status = resource.get("status") == "completed"

                if "storage_container" in resource_name:
                    new_deployment_status["details"]["storage"]["containers"] = resource_status
                elif "search_index" in resource_name:
                    new_deployment_status["details"]["search"]["index"] = resource_status
                elif "search_indexer" in resource_name:
                    new_deployment_status["details"]["search"]["indexer"] = resource_status
                elif "search_datasource" in resource_name:
                    new_deployment_status["details"]["search"]["datasource"] = resource_status
                elif "function_app" in resource_name:
                    new_deployment_status["details"]["function"]["function_app"] = resource_status

            # Update completion flags
            new_deployment_status["details"]["storage_complete"] = all(new_deployment_status["details"]["storage"].values())
            new_deployment_status["details"]["search_complete"] = all(new_deployment_status["details"]["search"].values())
            new_deployment_status["details"]["function_complete"] = all(new_deployment_status["details"]["function"].values())

            # Update the project document
            await projects_container.patch_item(
                item=project_id,
                partition_key=region,
                patch_operations=[
                    {
                        "op": "replace",
                        "path": "/deployment_status",
                        "value": new_deployment_status
                    },
                    {
                        "op": "replace",
                        "path": "/updated_at",
                        "value": datetime.now(timezone.utc).isoformat()
                    }
                ]
            )

            print(f"Successfully migrated deployment status for project {project_id}")

        except Exception as e:
            print(f"Error migrating deployment status for project {project_id}: {e}")

    print("Migration completed")
```

### 8. Testing

Comprehensive testing should be performed to ensure all components work with the new structure:

1. **Unit Tests**: Test individual functions that handle deployment status
2. **Integration Tests**: Test the API endpoints and WebSocket service
3. **End-to-End Tests**: Test the complete deployment process
4. **UI Tests**: Test the frontend components that display deployment status

### 9. Cleanup

Once the migration is complete and all components are working with the new structure, the legacy code and data can be cleaned up:

1. Remove any remaining references to the legacy container structure
2. Update documentation to reflect the new structure
3. Remove any migration scripts that are no longer needed
4. Archive or delete the legacy deployment status documents
```

## Query Patterns

The following query patterns demonstrate how to interact with the different containers in the current multi-container implementation.

### Projects Container Queries

```python
# Get a specific project by ID (with known region)
project = await self.projects_container.read_item(
    item=project_id,
    partition_key=region
)

# Get a specific project by ID (region unknown)
query = "SELECT * FROM c WHERE c.id = @projectId"
parameters = [{"name": "@projectId", "value": project_id}]
projects = []
async for item in self.projects_container.query_items(
    query=query,
    parameters=parameters
):
    projects.append(item)
project = projects[0] if projects else None

# Get all projects in a specific region
query = "SELECT * FROM c WHERE c.region = @region"
parameters = [{"name": "@region", "value": region}]
projects = []
async for item in self.projects_container.query_items(
    query=query,
    parameters=parameters
):
    projects.append(item)

# Update project deployment status
await self.projects_container.patch_item(
    item=project_id,
    partition_key=region,
    patch_operations=[
        {
            "op": "replace",
            "path": "/deployment_status",
            "value": "completed"
        },
        {
            "op": "replace",
            "path": "/updated_at",
            "value": datetime.now(timezone.utc).isoformat()
        }
    ]
)
```

### Users Container Queries

```python
# Get a specific user by ID
user = await self.users_container.read_item(
    item=user_id,
    partition_key=user_id
)

# Get users by role
query = "SELECT * FROM c WHERE c.role = @role"
parameters = [{"name": "@role", "value": role}]
users = []
async for item in self.users_container.query_items(
    query=query,
    parameters=parameters
):
    users.append(item)

# Update user information
await self.users_container.replace_item(
    item=user_id,
    partition_key=user_id,
    body=updated_user_doc
)
```

### Conversations Project Container Queries

```python
# Get conversations for a specific user and project
query = """
SELECT * FROM c
WHERE c.type = 'conversation'
AND c.userId = @userId
AND c.projectId = @projectId
"""
parameters = [
    {"name": "@userId", "value": user_id},
    {"name": "@projectId", "value": project_id}
]
conversations = []
async for item in self.conversations_project_container.query_items(
    query=query,
    parameters=parameters
):
    conversations.append(item)

# Get messages for a specific conversation
query = """
SELECT * FROM c
WHERE c.type = 'message'
AND c.userId = @userId
AND c.conversationId = @conversationId
ORDER BY c.timestamp
"""
parameters = [
    {"name": "@userId", "value": user_id},
    {"name": "@conversationId", "value": conversation_id}
]
messages = []
async for item in self.conversations_project_container.query_items(
    query=query,
    parameters=parameters
):
    messages.append(item)
```

### Role Assignments Container Queries

```python
# Get role assignments for a specific user
query = "SELECT * FROM c WHERE c.userId = @userId"
parameters = [{"name": "@userId", "value": user_id}]
assignments = []
async for item in self.role_assignments_container.query_items(
    query=query,
    parameters=parameters
):
    assignments.append(item)

# Get users with access to a specific project
query = """
SELECT * FROM c
WHERE c.resourceType = 'project'
AND c.resourceId = @projectId
"""
parameters = [{"name": "@projectId", "value": project_id}]
project_assignments = []
async for item in self.role_assignments_container.query_items(
    query=query,
    parameters=parameters,
    enable_cross_partition_query=True
):
    project_assignments.append(item)
```

### Teams Container Queries

```python
# Get teams in a specific region
query = "SELECT * FROM c WHERE c.region = @region"
parameters = [{"name": "@region", "value": region}]
teams = []
async for item in self.teams_container.query_items(
    query=query,
    parameters=parameters
):
    teams.append(item)

# Get team by ID
team = await self.teams_container.read_item(
    item=team_id,
    partition_key=region
)
```

### Regions Container Queries

```python
# Get all regions
query = "SELECT * FROM c"
regions = []
async for item in self.regions_container.query_items(query=query):
    regions.append(item)

# Get region by ID
region = await self.regions_container.read_item(
    item=region_id,
    partition_key=region_id
)
```

## Performance Optimization

### Indexing Policy

The container uses a custom indexing policy to optimize for the most common query patterns:

```json
{
  "indexingMode": "consistent",
  "automatic": true,
  "includedPaths": [
    {
      "path": "/type/?",
      "indexes": [
        {
          "kind": "Range",
          "dataType": "String",
          "precision": -1
        }
      ]
    },
    {
      "path": "/project_id/?",
      "indexes": [
        {
          "kind": "Range",
          "dataType": "String",
          "precision": -1
        }
      ]
    },
    {
      "path": "/created_at/?",
      "indexes": [
        {
          "kind": "Range",
          "dataType": "String",
          "precision": -1
        }
      ]
    },
    {
      "path": "/region/?",
      "indexes": [
        {
          "kind": "Range",
          "dataType": "String",
          "precision": -1
        }
      ]
    }
  ],
  "excludedPaths": [
    {
      "path": "/logs/*"
    },
    {
      "path": "/content/*"
    }
  ]
}
```

### Request Units (RU) Optimization

To optimize RU consumption:

1. **Partition Key Strategy**: Using `/type` as the partition key ensures even distribution of requests across physical partitions.

2. **Query Optimization**:
   - Use direct ID lookups when possible
   - Include the partition key in queries to avoid cross-partition queries
   - Use projections to return only needed fields

3. **Bulk Operations**:
   - Use bulk operations for batch updates and inserts
   - Implement retry policies with exponential backoff

4. **Caching**:
   - Implement application-level caching for frequently accessed data
   - Use TTL (Time-To-Live) for temporary data

## Consistency Level

The application uses Session consistency level, which provides:

- Read-your-writes consistency for a session
- Monotonic reads for a session
- Good balance between consistency and performance

## Error Handling and Resilience

### Retry Policy

The application implements a custom retry policy for Cosmos DB operations:

```python
retry_options = RetryOptions(max_retry_attempt_count=5,
                            max_retry_wait_time=timedelta(seconds=30),
                            retry_delay=timedelta(seconds=1),
                            max_retry_delay=timedelta(seconds=10))

cosmos_client = CosmosClient(url=cosmos_url,
                            credential=cosmos_key,
                            retry_options=retry_options)
```

### Error Handling

Common error scenarios and handling strategies:

1. **429 (Too Many Requests)**:
   - Implement exponential backoff
   - Consider scaling up RU provisioning

2. **Connectivity Issues**:
   - Implement circuit breaker pattern
   - Fall back to cached data when possible

3. **Consistency Conflicts**:
   - Implement conflict resolution strategies
   - Use ETags for optimistic concurrency control

## Multi-Region Configuration

For global deployments, Cosmos DB is configured for multi-region write with automatic failover:

1. **Primary Region**: West Europe
2. **Secondary Regions**: West US 2, Chile North Central

This configuration provides:
- Low-latency reads from the nearest region
- Automatic failover in case of regional outages
- Global distribution of data

## Monitoring and Diagnostics

### Key Metrics to Monitor

1. **Request Units (RU) Consumption**:
   - Track RU usage per operation
   - Monitor for throttling (429 responses)

2. **Latency**:
   - Track p50, p90, and p99 latency
   - Monitor for regional variations

3. **Storage Usage**:
   - Monitor data size growth
   - Track index size

### Diagnostic Logging

Cosmos DB operations are logged with:

1. **Resource-specific logs**:
   - Data Plane logs
   - Control Plane logs

2. **Diagnostic settings**:
   - Logs sent to Log Analytics
   - Metrics exported to Azure Monitor

## Security Implementation

### Data Encryption

1. **At Rest**: Data is automatically encrypted using Microsoft-managed keys
2. **In Transit**: All connections use TLS 1.2+

### Access Control

1. **Service-level**: Managed Identity with least-privilege roles
2. **Application-level**: Custom RBAC implemented in application code

## Backup and Disaster Recovery

### Automatic Backups

Cosmos DB provides automatic backups with:
- Continuous backups with point-in-time restore
- 30-day retention period

### Disaster Recovery Strategy

1. **Multi-region failover**: Automatic failover to secondary regions
2. **Manual restore**: Point-in-time restore for data corruption scenarios

## Cost Optimization Strategies

1. **Autoscale RU Provisioning**: Scale between minimum and maximum RU/s based on demand
2. **TTL for Temporary Data**: Set TTL for deployment status and other temporary documents
3. **Optimize Indexing**: Exclude paths that don't need indexing
4. **Query Optimization**: Minimize cross-partition queries

## Current Inconsistencies and Recommendations

### Identified Inconsistencies

Based on a thorough code review, several inconsistencies have been identified in the current CosmosDB implementation:

1. **Container Structure Inconsistency**:
   - The documentation previously described a single-container approach with a partition key on `/type`
   - The actual implementation uses multiple containers with different partition keys
   - The Bicep templates (`infra/db.bicep`) define containers with different partition keys than what's used in the code

2. **Legacy Code References**:
   - Some parts of the codebase still reference a `conversations` container with a `/type` partition key
   - The newer `CosmosRbacClient` uses multiple containers including `conversations_project` with a `/userId` partition key
   - The `container_client` property in `CosmosRbacClient` is maintained for backward compatibility

3. **Deployment Scripts Inconsistency**:
   - Multiple Bicep templates exist for container creation (`db.bicep`, `db_containers_only.bicep`, `db_modified.bicep`)
   - Some scripts create a container with partition key `/userId`, while others use `/type`
   - The `add_conversations_project_container.bicep` script adds a new container that wasn't in the original design

4. **Environment Variable Inconsistency**:
   - The environment variables reference a single container (`AZURE_COSMOSDB_CONVERSATIONS_CONTAINER`)
   - The code actually uses multiple containers that aren't all configured via environment variables

### Recommendations for Resolution

To resolve these inconsistencies and improve the CosmosDB implementation, the following approach is recommended:

1. **Standardize Container Structure**:
   ```python
   # From backend/rbac/cosmosdb_rbac_service.py
   class CosmosRbacClient:
       async def initialize(self, enable_message_feedback: bool = False):
           """Initialize the Cosmos DB client and containers"""
           try:
               self.enable_message_feedback = enable_message_feedback
               self.cosmosdb_client = CosmosClient(self.cosmosdb_endpoint, credential=self.credential)
               self.database_client = self.cosmosdb_client.get_database_client(self.database_name)

               # Get container clients
               self.users_container = self.database_client.get_container_client("users")
               self.regions_container = self.database_client.get_container_client("regions")
               self.teams_container = self.database_client.get_container_client("teams")
               self.projects_container = self.database_client.get_container_client("projects")
               self.role_assignments_container = self.database_client.get_container_client("roleAssignments")
               self.conversations_project_container = self.database_client.get_container_client("conversations_project")

               # Set the container_client for compatibility with code expecting CosmosConversationClient
               self.container_client = self.conversations_project_container
   ```

   - **Recommendation**: Maintain the current multi-container approach as it provides better performance and scalability, but standardize the container names and partition keys across all code and infrastructure definitions.

2. **Update Environment Variables**:
   - **Recommendation**: Update the environment variable configuration to explicitly define all container names:
   ```
   AZURE_COSMOSDB_ACCOUNT=your-cosmos-account
   AZURE_COSMOSDB_DATABASE=db_conversation_history
   AZURE_COSMOSDB_CONVERSATIONS_PROJECT_CONTAINER=conversations_project
   AZURE_COSMOSDB_USERS_CONTAINER=users
   AZURE_COSMOSDB_REGIONS_CONTAINER=regions
   AZURE_COSMOSDB_TEAMS_CONTAINER=teams
   AZURE_COSMOSDB_PROJECTS_CONTAINER=projects
   AZURE_COSMOSDB_ROLE_ASSIGNMENTS_CONTAINER=roleAssignments
   ```

3. **Consolidate Infrastructure Templates**:
   - **Recommendation**: Create a single, definitive Bicep template that correctly defines all containers with their appropriate partition keys:
   ```bicep
   // From infra/db.bicep
   param containers array = [
     {
       name: 'conversations_project'
       id: 'conversations_project'
       partitionKey: '/userId'
     }
     {
       name: 'users'
       id: 'users'
       partitionKey: '/id'
     }
     {
       name: 'regions'
       id: 'regions'
       partitionKey: '/id'
     }
     {
       name: 'teams'
       id: 'teams'
       partitionKey: '/region'
     }
     {
       name: 'projects'
       id: 'projects'
       partitionKey: '/region'
     }
     {
       name: 'roleAssignments'
       id: 'roleAssignments'
       partitionKey: '/userId'
     }
   ]
   ```

4. **Refactor Legacy Code**:
   - **Recommendation**: Refactor any code still using the old single-container approach to use the appropriate container for each data type:
   ```python
   # Instead of this:
   query = "SELECT * FROM c WHERE c.type = 'project' AND c.id = @id"
   result = container.query_items(query=query, parameters=[{"name": "@id", "value": project_id}])

   # Use this:
   project = await self.projects_container.read_item(item=project_id, partition_key=region)
   ```

5. **Update Documentation**:
   - **Recommendation**: Update all documentation to reflect the multi-container approach, including this document, code comments, and any other relevant documentation.

6. **Implement Data Migration**:
   - **Recommendation**: If there is legacy data in the old container structure, implement a migration script to move it to the new container structure:
   ```python
   async def migrate_data_to_new_containers():
       # Get all items from the old container
       old_items = list(old_container.query_items(query="SELECT * FROM c", enable_cross_partition_query=True))

       # Migrate each item to the appropriate new container
       for item in old_items:
           if item.get('type') == 'project':
               await projects_container.upsert_item(body=item)
           elif item.get('type') == 'user':
               await users_container.upsert_item(body=item)
           # etc.
   ```

### Benefits of the Multi-Container Approach

The current multi-container approach, despite the inconsistencies, offers several advantages:

1. **Better Performance**: Each container can be optimized for its specific data type
2. **Improved Scalability**: Containers can be scaled independently based on usage patterns
3. **Reduced Cross-Partition Queries**: Partition keys are optimized for common query patterns
4. **Simplified Access Control**: Container-level permissions can be applied more granularly
5. **Clearer Data Organization**: Data is logically separated by entity type

By standardizing on the multi-container approach and resolving the identified inconsistencies, the application will benefit from improved performance, maintainability, and scalability.
