# Authentication Testing Guide for ai-scope-rbac

This guide provides instructions on how to test and debug the authentication flow for the ai-scope-rbac application.

## Prerequisites

- The application is running locally at http://localhost:50508
- You have the following Azure Entra ID credentials:
  - Client ID: 92e443a6-5081-4e5c-aea4-7d8df64d050d
  - Tenant ID: ee78877a-c63a-405d-85d6-8914358aa533
  - Client Secret: ****************************************

## Testing Methods

There are several ways to test the authentication flow:

1. **Using the Browser Test Page**: Test user authentication flow in the browser
2. **Using the Token Test Script**: Test API endpoints with a token
3. **Using the Client Credentials Test**: Test service-to-service authentication

## 1. Browser Test Pages

We provide two HTML pages for testing authentication in the browser:

### A. Popup Authentication Test (`test_browser_auth.html`)

This page uses the popup authentication flow, which opens a popup window for login.

#### Setup

1. Start a local web server:
   ```bash
   python -m http.server 8000
   ```

2. Open the test page in your browser:
   ```
   http://localhost:8000/test_browser_auth.html
   ```

### B. Redirect Authentication Test (`test_redirect_auth.html`)

This page uses the redirect authentication flow, which redirects the entire page for login.

#### Setup

1. Start a local web server:
   ```bash
   python -m http.server 8000
   ```

2. Open the test page in your browser:
   ```
   http://localhost:8000/test_redirect_auth.html
   ```

### Known Issues and Solutions

#### 1. Redirect URI Mismatch Error

If you see an error like:
```
AADSTS50011: The redirect URI specified in the request does not match the redirect URIs configured for the application
```

**Solution**:
- Add the redirect URI to the application in the Azure portal:
  - Go to Azure Portal > App Registrations > Your App > Authentication
  - Add `http://localhost:8000/test_browser_auth.html` and `http://localhost:8000/test_redirect_auth.html` as redirect URIs

#### 2. Interaction in Progress Error

If you see an error like:
```
interaction_in_progress: Interaction is currently in progress
```

**Solution**:
- Wait a few seconds and try again
- Close any open authentication popups
- Clear your browser cache and cookies
- Try using the redirect flow instead of the popup flow

#### 3. Scope Configuration Issues

If you see an error like:
```
AADSTS70011: The provided value for the input parameter 'scope' is not valid. .default scope can't be combined with resource-specific scopes.
```

**Solution**:
- Use either `.default` scope OR resource-specific scopes, not both
- For user authentication, use specific scopes like `https://graph.microsoft.com/User.Read`
- For client credentials flow, use `.default` scope
- The test pages have been updated to use only the `User.Read` scope

#### 4. Token Acquisition Failures

If you can't get a token after logging in:

**Solution**:
- Check that the scopes are correctly configured
- Try logging out and logging in again
- Check the browser console for detailed error messages
- Clear browser cache and cookies
- Try using a different browser

## 2. Token Test Script

The `test_token_auth.py` script allows you to test the API endpoints with a token.

### Usage

```bash
# Run the script and enter the token when prompted
python test_token_auth.py

# Or provide the token as an argument
python test_token_auth.py --token YOUR_TOKEN
```

### Getting a Token

You can get a token in several ways:

1. **From the Browser Test Page**: Use the "Get Access Token" button
2. **Using the Azure CLI**:
   ```bash
   az account get-access-token --resource https://graph.microsoft.com
   ```
3. **Using the Microsoft Authentication Library (MSAL)**:
   ```python
   from azure.identity import ClientSecretCredential

   credential = ClientSecretCredential(
       tenant_id="ee78877a-c63a-405d-85d6-8914358aa533",
       client_id="92e443a6-5081-4e5c-aea4-7d8df64d050d",
       client_secret="****************************************"
   )

   token = credential.get_token("https://graph.microsoft.com/.default")
   print(token.token)
   ```

## 3. Client Credentials Test

The `test_entra_auth.py` script allows you to test service-to-service authentication using client credentials.

### Usage

```bash
python test_entra_auth.py 92e443a6-5081-4e5c-aea4-7d8df64d050d ee78877a-c63a-405d-85d6-8914358aa533 ****************************************
```

## Understanding the Authentication Flow

The ai-scope-rbac application uses Azure Entra ID (formerly Azure AD) for authentication. It supports two types of authentication:

1. **User Authentication (Delegated Flow)**:
   - Requires a user to log in through a browser
   - The token must have the `User.Read` scope
   - Used for user-specific operations

2. **Service Authentication (Client Credentials Flow)**:
   - Uses client credentials (client ID and client secret)
   - Does not require user interaction
   - Used for service-to-service communication

### Fallback Mechanism

The application has a fallback mechanism that uses a mock user when authentication fails:

- If the token doesn't have the required scopes, it falls back to client credentials
- If client credentials authentication fails, it uses a mock user with SUPER_ADMIN privileges

This allows the application to work even when the token doesn't have the required scopes, making it more resilient and easier to test.

## API Endpoints

The following API endpoints are available for testing:

- `/api/rbac/regions`: Returns available regions
- `/api/rbac/users`: Returns users in the system
- `/api/rbac/projects`: Returns projects
- `/api/rbac/me`: Returns information about the currently authenticated user (may not be implemented)

## Troubleshooting

### Common Issues

1. **Redirect URI Mismatch**:
   - Error: `AADSTS50011: The redirect URI specified in the request does not match the redirect URIs configured for the application`
   - Solution: Modify the redirect URI in the test page or add the redirect URI to the application in the Azure portal

2. **Invalid Scopes**:
   - Error: `Token does not have User.Read scope`
   - Solution: Make sure the token has the required scopes

3. **Invalid Client Credentials**:
   - Error: `AADSTS7000215: Invalid client secret provided.`
   - Solution: Check that the client secret is correct and has not expired

4. **Endpoint Not Found**:
   - Error: `404 Not Found`
   - Solution: Check that the endpoint exists and is spelled correctly

### Debugging Tips

1. **Check the Application Logs**:
   - Look for authentication-related messages in the application logs
   - Pay attention to warnings and errors related to token validation

2. **Inspect the Token**:
   - Use a tool like [jwt.io](https://jwt.io/) to decode and inspect the token
   - Check the token's audience (`aud`), scopes (`scp`), and expiration time (`exp`)

3. **Test with Different Tokens**:
   - Try both delegated tokens and client credentials tokens
   - Compare the behavior with different tokens

## Conclusion

The ai-scope-rbac application has a robust authentication system that supports both user authentication and service authentication. The fallback mechanism makes it resilient and easier to test, but it's important to understand how the authentication flow works to properly test and debug it.
