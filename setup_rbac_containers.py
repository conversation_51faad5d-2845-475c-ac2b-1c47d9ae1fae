#!/usr/bin/env python
"""
<PERSON><PERSON>t to set up RBAC containers in Cosmos DB.
This script checks if the required containers exist and creates them if they don't.
"""

import asyncio
import logging
from azure.cosmos.aio import CosmosClient
from azure.cosmos import exceptions, PartitionKey
from backend.settings import app_settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

async def setup_rbac_containers():
    """Set up RBAC containers in Cosmos DB."""
    logging.info("Setting up RBAC containers in Cosmos DB...")

    # Check if app_settings.chat_history is configured
    if not app_settings.chat_history:
        logging.error("CosmosDB settings not configured. RBAC requires CosmosDB.")
        return False

    # Construct the CosmosDB endpoint
    cosmos_endpoint = f"https://{app_settings.chat_history.account}.documents.azure.com:443/"
    logging.info(f"CosmosDB endpoint: {cosmos_endpoint}")

    # Create a new Cosmos DB client
    try:
        async with CosmosClient(cosmos_endpoint, credential=app_settings.chat_history.account_key) as client:
            # Get the database
            database_name = app_settings.chat_history.database
            try:
                database = client.get_database_client(database_name)
                await database.read()
                logging.info(f"Database '{database_name}' exists.")
            except exceptions.CosmosResourceNotFoundError:
                logging.error(f"Database '{database_name}' does not exist. Creating it...")
                database = await client.create_database(database_name)
                logging.info(f"Database '{database_name}' created.")

            # Required containers for RBAC
            required_containers = [
                {"name": "users", "partition_key": "/id"},
                {"name": "regions", "partition_key": "/id"},
                {"name": "teams", "partition_key": "/id"},
                {"name": "projects", "partition_key": "/id"},
                {"name": "roleAssignments", "partition_key": "/id"}
            ]

            # Check and create containers
            for container_info in required_containers:
                container_name = container_info["name"]
                partition_key = container_info["partition_key"]

                try:
                    container = database.get_container_client(container_name)
                    await container.read()
                    logging.info(f"Container '{container_name}' exists.")
                except exceptions.CosmosResourceNotFoundError:
                    logging.info(f"Container '{container_name}' does not exist. Creating it...")
                    await database.create_container(
                        id=container_name,
                        partition_key=PartitionKey(path=partition_key),
                        offer_throughput=400  # Minimum throughput
                    )
                    logging.info(f"Container '{container_name}' created.")

            # Create a default region if none exists
            regions_container = database.get_container_client("regions")
            regions_query = "SELECT * FROM c WHERE c.type = 'region'"
            regions = []
            async for item in regions_container.query_items(query=regions_query):
                regions.append(item)

            if not regions:
                logging.info("No regions found. Creating default 'Europe' region...")
                import uuid
                from datetime import datetime, timezone

                now = datetime.now(timezone.utc).isoformat()
                region_doc = {
                    'id': str(uuid.uuid4()),
                    'type': 'region',
                    'name': 'Europe',
                    'description': 'Default European region',
                    'cost_limit': 1000,
                    'created_by': 'system',
                    'created_at': now,
                    'updated_at': now
                }

                await regions_container.upsert_item(region_doc)
                logging.info("Default 'Europe' region created.")

            # Create a default super admin user if none exists
            users_container = database.get_container_client("users")
            users_query = "SELECT * FROM c WHERE c.type = 'user' AND c.role = 'super_admin'"
            users = []
            async for item in users_container.query_items(query=users_query):
                users.append(item)

            if not users:
                logging.info("No super admin users found. Creating default super admin user...")
                import uuid
                from datetime import datetime, timezone

                # Get the Europe region ID
                europe_region_query = "SELECT * FROM c WHERE c.type = 'region' AND c.name = 'Europe'"
                europe_regions = []
                async for item in regions_container.query_items(query=europe_region_query):
                    europe_regions.append(item)

                region_id = europe_regions[0]['id'] if europe_regions else None

                now = datetime.now(timezone.utc).isoformat()
                user_doc = {
                    'id': '1',  # Use ID 1 for the default super admin
                    'type': 'user',
                    'name': 'John Doe',
                    'email': '<EMAIL>',
                    'role': 'super_admin',
                    'region': region_id,
                    'avatar': 'https://via.placeholder.com/150',
                    'created_at': now,
                    'updated_at': now
                }

                await users_container.upsert_item(user_doc)
                logging.info("Default super admin user created.")

            logging.info("RBAC containers setup completed successfully.")
            return True
    except Exception as e:
        logging.error(f"Error setting up RBAC containers: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(setup_rbac_containers())
