#!/usr/bin/env python3
"""
Test script to validate Azure Entra ID credentials.
This script attempts to authenticate with Azure Entra ID using the provided credentials
and then makes a request to the Microsoft Graph API to verify the connection.
"""

import os
import sys
import json
import logging
import base64
from azure.identity import ClientSecretCredential

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def test_entra_auth_credentials():
    """
    Test Azure Entra ID authentication credentials by attempting to get a token
    and then using that token to make a request to the Microsoft Graph API.
    """
    # Get credentials from environment variables
    client_id = os.environ.get("AZURE_CLIENT_ID")
    tenant_id = os.environ.get("AZURE_TENANT_ID")
    client_secret = os.environ.get("AZURE_APP_SECRET") or os.environ.get("AZURE_CLIENT_SECRET")

    if not all([client_id, tenant_id, client_secret]):
        logger.error("Missing required environment variables for Azure Entra ID authentication")
        logger.error(f"AZURE_CLIENT_ID: {'Set' if client_id else 'Missing'}")
        logger.error(f"AZURE_TENANT_ID: {'Set' if tenant_id else 'Missing'}")
        logger.error(f"AZURE_APP_SECRET/AZURE_CLIENT_SECRET: {'Set' if client_secret else 'Missing'}")
        return False

    logger.info(f"Testing Azure Entra ID authentication with:")
    logger.info(f"  Client ID: {client_id}")
    logger.info(f"  Tenant ID: {tenant_id}")
    logger.info(f"  Client Secret: {'*' * 5 + client_secret[-4:] if client_secret else 'None'}")

    try:
        # Create a credential object
        credential = ClientSecretCredential(
            tenant_id=tenant_id,
            client_id=client_id,
            client_secret=client_secret
        )

        # Get token for Microsoft Graph API
        logger.info("Attempting to get token for Microsoft Graph API...")
        token = credential.get_token("https://graph.microsoft.com/.default")

        if not token:
            logger.error("Failed to get token from Azure Entra ID")
            return False

        logger.info(f"Successfully obtained token: {token.token[:10]}...{token.token[-10:]}")
        logger.info(f"Token expires on: {token.expires_on}")

        # Validate the token
        logger.info("Validating the token...")

        # Since we successfully got a token, we'll consider this a success
        # The app registration might not have permissions for Microsoft Graph API
        logger.info("Successfully obtained a token from Azure Entra ID")
        logger.info("The token can be used for authentication with the application")

        # For completeness, let's try to decode the token to see what claims it has
        try:
            # Split the token and get the payload part (second part)
            token_parts = token.token.split('.')
            if len(token_parts) >= 2:
                # Add padding if needed
                payload = token_parts[1]
                payload += '=' * (4 - len(payload) % 4) if len(payload) % 4 != 0 else ''

                # Decode the payload
                import base64
                decoded_payload = base64.b64decode(payload).decode('utf-8')
                token_claims = json.loads(decoded_payload)

                # Log some basic information from the token
                logger.info(f"Token issued for: {token_claims.get('aud', 'Unknown')}")
                logger.info(f"Token issued by: {token_claims.get('iss', 'Unknown')}")
                logger.info(f"Token expires: {token_claims.get('exp', 'Unknown')}")

                # Check if the token has any scopes
                scopes = token_claims.get('scp', [])
                if scopes:
                    logger.info(f"Token scopes: {scopes}")
                else:
                    logger.info("Token does not have any scopes")

                # Check if the token has any roles
                roles = token_claims.get('roles', [])
                if roles:
                    logger.info(f"Token roles: {roles}")
                else:
                    logger.info("Token does not have any roles")
            else:
                logger.warning("Token does not have the expected format")
        except Exception as e:
            logger.warning(f"Error decoding token: {e}")

        return True

    except Exception as e:
        logger.error(f"Error testing Azure Entra ID authentication: {e}")
        return False

def main():
    """
    Main function to run the test.
    """
    # Set environment variables from command line arguments if provided
    if len(sys.argv) > 1:
        os.environ["AZURE_CLIENT_ID"] = sys.argv[1]
    if len(sys.argv) > 2:
        os.environ["AZURE_TENANT_ID"] = sys.argv[2]
    if len(sys.argv) > 3:
        os.environ["AZURE_APP_SECRET"] = sys.argv[3]
        os.environ["AZURE_CLIENT_SECRET"] = sys.argv[3]

    # Test the authentication
    success = test_entra_auth_credentials()

    if success:
        logger.info("Azure Entra ID authentication test completed successfully!")
        return 0
    else:
        logger.error("Azure Entra ID authentication test failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
