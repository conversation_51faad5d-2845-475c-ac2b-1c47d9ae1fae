[{"name": "AZURE_FUNCTION_MATURITY_ASSESSMENT_KEY", "value": "*retrieve from project function app*", "slotSetting": false}, {"name": "AZURE_FUNCTION_MATURITY_ASSESSMENT_URL", "value": "https://*retrieve from project function app*.net/api/HttpTriggerAppMaturityAssessment", "slotSetting": false}, {"name": "AZURE_FUNCTION_EXECUTIVE_SUMMARY_KEY", "value": "*retrieve from project function app*", "slotSetting": false}, {"name": "AZURE_FUNCTION_EXECUTIVE_SUMMARY_URL", "value": "https://*retrieve from project function app*.azurewebsites.net/api/execSummary?", "slotSetting": false}, {"name": "AZURE_STORAGE_CONTAINER_NAME", "value": "uploads*storage_container", "slotSetting": false}, {"name": "AZURE_STORAGE_CONTAINER_NAME_INPUT", "value": "input*storage_container", "slotSetting": false}, {"name": "AZURE_STORAGE_CONTAINER_NAME_OUTPUT", "value": "output*storage_container", "slotSetting": false}, {"name": "AZURE_STORAGE_ACCOUNT_NAME", "value": "project*Storage_account", "slotSetting": false}, {"name": "AZURE_STORAGE_CONTAINER_SAS_TOKEN", "value": "*", "slotSetting": false}, {"name": "AZURE_OPENAI_RESOURCE", "value": "ai-scope-openai", "slotSetting": false}, {"name": "AZURE_OPENAI_MODEL", "value": "gpt-4o-ai-scope", "slotSetting": false}, {"name": "AZURE_OPENAI_KEY", "value": "2b84bd8e5c1749789f4ba5cccb84cfdf", "slotSetting": false}, {"name": "AZURE_OPENAI_MODEL_NAME", "value": "gpt-4o", "slotSetting": false}, {"name": "AZURE_OPENAI_TEMPERATURE", "value": "0", "slotSetting": false}, {"name": "AZURE_OPENAI_TOP_P", "value": "1.0", "slotSetting": false}, {"name": "AZURE_OPENAI_MAX_TOKENS", "value": "1000", "slotSetting": false}, {"name": "AZURE_OPENAI_CHOICES_COUNT", "value": "1", "slotSetting": false}, {"name": "AZURE_OPENAI_PRESENCE_PENALTY", "value": "0.0", "slotSetting": false}, {"name": "AZURE_OPENAI_FREQUENCY_PENALTY", "value": "0.0", "slotSetting": false}, {"name": "AZURE_OPENAI_SYSTEM_MESSAGE", "value": "You are an AI IT sales asssistant, helping keyrus analyse companies", "slotSetting": false}, {"name": "AZURE_OPENAI_PREVIEW_API_VERSION", "value": "2024-05-01-preview", "slotSetting": false}, {"name": "AZURE_OPENAI_STREAM", "value": "True", "slotSetting": false}, {"name": "AZURE_OPENAI_ENDPOINT", "value": "https://ai-scope-openai.openai.azure.com/", "slotSetting": false}, {"name": "AZURE_OPENAI_EMBEDDING_NAME", "value": "text-embedding-ada-002-ai-scope", "slotSetting": false}, {"name": "AZURE_OPENAI_EMBEDDING_ENDPOINT", "value": "https://ai-scope-openai.openai.azure.com/openai/deployments/text-embedding-ada-002-ai-scope/embeddings?api-version=2023-05-15", "slotSetting": false}, {"name": "AZURE_OPENAI_EMBEDDING_KEY", "value": "2b84bd8e5c1749789f4ba5cccb84cfdf", "slotSetting": false}, {"name": "UI_CHAT_LOGO", "value": "https://images.ctfassets.net/te2janzw7nut/1k38fRLGCdPTDBILanEe0k/3da972954bd6a651b19d0af1b4bc758b/logo-keyrus.svg", "slotSetting": false}, {"name": "UI_CHAT_TITLE", "value": "Explore Your Data", "slotSetting": false}, {"name": "UI_FAVICON", "value": "https://images.ctfassets.net/te2janzw7nut/1k38fRLGCdPTDBILanEe0k/3da972954bd6a651b19d0af1b4bc758b/logo-keyrus.svg", "slotSetting": false}, {"name": "AZURE_COSMOSDB_ACCOUNT", "value": "internal-ai-conversation-history-db", "slotSetting": false}, {"name": "AZURE_COSMOSDB_DATABASE", "value": "db_conversation_history", "slotSetting": false}, {"name": "AZURE_COSMOSDB_CONVERSATIONS_CONTAINER", "value": "conversations", "slotSetting": false}, {"name": "AZURE_COSMOSDB_ACCOUNT_KEY", "value": "pNgnkPf6Sr24BPimwFURmrcPX7dWDlbagPXbdgfvVyv0ms7fFyK3FcLgIhVDa07pslzHK3MBEoHEACDbfEEfNg==", "slotSetting": false}, {"name": "AZURE_COSMOSDB_ENABLE_FEEDBACK", "value": "False", "slotSetting": false}, {"name": "DATASOURCE_TYPE", "value": "AzureCognitiveSearch", "slotSetting": false}, {"name": "SEARCH_TOP_K", "value": "5", "slotSetting": false}, {"name": "SEARCH_STRICTNESS", "value": "3", "slotSetting": false}, {"name": "SEARCH_ENABLE_IN_DOMAIN", "value": "True", "slotSetting": false}, {"name": "AZURE_SEARCH_API_VERSION", "value": "2021-04-30-Preview", "slotSetting": false}, {"name": "AZURE_SEARCH_INDEXER", "value": "ai-scope-app3-indexer", "slotSetting": false}, {"name": "AZURE_SEARCH_SERVICE", "value": "ai-scope-ai-search", "slotSetting": false}, {"name": "AZURE_SEARCH_INDEX", "value": "ai-scope-app3", "slotSetting": false}, {"name": "AZURE_SEARCH_KEY", "value": "****************************************************", "slotSetting": false}, {"name": "AZURE_SEARCH_USE_SEMANTIC_SEARCH", "value": "True", "slotSetting": false}, {"name": "AZURE_SEARCH_SEMANTIC_SEARCH_CONFIG", "value": "ai-scope-app3-semantic-configuration", "slotSetting": false}, {"name": "AZURE_SEARCH_INDEX_IS_PRECHUNKED", "value": "False", "slotSetting": false}, {"name": "AZURE_SEARCH_TOP_K", "value": "5", "slotSetting": false}, {"name": "AZURE_SEARCH_ENABLE_IN_DOMAIN", "value": "False", "slotSetting": false}, {"name": "AZURE_SEARCH_QUERY_TYPE", "value": "vectorSemanticHybrid", "slotSetting": false}, {"name": "AZURE_SEARCH_STRICTNESS", "value": "3", "slotSetting": false}, {"name": "ELASTICSEARCH_EMBEDDING_MODEL_ID", "value": " ", "slotSetting": false}, {"name": "USE_PROMPTFLOW", "value": "False", "slotSetting": false}, {"name": "PROMPTFLOW_RESPONSE_TIMEOUT", "value": "120", "slotSetting": false}, {"name": "PROMPTFLOW_REQUEST_FIELD_NAME", "value": "query", "slotSetting": false}, {"name": "PROMPTFLOW_RESPONSE_FIELD_NAME", "value": "reply", "slotSetting": false}, {"name": "PROMPTFLOW_CITATIONS_FIELD_NAME", "value": "documents", "slotSetting": false}, {"name": "ALLOWED_ORIGINS", "value": "http://localhost:50505,http://127.0.0.1:50505,http://localhost:50506,http://127.0.0.1:50506,https://ai-scope-app3.azurewebsites.net,https://ai-scope-app3.azurewebsites.net", "slotSetting": false}, {"name": "API_PORT", "value": "8000", "slotSetting": false}]