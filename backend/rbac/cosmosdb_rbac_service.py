import uuid
import sys
import logging
import json
import os
from datetime import datetime, timezone
from typing import List, Optional, Dict, Any
from azure.cosmos.aio import CosmosClient
from azure.cosmos import exceptions
try:
    from azure.core import MatchConditions
except ImportError:
    # For older versions of the SDK
    from azure.cosmos import MatchConditions
from backend.models.rbac import UserRole, ProjectUserRole, TeamUserRole
from backend.utils.logging_config import ensure_logs_directory, configure_deployment_logger, configure_cosmos_logger

# Configure a logger for CosmosDB operations
cosmos_logger = configure_cosmos_logger()

class CosmosRbacClient:
    def __init__(self, cosmosdb_endpoint: str, credential: Any, database_name: str):
        self.cosmosdb_endpoint = cosmosdb_endpoint
        self.credential = credential
        self.database_name = database_name
        self.cosmosdb_client = None
        self.database_client = None
        self.users_container = None
        self.regions_container = None
        self.teams_container = None
        self.projects_container = None
        self.role_assignments_container = None
        self.conversations_project_container = None
        self.enable_message_feedback = False
        # Compatibility attribute for code that expects CosmosConversationClient
        self.container_client = None

    async def initialize(self, enable_message_feedback: bool = False):
        """Initialize the Cosmos DB client and containers"""
        try:
            self.enable_message_feedback = enable_message_feedback
            
            # For local development, handle non-HTTPS endpoints
            connection_policy = None
            if os.environ.get("DEVELOPMENT_MODE", "false").lower() == "true":
                cosmos_logger.info("Running in development mode - configuring CosmosDB for local development")
                # For local development with emulator or when running on HTTP
                import ssl
                connection_policy = {
                    "DisableSSLVerification": True
                }
            
            self.cosmosdb_client = CosmosClient(
                self.cosmosdb_endpoint, 
                credential=self.credential,
                connection_policy=connection_policy
            )
            self.database_client = self.cosmosdb_client.get_database_client(self.database_name)

            # Get container clients
            self.users_container = self.database_client.get_container_client("users")
            self.regions_container = self.database_client.get_container_client("regions")
            self.teams_container = self.database_client.get_container_client("teams")
            self.projects_container = self.database_client.get_container_client("projects")
            self.role_assignments_container = self.database_client.get_container_client("roleAssignments")
            self.conversations_project_container = self.database_client.get_container_client("conversations_project")

            # Set the container_client for compatibility with code expecting CosmosConversationClient
            # Point to projects container for project-related operations
            self.container_client = self.projects_container

            # Verify connection by reading database info
            try:
                await self.database_client.read()
                cosmos_logger.info(f"Successfully connected to CosmosDB database {self.database_name}")
            except exceptions.CosmosHttpResponseError as db_error:
                cosmos_logger.error(f"Failed to connect to CosmosDB database {self.database_name}: {db_error}")
                return False

            return True
        except exceptions.CosmosHttpResponseError as e:
            cosmos_logger.error(f"Error initializing Cosmos DB client: {e}")
            return False
        except Exception as e:
            cosmos_logger.error(f"Unexpected error initializing Cosmos DB client: {e}")
            return False

    async def close(self):
        """Close the Cosmos DB client"""
        if self.cosmosdb_client:
            await self.cosmosdb_client.close()

    # User Management Methods
    async def create_user(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new user in Cosmos DB"""
        user_id = user_data.get('id', str(uuid.uuid4()))
        now = datetime.now(timezone.utc).isoformat()

        user_doc = {
            'id': user_id,
            'type': 'user',
            'name': user_data.get('name'),
            'email': user_data.get('email'),
            'role': user_data.get('role'),
            'region': user_data.get('region'),
            'avatar': user_data.get('avatar'),
            'created_at': now,
            'updated_at': now
        }

        resp = await self.users_container.upsert_item(user_doc)
        return resp

    async def get_user(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get a user by ID"""
        try:
            user = await self.users_container.read_item(item=user_id, partition_key=user_id)
            return user
        except exceptions.CosmosResourceNotFoundError:
            return None

    async def update_user(self, user_id: str, user_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update a user by ID"""
        try:
            user = await self.users_container.read_item(item=user_id, partition_key=user_id)

            # Update fields
            for key, value in user_data.items():
                if key not in ['id', 'type', 'created_at']:
                    user[key] = value

            user['updated_at'] = datetime.now(timezone.utc).isoformat()

            updated_user = await self.users_container.replace_item(item=user_id, body=user)
            return updated_user
        except exceptions.CosmosResourceNotFoundError:
            return None

    async def delete_user(self, user_id: str) -> bool:
        """Delete a user by ID"""
        try:
            await self.users_container.delete_item(item=user_id, partition_key=user_id)
            return True
        except exceptions.CosmosResourceNotFoundError:
            return False

    async def get_users(self, filter_params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Get users with optional filtering"""
        query = "SELECT * FROM c WHERE c.type = 'user'"
        parameters = []

        if filter_params:
            param_index = 0
            for key, value in filter_params.items():
                param_name = f"@param{param_index}"
                query += f" AND c.{key} = {param_name}"
                parameters.append({"name": param_name, "value": value})
                param_index += 1

        users = []
        async for item in self.users_container.query_items(
            query=query,
            parameters=parameters
        ):
            users.append(item)

        return users

    # Region Management Methods
    async def create_region(self, region_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new region in Cosmos DB"""
        region_id = region_data.get('id', str(uuid.uuid4()))
        now = datetime.now(timezone.utc).isoformat()

        region_doc = {
            'id': region_id,
            'type': 'region',
            'name': region_data.get('name'),
            'description': region_data.get('description'),
            'cost_limit': region_data.get('cost_limit'),
            'created_by': region_data.get('created_by'),
            'created_at': now,
            'updated_at': now
        }

        resp = await self.regions_container.upsert_item(region_doc)
        return resp

    async def get_region(self, region_id: str) -> Optional[Dict[str, Any]]:
        """Get a region by ID"""
        try:
            region = await self.regions_container.read_item(item=region_id, partition_key=region_id)
            return region
        except exceptions.CosmosResourceNotFoundError:
            return None

    async def update_region(self, region_id: str, region_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update a region by ID"""
        try:
            region = await self.regions_container.read_item(item=region_id, partition_key=region_id)

            # Update fields
            for key, value in region_data.items():
                if key not in ['id', 'type', 'created_at', 'created_by']:
                    region[key] = value

            region['updated_at'] = datetime.now(timezone.utc).isoformat()

            updated_region = await self.regions_container.replace_item(item=region_id, body=region)
            return updated_region
        except exceptions.CosmosResourceNotFoundError:
            return None

    async def delete_region(self, region_id: str) -> bool:
        """Delete a region by ID"""
        try:
            await self.regions_container.delete_item(item=region_id, partition_key=region_id)
            return True
        except exceptions.CosmosResourceNotFoundError:
            return False

    async def get_regions(self) -> List[Dict[str, Any]]:
        """Get all regions"""
        query = "SELECT * FROM c WHERE c.type = 'region'"

        regions = []
        async for item in self.regions_container.query_items(
            query=query
        ):
            regions.append(item)

        return regions

    # Team Management Methods
    async def create_team(self, team_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new team in Cosmos DB"""
        team_id = team_data.get('id', str(uuid.uuid4()))
        now = datetime.now(timezone.utc).isoformat()

        team_doc = {
            'id': team_id,
            'type': 'team',
            'name': team_data.get('name'),
            'description': team_data.get('description'),
            'region': team_data.get('region'),
            'created_by': team_data.get('created_by'),
            'created_at': now,
            'updated_at': now
        }

        resp = await self.teams_container.upsert_item(team_doc)
        return resp

    async def get_team(self, team_id: str, region: str = None) -> Optional[Dict[str, Any]]:
        """Get a team by ID

        Args:
            team_id: The ID of the team to retrieve
            region: The region of the team (used as partition key)

        Returns:
            The team document if found, None otherwise
        """
        try:
            # If region is provided, use it as partition key
            if region:
                team = await self.teams_container.read_item(item=team_id, partition_key=region)
                return team

            # If region is not provided, try to find it using a query
            query = "SELECT * FROM c WHERE c.id = @teamId AND c.type = 'team'"
            parameters = [{"name": "@teamId", "value": team_id}]

            teams = []
            async for item in self.teams_container.query_items(
                query=query,
                parameters=parameters
            ):
                teams.append(item)

            if teams:
                return teams[0]

            return None
        except exceptions.CosmosResourceNotFoundError:
            return None

    async def update_team(self, team_id: str, region: str, team_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update a team by ID"""
        try:
            team = await self.teams_container.read_item(item=team_id, partition_key=region)

            # Update fields
            for key, value in team_data.items():
                if key not in ['id', 'type', 'region', 'created_at', 'created_by']:
                    team[key] = value

            team['updated_at'] = datetime.now(timezone.utc).isoformat()

            updated_team = await self.teams_container.replace_item(item=team_id, body=team)
            return updated_team
        except exceptions.CosmosResourceNotFoundError:
            return None

    async def delete_team(self, team_id: str, region: str) -> bool:
        """Delete a team by ID"""
        try:
            await self.teams_container.delete_item(item=team_id, partition_key=region)
            return True
        except exceptions.CosmosResourceNotFoundError:
            return False

    async def get_teams(self, region: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get teams with optional region filtering"""
        if region:
            query = "SELECT * FROM c WHERE c.type = 'team' AND c.region = @region"
            parameters = [{"name": "@region", "value": region}]
        else:
            query = "SELECT * FROM c WHERE c.type = 'team'"
            parameters = []

        teams = []
        async for item in self.teams_container.query_items(
            query=query,
            parameters=parameters
        ):
            teams.append(item)

        return teams

    # Project Management Methods
    async def create_project(self, project_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new project in Cosmos DB with minimal information.
        The actual resource names will be set by the deployment script.
        """
        project_id = project_data.get('id', str(uuid.uuid4()))
        now = datetime.now(timezone.utc).isoformat()

        # Only generate search resource names which are needed for consistency
        # The actual storage account and container names will be set by the deployment script
        project_name = project_data.get('name', '')
        sanitized_name = project_name.lower().replace(' ', '-')
        sanitized_name = ''.join(c for c in sanitized_name if c.isalnum() or c == '-')

        # Generate search resource names only - these are consistent between RBAC and deployment
        search_index_name = f"project-{sanitized_name}-index"
        search_datasource_name = f"project-{sanitized_name}-ds"
        search_indexer_name = f"project-{sanitized_name}-indexer"

        # Generate a random business icon if not provided
        business_icons = [
            '📊', '📈', '📉', '📋', '📝', '📑', '📚', '📁', '📂', '🗂️',
            '📒', '📓', '📔', '📕', '📗', '📘', '📙', '💼', '🗄️', '🖥️',
            '💻', '🖨️', '📱', '📞', '📧', '🔍', '🔎', '🔬', '🔭', '📡',
            '💡', '⚙️', '🔧', '🔨', '🛠️', '📌', '📍', '✂️', '📏', '📐',
            '🧮', '🌐', '🏢', '🏭', '🏗️', '🚀', '⏱️', '📅', '📆', '🗓️',
            '⏰', '🧠', '👥', '🤝', '🔐', '🔑'
        ]
        import random

        # Generate a random color gradient if not provided
        def get_random_color():
            return 'linear-gradient(135deg, #ffffff 0%, #add8e6 100%)'

        # Add default deployment status with detailed structure
        default_deployment_status = {
            "status": "pending",
            "message": "Project deployment started",
            "updated_at": now,
            "details": {
                "storage": {
                    "storage_account": False,
                    "containers": {
                        "uploads": False,
                        "input": False,
                        "output": False
                    }
                },
                "storage_complete": False,
                "search": {
                    "search_service": False,
                    "index": False,
                    "indexer": False,
                    "datasource": False
                },
                "search_complete": False,
                "function": {
                    "function_app": False,
                    "event_grid_topic": False,
                    "event_grid_system_topic": False,
                    "event_grid": False,
                    "maturity_assessment": False,
                    "executive_summary": False
                },
                "function_complete": False,
                "overall_complete": False,
                "completion_percentage": 0
            }
        }

        project_doc = {
            'id': project_id,
            'type': 'project',
            'name': project_data.get('name'),
            'description': project_data.get('description', ''),
            'region': project_data.get('region'),
            'owner': project_data.get('owner'),
            # Storage resources - these will be set by the deployment script
            'storage_account_name': None,
            'storage_container_uploads': None,
            'storage_container_input': None,
            'storage_container_output': None,
            # Search resources - only include the names that are consistent
            'search_service_name': None,
            'search_index_name': search_index_name,
            'search_datasource_name': search_datasource_name,
            'search_indexer_name': search_indexer_name,
            # Function app resources - these will be set by the deployment script
            'function_app_name': None,
            'function_names': None,
            # Event Grid resources - these will be set by the deployment script
            'event_grid_topic_name': None,
            'event_grid_system_topic_name': None,
            'event_grid_topic_endpoint': None,
            'event_grid_topic_key': None,
            # Other metadata
            'cost_limit': project_data.get('cost_limit'),
            'environment': project_data.get('environment', {}),
            'icon': project_data.get('icon', random.choice(business_icons)),
            'color': project_data.get('color', get_random_color()),
            'created_at': now,
            'updated_at': now,
            'deployment_status': default_deployment_status
        }

        # Save to the projects container only
        resp = await self.projects_container.upsert_item(project_doc)
        print(f"Saved RBAC project {project_id} to projects container")

        # Start the deployment process in a background thread
        import threading
        import subprocess
        import os
        import time

        # Set up a dedicated logger for deployment debugging
        deployment_logger = configure_deployment_logger(
            project_id=project_id,
            console_level=logging.INFO,  # Less verbose in console
            file_level=logging.DEBUG     # Very detailed in log file
        )

        # Get the log file path for reference in print statements
        logs_dir = ensure_logs_directory()
        deployment_log_file = os.path.join(logs_dir, f"deployment_{project_id}.log")

        def deploy_resources_background():
            try:
                # Get the current directory
                current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

                # Log detailed project information for debugging
                deployment_logger.info(f"Starting deployment for project {project_id}")
                deployment_logger.info(f"Project ID: {project_id}")
                deployment_logger.info(f"Project Name: {project_data.get('name')}")

                # Log Azure resource names
                deployment_logger.info("Azure resource names:")
                deployment_logger.info(f"Search Index Name: {project_doc['search_index_name']}")
                deployment_logger.info(f"Search Datasource Name: {project_doc['search_datasource_name']}")
                deployment_logger.info(f"Search Indexer Name: {project_doc['search_indexer_name']}")

                # Log environment information
                deployment_logger.debug("Environment information:")
                deployment_logger.debug(f"Current directory: {current_dir}")
                deployment_logger.debug(f"Python executable: {sys.executable}")
                deployment_logger.debug(f"Python version: {sys.version}")

                # Log CosmosDB information
                cosmos_logger.debug(f"CosmosDB endpoint: {self.cosmosdb_endpoint}")
                cosmos_logger.debug(f"CosmosDB database: {self.database_name}")
                cosmos_logger.debug("Starting deployment process with CosmosDB tracking")

                # Build the command
                cmd = [
                    "python",
                    os.path.join(current_dir, "deploy_project_resources.py"),
                    project_id,
                    project_data.get('name')
                ]

                # Add region_id if available
                if project_data.get('region'):
                    cmd.extend(["--region-id", project_data.get('region')])
                    deployment_logger.info(f"Using region ID: {project_data.get('region')} for deployment")

                deployment_logger.info(f"Starting deployment process for project {project_id} with command: {' '.join(cmd)}")
                print(f"Starting deployment process for project {project_id} with command: {' '.join(cmd)}")

                # Run the deployment script and capture output
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    cwd=current_dir
                )

                # Log the process ID
                deployment_logger.info(f"Deployment process started with PID: {process.pid}")
                print(f"Deployment process started for project {project_id} with PID: {process.pid}")

                # Stream and log the output in real-time
                deployment_logger.info("Streaming deployment process output...")

                # Log all environment variables for debugging (redacting sensitive info)
                env_vars = {k: '***REDACTED***' if 'key' in k.lower() or 'secret' in k.lower() or 'password' in k.lower() or 'token' in k.lower() else v
                           for k, v in os.environ.items()}
                deployment_logger.debug(f"Environment variables: {json.dumps(env_vars, indent=2)}")

                # Stream and log process output
                while True:
                    stdout_line = process.stdout.readline()
                    stderr_line = process.stderr.readline()

                    if stdout_line == '' and stderr_line == '' and process.poll() is not None:
                        break

                    if stdout_line:
                        # Log to file with DEBUG level (detailed)
                        deployment_logger.debug(f"STDOUT: {stdout_line.strip()}")
                        # Only log important info to console with INFO level
                        if any(keyword in stdout_line for keyword in ['created', 'deployed', 'success', 'completed', 'error', 'failed']):
                            deployment_logger.info(f"STDOUT: {stdout_line.strip()}")

                    if stderr_line:
                        # Always log errors at ERROR level
                        deployment_logger.error(f"STDERR: {stderr_line.strip()}")

                # Get the return code
                return_code = process.poll()
                deployment_logger.info(f"Deployment process completed with return code: {return_code}")

                print(f"Deployment process completed for project {project_id} with return code: {return_code}")
                print(f"See detailed logs in {deployment_log_file}")
            except Exception as e:
                error_msg = f"Error starting deployment process for project {project_id}: {e}"
                deployment_logger.error(error_msg)
                print(error_msg)

        # Start the deployment in a background thread
        deployment_thread = threading.Thread(target=deploy_resources_background)
        deployment_thread.daemon = True
        deployment_thread.start()

        # Log that we've started the deployment thread
        print(f"Deployment thread started for project {project_id}")
        print(f"Deployment logs will be written to {deployment_log_file}")

        return resp

    async def get_project(self, project_id: str, region: str = None, user_id: str = None) -> Optional[Dict[str, Any]]:
        """
        Get a project by ID

        Args:
            project_id: The ID of the project to retrieve
            region: The region of the project (used as partition key)
            user_id: Optional user ID (not used for direct retrieval but kept for compatibility)

        Returns:
            The project document if found, None otherwise
        """
        try:
            # If region is provided, use it as partition key
            if region:
                project = await self.projects_container.read_item(item=project_id, partition_key=region)
                return project

            # If region is not provided, try to find it using a query
            query = "SELECT * FROM c WHERE c.id = @projectId AND c.type = 'project'"
            parameters = [{"name": "@projectId", "value": project_id}]

            projects = []
            async for item in self.projects_container.query_items(
                query=query,
                parameters=parameters
            ):
                projects.append(item)

            if projects:
                return projects[0]

            return None
        except exceptions.CosmosResourceNotFoundError:
            return None

    async def get_project_by_id(self, project_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a project by ID without requiring region or user_id

        This is a convenience method for finding a project when only the ID is known.

        Args:
            project_id: The ID of the project to get

        Returns:
            The project document if found, None otherwise
        """
        try:
            # Try to find the project using a query
            query = "SELECT * FROM c WHERE c.id = @projectId AND c.type = 'project'"
            parameters = [{"name": "@projectId", "value": project_id}]

            projects = []
            async for item in self.projects_container.query_items(
                query=query,
                parameters=parameters
            ):
                projects.append(item)

            if projects:
                return projects[0]

            return None
        except exceptions.CosmosHttpResponseError as e:
            cosmos_logger.error(f"Error getting project by ID {project_id}: {e}")
            return None

    async def update_project(self, project_id: str, region: str = None, project_data: Dict[str, Any] = None, user_id: str = None, etag: str = None) -> Optional[Dict[str, Any]]:
        """
        Update a project by ID using optimistic concurrency control if an ETag is provided.

        Args:
            project_id: The ID of the project to update.
            region: The region of the project (used as partition key). Must be provided.
            project_data: The data to update in the project. Can be partial (only fields to update).
            user_id: Optional user ID (kept for signature compatibility, not used in core DB logic here).
            etag: Optional ETag for optimistic concurrency control.

        Returns:
            The updated project document if successful, None otherwise.

        Raises:
            exceptions.CosmosHttpResponseError: For ETag mismatch (412) or other Cosmos DB HTTP errors.
            ValueError: If essential parameters like project_data or region are missing.
        """
        if not project_data:
            cosmos_logger.error(f"Project data not provided for update of project {project_id}.")
            raise ValueError("project_data must be provided for update")

        try:
            # First, get the existing project document
            if not region:
                # If region is not provided, try to find it using a query
                existing_project = await self.get_project(project_id)
                if not existing_project:
                    cosmos_logger.warning(f"Project {project_id} not found during update attempt.")
                    return None

                region = existing_project.get('region')
                if not region:
                    cosmos_logger.error(f"Region not found in existing project {project_id}.")
                    raise ValueError("Region not found in existing project")
            else:
                # If region is provided, use it to get the project
                existing_project = await self.projects_container.read_item(item=project_id, partition_key=region)
                if not existing_project:
                    cosmos_logger.warning(f"Project {project_id} not found in region {region} during update attempt.")
                    return None

            # Update only the fields provided in project_data
            for key, value in project_data.items():
                if key not in ['id', 'type', 'created_at', 'created_by']:
                    existing_project[key] = value

            # Always update the 'updated_at' timestamp
            existing_project['updated_at'] = datetime.now(timezone.utc).isoformat()

            cosmos_logger.debug(f"Attempting to update project {project_id} in region {region} with etag: {etag}")

            # Handle potential API differences between SDK versions
            try:
                # First try without partition_key parameter since we're using SDK 4.5.1
                cosmos_logger.info("Using replace_item without partition_key parameter")

                # Ignore etag and just do a regular replace
                updated_project_doc = await self.projects_container.replace_item(
                    item=project_id,  # The ID of the item to replace
                    body=existing_project  # The updated document body
                )
            except TypeError as e:
                if "unexpected keyword argument 'partition_key'" not in str(e):
                    # Re-raise if it's a different TypeError
                    raise

                # If that fails, try with partition_key parameter as a fallback
                cosmos_logger.info("Retrying with partition_key parameter")

                # Ignore etag and just do a regular replace
                updated_project_doc = await self.projects_container.replace_item(
                    item=project_id,  # The ID of the item to replace
                    body=existing_project,  # The updated document body
                    partition_key=region
                )

            log_msg = f"Successfully updated project {project_id} (ignoring ETag)"
            cosmos_logger.info(log_msg)
            return updated_project_doc

        except exceptions.CosmosResourceNotFoundError:
            # This typically means the item to be replaced was not found.
            cosmos_logger.warning(f"Project {project_id} not found in region {region} during update attempt (replace_item).")
            return None # Or re-raise as a specific error if the caller needs to distinguish
        except exceptions.CosmosHttpResponseError as e:
            if e.status_code == 404: # Explicitly handle 404 if not caught by CosmosResourceNotFoundError
                cosmos_logger.warning(f"Project {project_id} not found (404) in region {region} during replace_item operation.")
                return None
            else:
                cosmos_logger.error(f"CosmosDB HTTP error updating project {project_id}: Status={e.status_code}, Message={e.message}")
                raise e  # Re-raise other Cosmos HTTP errors
        except Exception as e:
            cosmos_logger.error(f"Unexpected error updating project {project_id}: {type(e).__name__} - {e}")
            raise e # Re-raise other unexpected errors

    async def delete_project(self, project_id: str, region: str = None, user_id: str = None) -> bool:
        """
        Delete a project by ID

        Args:
            project_id: The ID of the project to delete
            region: The region of the project (used as partition key)
            user_id: Optional user ID (not used for direct deletion but kept for compatibility)

        Returns:
            True if the project was deleted, False otherwise
        """
        try:
            # If region is not provided, try to find it using a query
            if not region:
                # Get the project first to find its region
                project = await self.get_project(project_id)
                if not project:
                    return False

                region = project.get('region')
                if not region:
                    return False

            # Now delete the project with the region as partition key
            await self.projects_container.delete_item(item=project_id, partition_key=region)
            return True
        except exceptions.CosmosResourceNotFoundError:
            return False

    async def get_projects(self, region: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get projects with optional region filtering"""
        if region:
            query = "SELECT * FROM c WHERE c.type = 'project' AND c.region = @region"
            parameters = [{"name": "@region", "value": region}]
        else:
            query = "SELECT * FROM c WHERE c.type = 'project'"
            parameters = []

        projects = []
        async for item in self.projects_container.query_items(
            query=query,
            parameters=parameters
        ):
            projects.append(item)

        return projects

    # Role Assignment Methods
    async def add_user_to_team(self, team_id: str, user_id: str, role: TeamUserRole, created_by: str) -> Dict[str, Any]:
        """Add a user to a team with a specific role"""
        now = datetime.now(timezone.utc).isoformat()
        assignment_id = str(uuid.uuid4())

        assignment_doc = {
            'id': assignment_id,
            'type': 'teamMember',
            'userId': user_id,
            'teamId': team_id,
            'role': role,
            'created_by': created_by,
            'created_at': now
        }

        resp = await self.role_assignments_container.upsert_item(assignment_doc)
        return resp

    async def remove_user_from_team(self, team_id: str, user_id: str) -> bool:
        """Remove a user from a team"""
        query = "SELECT * FROM c WHERE c.type = 'teamMember' AND c.teamId = @teamId AND c.userId = @userId"
        parameters = [
            {"name": "@teamId", "value": team_id},
            {"name": "@userId", "value": user_id}
        ]

        assignments = []
        async for item in self.role_assignments_container.query_items(
            query=query,
            parameters=parameters,
            partition_key=user_id
        ):
            assignments.append(item)

        if not assignments:
            return False

        for assignment in assignments:
            await self.role_assignments_container.delete_item(
                item=assignment['id'],
                partition_key=user_id
            )

        return True

    async def get_team_members(self, team_id: str) -> List[Dict[str, Any]]:
        """Get all members of a team"""
        query = "SELECT * FROM c WHERE c.type = 'teamMember' AND c.teamId = @teamId"
        parameters = [{"name": "@teamId", "value": team_id}]

        assignments = []
        async for item in self.role_assignments_container.query_items(
            query=query,
            parameters=parameters
        ):
            assignments.append(item)

        return assignments

    async def get_user_teams(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all teams a user belongs to"""
        query = "SELECT * FROM c WHERE c.type = 'teamMember' AND c.userId = @userId"
        parameters = [{"name": "@userId", "value": user_id}]

        assignments = []
        async for item in self.role_assignments_container.query_items(
            query=query,
            parameters=parameters,
            partition_key=user_id
        ):
            assignments.append(item)

        return assignments

    async def assign_team_to_project(self, project_id: str, team_id: str, created_by: str) -> Dict[str, Any]:
        """Assign a team to a project"""
        now = datetime.now(timezone.utc).isoformat()
        assignment_id = str(uuid.uuid4())

        # Get the team to find its owner for partition key
        team_query = "SELECT * FROM c WHERE c.id = @teamId AND c.type = 'team'"
        team_parameters = [{"name": "@teamId", "value": team_id}]

        teams = []
        async for item in self.teams_container.query_items(
            query=team_query,
            parameters=team_parameters
        ):
            teams.append(item)

        if not teams:
            raise ValueError(f"Team with ID {team_id} not found")

        team = teams[0]

        assignment_doc = {
            'id': assignment_id,
            'type': 'projectTeam',
            'projectId': project_id,
            'teamId': team_id,
            'userId': team['created_by'],  # Use team creator's ID as partition key
            'created_by': created_by,
            'created_at': now
        }

        resp = await self.role_assignments_container.upsert_item(assignment_doc)
        return resp

    async def remove_team_from_project(self, project_id: str, team_id: str) -> bool:
        """Remove a team from a project"""
        query = "SELECT * FROM c WHERE c.type = 'projectTeam' AND c.projectId = @projectId AND c.teamId = @teamId"
        parameters = [
            {"name": "@projectId", "value": project_id},
            {"name": "@teamId", "value": team_id}
        ]

        assignments = []
        async for item in self.role_assignments_container.query_items(
            query=query,
            parameters=parameters
        ):
            assignments.append(item)

        if not assignments:
            return False

        for assignment in assignments:
            await self.role_assignments_container.delete_item(
                item=assignment['id'],
                partition_key=assignment['userId']
            )

        return True

    async def get_project_teams(self, project_id: str) -> List[Dict[str, Any]]:
        """Get all teams assigned to a project"""
        query = "SELECT * FROM c WHERE c.type = 'projectTeam' AND c.projectId = @projectId"
        parameters = [{"name": "@projectId", "value": project_id}]

        assignments = []
        async for item in self.role_assignments_container.query_items(
            query=query,
            parameters=parameters
        ):
            assignments.append(item)

        return assignments

    async def assign_user_to_project(self, project_id: str, user_id: str, role: ProjectUserRole, created_by: str) -> Dict[str, Any]:
        """Assign a user to a project with a specific role"""
        now = datetime.now(timezone.utc).isoformat()
        assignment_id = str(uuid.uuid4())

        assignment_doc = {
            'id': assignment_id,
            'type': 'projectUser',
            'projectId': project_id,
            'userId': user_id,
            'role': role,
            'created_by': created_by,
            'created_at': now
        }

        resp = await self.role_assignments_container.upsert_item(assignment_doc)
        return resp

    async def remove_user_from_project(self, project_id: str, user_id: str) -> bool:
        """Remove a user from a project"""
        query = "SELECT * FROM c WHERE c.type = 'projectUser' AND c.projectId = @projectId AND c.userId = @userId"
        parameters = [
            {"name": "@projectId", "value": project_id},
            {"name": "@userId", "value": user_id}
        ]

        assignments = []
        async for item in self.role_assignments_container.query_items(
            query=query,
            parameters=parameters,
            partition_key=user_id
        ):
            assignments.append(item)

        if not assignments:
            return False

        for assignment in assignments:
            await self.role_assignments_container.delete_item(
                item=assignment['id'],
                partition_key=user_id
            )

        return True

    async def get_project_users(self, project_id: str) -> List[Dict[str, Any]]:
        """Get all users assigned to a project"""
        query = "SELECT * FROM c WHERE c.type = 'projectUser' AND c.projectId = @projectId"
        parameters = [{"name": "@projectId", "value": project_id}]

        assignments = []
        async for item in self.role_assignments_container.query_items(
            query=query,
            parameters=parameters
        ):
            assignments.append(item)

        return assignments

    async def get_user_projects(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all projects a user is directly assigned to"""
        query = "SELECT * FROM c WHERE c.type = 'projectUser' AND c.userId = @userId"
        parameters = [{"name": "@userId", "value": user_id}]

        assignments = []
        async for item in self.role_assignments_container.query_items(
            query=query,
            parameters=parameters,
            partition_key=user_id
        ):
            assignments.append(item)

        return assignments

    # RBAC-specific methods
    async def get_accessible_users(self, user_id: str) -> List[Dict[str, Any]]:
        """Get users that the current user has permission to see based on RBAC rules"""
        current_user = await self.get_user(user_id)

        if not current_user:
            return []

        if current_user['role'] == UserRole.SUPER_ADMIN.value:
            # Super admins can see all users
            return await self.get_users()

        elif current_user['role'] == UserRole.REGIONAL_ADMIN.value:
            # Regional admins can see:
            # 1. Regional admins in their region
            # 2. Regular users in their region
            # 3. Themselves
            # They CANNOT see Super Admins
            region = current_user.get('region')
            if not region:
                return [current_user]  # Only see themselves if no region assigned

            query = """
            SELECT * FROM c
            WHERE c.type = 'user' AND
                  ((c.region = @region AND c.role != @superAdminRole) OR c.id = @userId)
            """

            parameters = [
                {"name": "@region", "value": region},
                {"name": "@superAdminRole", "value": UserRole.SUPER_ADMIN.value},
                {"name": "@userId", "value": user_id}
            ]

            users = []
            async for item in self.users_container.query_items(
                query=query,
                parameters=parameters
            ):
                users.append(item)

            return users

        elif current_user['role'] == UserRole.REGULAR_USER.value:
            # Regular users can only see users in their teams
            # First, get all teams the user belongs to
            user_team_assignments = await self.get_user_teams(user_id)

            if not user_team_assignments:
                return [current_user]  # Only see themselves if not in any team

            # Get all team members from user's teams
            team_members = set([user_id])  # Include the user themselves
            for assignment in user_team_assignments:
                team_id = assignment['teamId']
                team_member_assignments = await self.get_team_members(team_id)
                for member_assignment in team_member_assignments:
                    team_members.add(member_assignment['userId'])

            # Get user details for all team members
            users = []
            for member_id in team_members:
                user = await self.get_user(member_id)
                if user:
                    users.append(user)

            return users

        return []

    async def get_accessible_projects(self, user_id: str) -> List[Dict[str, Any]]:
        """Get projects that the current user has permission to access based on RBAC rules"""
        current_user = await self.get_user(user_id)

        if not current_user:
            return []

        if current_user['role'] == UserRole.SUPER_ADMIN.value:
            # Super admins can see all projects
            return await self.get_projects()

        elif current_user['role'] == UserRole.REGIONAL_ADMIN.value:
            # Regional admins can see all projects in their region
            region = current_user.get('region')
            if not region:
                return []

            return await self.get_projects(region)

        elif current_user['role'] == UserRole.REGULAR_USER.value:
            # Regular users can see:
            # 1. Projects they are directly assigned to
            # 2. Projects their teams are assigned to

            # Get directly assigned projects
            direct_project_assignments = await self.get_user_projects(user_id)
            direct_project_ids = [assignment['projectId'] for assignment in direct_project_assignments]

            # Get team assignments
            team_assignments = await self.get_user_teams(user_id)
            team_ids = [assignment['teamId'] for assignment in team_assignments]

            # Get projects assigned to these teams
            team_project_ids = set()
            if team_ids:
                # Fetch all projectTeam assignments for the user's teams in one go
                # Assuming team_ids is a list of strings
                # CosmosDB IN clause can typically handle a reasonable number of items.
                # If team_ids can be extremely large, batching might be needed,
                # but for typical scenarios, this should be more efficient.
                placeholders = ', '.join([f'@teamId{i}' for i in range(len(team_ids))])
                project_team_query = f"SELECT * FROM c WHERE c.type = 'projectTeam' AND c.teamId IN ({placeholders})"
                project_team_parameters = [{"name": f"@teamId{i}", "value": tid} for i, tid in enumerate(team_ids)]
                
                async for assignment in self.role_assignments_container.query_items(
                    query=project_team_query,
                    parameters=project_team_parameters
                    # enable_cross_partition_query=True # Consider if teamId isn't partition key for role_assignments
                ):
                    team_project_ids.add(assignment['projectId'])

            # Combine direct and team project IDs
            all_project_ids = list(set(direct_project_ids) | team_project_ids) # Convert to list for IN clause

            projects = []
            if all_project_ids:
                # Fetch all project details in a single query
                project_placeholders = ', '.join([f'@projectId{i}' for i in range(len(all_project_ids))])
                project_details_query = f"SELECT * FROM c WHERE c.type = 'project' AND c.id IN ({project_placeholders})"
                project_details_parameters = [{"name": f"@projectId{i}", "value": pid} for i, pid in enumerate(all_project_ids)]

                async for item in self.projects_container.query_items(
                    query=project_details_query,
                    parameters=project_details_parameters
                    # enable_cross_partition_query=True # If project ID is not the partition key or querying across partitions
                ):
                    projects.append(item)
            
            return projects

        return []

    async def get_team_projects(self, team_id: str) -> List[Dict[str, Any]]:
        """Get all projects a team is assigned to"""
        query = "SELECT * FROM c WHERE c.type = 'projectTeam' AND c.teamId = @teamId"
        parameters = [{"name": "@teamId", "value": team_id}]

        assignments = []
        async for item in self.role_assignments_container.query_items(
            query=query,
            parameters=parameters
        ):
            assignments.append(item)

        return assignments

    async def get_accessible_teams(self, user_id: str) -> List[Dict[str, Any]]:
        """Get teams that the current user has permission to see based on RBAC rules"""
        current_user = await self.get_user(user_id)

        if not current_user:
            return []

        if current_user['role'] == UserRole.SUPER_ADMIN.value:
            # Super admins can see all teams
            return await self.get_teams()

        elif current_user['role'] == UserRole.REGIONAL_ADMIN.value:
            # Regional admins can see all teams in their region
            region = current_user.get('region')
            if not region:
                return []

            return await self.get_teams(region)

        elif current_user['role'] == UserRole.REGULAR_USER.value:
            # Regular users can only see teams they are members of
            team_assignments = await self.get_user_teams(user_id)
            team_ids = [assignment['teamId'] for assignment in team_assignments]

            teams = []
            for team_id in team_ids:
                # We need to find the region for each team to query it
                team_query = "SELECT * FROM c WHERE c.id = @teamId AND c.type = 'team'"
                team_parameters = [{"name": "@teamId", "value": team_id}]

                team_results = []
                async for item in self.teams_container.query_items(
                    query=team_query,
                    parameters=team_parameters
                ):
                    team_results.append(item)

                if team_results:
                    teams.append(team_results[0])

            return teams

        return []

    # Conversation Methods
    async def ensure(self):
        """Ensure the Cosmos DB client is properly initialized"""
        if not self.cosmosdb_client or not self.database_client or not self.conversations_project_container:
            return False, "CosmosDB client not initialized correctly"
        try:
            await self.database_client.read()
        except exceptions.CosmosHttpResponseError as e:
            cosmos_logger.error(f"CosmosDB database error: {e}")
            return False, f"CosmosDB database {self.database_name} on account {self.cosmosdb_endpoint} not found: {str(e)}"
        except Exception as e:
            cosmos_logger.error(f"Unexpected error accessing CosmosDB database: {e}")
            return False, f"Unexpected error accessing CosmosDB database: {str(e)}"

        try:
            await self.conversations_project_container.read()
        except exceptions.CosmosHttpResponseError as e:
            cosmos_logger.error(f"CosmosDB container error: {e}")
            return False, f"CosmosDB container conversations_project not found: {str(e)}"
        except Exception as e:
            cosmos_logger.error(f"Unexpected error accessing CosmosDB container: {e}")
            return False, f"Unexpected error accessing CosmosDB container: {str(e)}"

        return True, "CosmosDB client initialized successfully"

    async def create_conversation(self, user_id: str, project_id: str, region_id: str, title: str = ''):
        """Create a new conversation with project and region information"""
        conversation = {
            'id': str(uuid.uuid4()),
            'type': 'conversation',
            'createdAt': datetime.now(timezone.utc).isoformat(),
            'updatedAt': datetime.now(timezone.utc).isoformat(),
            'userId': user_id,
            'project_id': project_id,
            'region_id': region_id,
            'title': title
        }

        resp = await self.conversations_project_container.upsert_item(conversation)
        if resp:
            return resp
        else:
            return False

    async def upsert_conversation(self, conversation):
        """Update an existing conversation"""
        resp = await self.conversations_project_container.upsert_item(conversation)
        if resp:
            return resp
        else:
            return False

    async def delete_conversation(self, user_id, conversation_id):
        """Delete a conversation by ID"""
        try:
            conversation = await self.conversations_project_container.read_item(item=conversation_id, partition_key=user_id)
            if conversation:
                resp = await self.conversations_project_container.delete_item(item=conversation_id, partition_key=user_id)
                return resp
            else:
                return True
        except exceptions.CosmosResourceNotFoundError:
            return True

    async def delete_messages(self, conversation_id, user_id):
        """Delete all messages in a conversation"""
        messages = await self.get_messages(user_id, conversation_id)
        response_list = []
        if messages:
            for message in messages:
                try:
                    resp = await self.conversations_project_container.delete_item(item=message['id'], partition_key=user_id)
                    response_list.append(resp)
                except exceptions.CosmosResourceNotFoundError:
                    pass
            return response_list
        return []

    async def get_conversations(self, user_id, project_id=None, region_id=None, limit=25, sort_order='DESC', offset=0):
        """Get conversations for a user with optional project and region filtering"""
        parameters = [
            {'name': '@userId', 'value': user_id}
        ]

        query = "SELECT * FROM c WHERE c.userId = @userId AND c.type = 'conversation'"

        if project_id:
            query += " AND c.project_id = @projectId"
            parameters.append({'name': '@projectId', 'value': project_id})

        if region_id:
            query += " AND c.region_id = @regionId"
            parameters.append({'name': '@regionId', 'value': region_id})

        query += f" ORDER BY c.updatedAt {sort_order}"

        if limit is not None:
            query += f" OFFSET {offset} LIMIT {limit}"

        conversations = []
        async for item in self.conversations_project_container.query_items(query=query, parameters=parameters):
            conversations.append(item)

        return conversations

    async def get_conversation(self, user_id, conversation_id):
        """Get a specific conversation by ID"""
        parameters = [
            {'name': '@conversationId', 'value': conversation_id},
            {'name': '@userId', 'value': user_id}
        ]

        query = "SELECT * FROM c WHERE c.id = @conversationId AND c.type = 'conversation' AND c.userId = @userId"

        conversations = []
        async for item in self.conversations_project_container.query_items(query=query, parameters=parameters):
            conversations.append(item)

        if len(conversations) == 0:
            return None
        else:
            return conversations[0]

    async def create_message(self, uuid_str, conversation_id, user_id, input_message: dict):
        """Create a new message in a conversation"""
        try:
            # First, get the conversation to get project_id and region_id
            conversation = await self.get_conversation(user_id, conversation_id)
            if not conversation:
                cosmos_logger.warning(f"Conversation {conversation_id} not found for user {user_id}")
                return {"error": "Conversation not found"}

            message = {
                'id': uuid_str,
                'type': 'message',
                'userId': user_id,
                'createdAt': datetime.now(timezone.utc).isoformat(),
                'updatedAt': datetime.now(timezone.utc).isoformat(),
                'conversationId': conversation_id,
                'project_id': conversation.get('project_id'),
                'region_id': conversation.get('region_id'),
                'role': input_message['role'],
                'content': input_message['content']
            }

            if self.enable_message_feedback:
                message['feedback'] = ''

            try:
                resp = await self.conversations_project_container.upsert_item(message)
                if resp:
                    # Update the parent conversation's updatedAt field
                    conversation['updatedAt'] = message['createdAt']
                    await self.upsert_conversation(conversation)
                    return resp
                else:
                    cosmos_logger.error(f"Failed to upsert message {uuid_str} in conversation {conversation_id}")
                    return {"error": "Failed to create message"}
            except exceptions.CosmosHttpResponseError as e:
                cosmos_logger.error(f"CosmosDB error creating message: {e}")
                return {"error": f"CosmosDB error: {str(e)}"}
        except KeyError as e:
            cosmos_logger.error(f"Missing required field in input_message: {e}")
            return {"error": f"Missing required field: {str(e)}"}
        except Exception as e:
            cosmos_logger.error(f"Unexpected error creating message: {e}")
            return {"error": f"Unexpected error: {str(e)}"}

    async def update_message_feedback(self, user_id, message_id, feedback):
        """Update feedback for a message"""
        try:
            message = await self.conversations_project_container.read_item(item=message_id, partition_key=user_id)
            if message:
                message['feedback'] = feedback
                message['updatedAt'] = datetime.now(timezone.utc).isoformat()
                resp = await self.conversations_project_container.upsert_item(message)
                return resp
            else:
                cosmos_logger.warning(f"Message {message_id} not found for user {user_id} when updating feedback")
                return {"error": "Message not found"}
        except exceptions.CosmosResourceNotFoundError as e:
            cosmos_logger.warning(f"Message {message_id} not found for user {user_id}: {e}")
            return {"error": "Message not found"}
        except exceptions.CosmosHttpResponseError as e:
            cosmos_logger.error(f"CosmosDB error updating message feedback: {e}")
            return {"error": f"CosmosDB error: {str(e)}"}
        except Exception as e:
            cosmos_logger.error(f"Unexpected error updating message feedback: {e}")
            return {"error": f"Unexpected error: {str(e)}"}

    async def get_messages(self, user_id, conversation_id):
        """Get all messages in a conversation"""
        try:
            parameters = [
                {'name': '@conversationId', 'value': conversation_id},
                {'name': '@userId', 'value': user_id}
            ]

            query = "SELECT * FROM c WHERE c.conversationId = @conversationId AND c.type = 'message' AND c.userId = @userId ORDER BY c.createdAt ASC"

            messages = []
            try:
                async for item in self.conversations_project_container.query_items(
                    query=query,
                    parameters=parameters,
                    max_item_count=100  # Limit to prevent excessive memory usage
                ):
                    messages.append(item)

                cosmos_logger.info(f"Retrieved {len(messages)} messages for conversation {conversation_id}")
                return messages
            except exceptions.CosmosHttpResponseError as e:
                cosmos_logger.error(f"CosmosDB error retrieving messages: {e}")
                return []
        except Exception as e:
            cosmos_logger.error(f"Unexpected error retrieving messages: {e}")
            return []
