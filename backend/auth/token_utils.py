"""
Token Utilities for Delegated Authentication Flow

This module provides utilities for extracting and validating tokens from request headers,
and using them for delegated authentication with Microsoft Graph API.
"""

import logging
import time
from typing import Dict, Any, Optional, Tuple
import jwt
import requests
from fastapi import Request, HTTPException, status
from backend.auth.mock_auth import get_mock_user

# Cache for token validation results to avoid unnecessary API calls
_token_validation_cache = {}
# Cache expiry time in seconds
TOKEN_CACHE_EXPIRY = 300  # 5 minutes

def extract_token_from_request(request: Request) -> Optional[str]:
    """
    Extract the Bearer token from the Authorization header.

    Args:
        request: The FastAPI request object

    Returns:
        The token string or None if not found
    """
    # Log all headers for debugging (excluding sensitive information)
    safe_headers = {k: v for k, v in request.headers.items()
                   if k.lower() not in ('authorization', 'cookie', 'x-api-key')}
    logging.info(f"Request headers: {safe_headers}")

    auth_header = request.headers.get("Authorization")
    if not auth_header:
        logging.warning("No Authorization header found in request")
        return None

    # Log that we found an Authorization header (without showing the full token)
    if len(auth_header) > 20:
        logging.info(f"Found Authorization header: {auth_header[:10]}...{auth_header[-10:]} (truncated)")
    else:
        logging.info(f"Found Authorization header with format: {auth_header}")

    parts = auth_header.split()
    if len(parts) != 2 or parts[0].lower() != "bearer":
        logging.warning(f"Invalid Authorization header format: {parts[0] if len(parts) > 0 else 'empty'}")
        return None

    token = parts[1]
    logging.info(f"Successfully extracted token: {token[:10]}...{token[-10:]} (truncated)")
    return token

async def validate_token(token: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
    """
    Validate the token and extract its claims.

    Args:
        token: The JWT token to validate

    Returns:
        A tuple of (is_valid, claims)
    """
    # Check cache first
    cache_key = token
    if cache_key in _token_validation_cache:
        cache_entry = _token_validation_cache[cache_key]
        if time.time() < cache_entry["expires_at"]:
            return cache_entry["is_valid"], cache_entry["claims"]

    try:
        # For basic validation, we'll just decode the token without verification
        # In a production environment, you should verify the token signature
        # using the public key from the issuer

        # Handle token type issues - ensure we're working with a string
        if isinstance(token, bytes):
            token_str = token.decode('utf-8')
        else:
            token_str = str(token)

        claims = jwt.decode(token_str, options={"verify_signature": False})

        # Check if token is expired
        if "exp" in claims and claims["exp"] < time.time():
            logging.warning("Token is expired")
            _cache_validation_result(token, False, None)
            return False, None

        # Cache the result
        _cache_validation_result(token, True, claims)
        return True, claims
    except Exception as e:
        logging.error(f"Error validating token: {e}")
        _cache_validation_result(token, False, None)
        return False, None

def _cache_validation_result(token: str, is_valid: bool, claims: Optional[Dict[str, Any]]) -> None:
    """Cache the token validation result"""
    _token_validation_cache[token] = {
        "is_valid": is_valid,
        "claims": claims,
        "expires_at": time.time() + TOKEN_CACHE_EXPIRY
    }

async def get_user_info_from_graph(token: str) -> Optional[Dict[str, Any]]:
    """
    Get user information from Microsoft Graph API using the delegated token.

    Args:
        token: The delegated access token

    Returns:
        User information or None if the request fails
    """
    if not token:
        logging.warning("No token provided to get_user_info_from_graph")
        return None

    try:
        # Call Microsoft Graph API to get user info
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }

        logging.info("Calling Microsoft Graph API with delegated token")
        logging.debug(f"Token: {token[:10]}...{token[-10:]} (truncated)")

        response = requests.get(
            "https://graph.microsoft.com/v1.0/me",
            headers=headers
        )

        if response.status_code == 200:
            user_data = response.json()
            logging.info(f"Successfully retrieved user info from Graph API: {user_data.get('displayName')}")

            return {
                "id": user_data.get("id"),
                "name": user_data.get("displayName"),
                "email": user_data.get("userPrincipalName"),
                # Don't set a default role here - it will be set from the database
                # or defaulted to REGULAR_USER later if not found in the database
            }
        else:
            logging.error(f"Error getting user info from Graph API: {response.status_code} - {response.text}")
            # Log more details about the token
            try:
                import jwt
                token_parts = token.split('.')
                if len(token_parts) == 3:  # Valid JWT format
                    header = jwt.decode_complete(token, options={"verify_signature": False})["header"]
                    payload = jwt.decode_complete(token, options={"verify_signature": False})["payload"]
                    logging.info(f"Token header: {header}")
                    logging.info(f"Token payload aud: {payload.get('aud')}")
                    logging.info(f"Token payload scp: {payload.get('scp')}")
                    if 'User.Read' not in str(payload.get('scp', '')):
                        logging.error("Token does not have User.Read scope")
                else:
                    logging.warning("Token is not in valid JWT format")
            except Exception as jwt_error:
                logging.error(f"Error decoding token: {jwt_error}")

            return None
    except Exception as e:
        logging.error(f"Error calling Microsoft Graph API: {e}")
        return None

async def get_authenticated_user_with_token(request: Request) -> Dict[str, Any]:
    """
    Get the authenticated user using the token from the request.

    This function extracts the token from the request, validates it,
    and uses it to get user information from Microsoft Graph API.
    If the token is invalid or missing, it falls back to the mock user.

    Args:
        request: The FastAPI request object

    Returns:
        User information
    """
    # Extract token from request
    token = extract_token_from_request(request)
    if not token:
        logging.warning("No token found in request, using mock user")
        return await get_mock_user()

    # Validate token
    is_valid, claims = await validate_token(token)
    if not is_valid:
        logging.warning("Invalid token, using mock user")
        return await get_mock_user()

    # Get user info from Microsoft Graph API
    user_info = await get_user_info_from_graph(token)
    if not user_info:
        logging.warning("Failed to get user info from Graph API, using mock user")
        return await get_mock_user()

    return user_info
