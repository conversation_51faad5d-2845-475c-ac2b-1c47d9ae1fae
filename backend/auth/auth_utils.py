import logging
from quart import Blueprint, jsonify, request, make_response
import os
import base64
import json
from typing import Dict, Any

async def get_authenticated_user_details(request_headers): # request_headers might be Quart's request.headers
    user_object = {}
    raw_user_object = None

    # Check if we're in development mode
    is_development = os.environ.get('DEVELOPMENT_MODE', 'false').lower() == 'true'

    # Log the headers for debugging
    logging.debug(f"Auth headers: {request_headers}")

    # Extract token from request and validate
    token = request_headers.get('Authorization', '').replace('Bearer ', '')
    if not token:
        logging.warning("No authorization token found in request")
        return None

    # Validate token
    try:
        from backend.auth.token_utils import validate_token
        is_valid, claims = await validate_token(token)
        if not is_valid:
            logging.warning("Invalid authentication token")
            return None

        # Create user object from token claims
        raw_user_object = {
            'X-Ms-Client-Principal-Idp': 'aad',
            'X-Ms-Token-Aad-Id-Token': claims.get('_raw', None),
            'access_token': token
        }
        logging.info(f"User details from token: {claims.get('email')}")
    except Exception as e:
        logging.error(f"Error validating token: {e}")
        return None

    # 3. If still no user, and in development mode, use mock user
    if not raw_user_object and is_development:
        raw_user_object = create_development_user()
        logging.info("Using development user.")
    
    # 4. If no auth info at all (no headers)
    if not raw_user_object:
        logging.warning("No authentication information found (no headers) and not in development mode. Falling back to default mock user.")
        raw_user_object = create_development_user() # Fallback to prevent errors, but this indicates an issue

    # Extract user details into the final user_object
    user_object['user_principal_id'] = raw_user_object.get('X-Ms-Client-Principal-Id')
    user_object['user_name'] = raw_user_object.get('X-Ms-Client-Principal-Name')
    user_object['user_email'] = raw_user_object.get('X-Ms-Client-Principal-Email', '<EMAIL>') # Default email
    user_object['auth_provider'] = raw_user_object.get('X-Ms-Client-Principal-Idp', 'unknown')
    
    # Use token from request headers
    # Otherwise, fall back to X-Ms-Token-Aad-Id-Token (for EasyAuth)
    user_object['auth_token'] = raw_user_object.get('access_token') or raw_user_object.get('X-Ms-Token-Aad-Id-Token')
    
    user_object['client_principal_b64'] = request_headers.get('X-Ms-Client-Principal') # Keep for potential downstream use
    user_object['aad_id_token'] = raw_user_object.get('X-Ms-Token-Aad-Id-Token') # Keep for potential downstream use

    # Handle roles: if it's a list, use it, otherwise wrap string in a list. Default to ['User']
    roles_from_raw = raw_user_object.get('X-Ms-Client-Principal-Role', 'User')
    if isinstance(roles_from_raw, list):
        user_object['roles'] = roles_from_raw if roles_from_raw else ['User']
    else:
        user_object['roles'] = [roles_from_raw]

    return user_object

def create_development_user() -> Dict[str, Any]:
    """Create a mock user for development purposes"""
    return {
        'X-Ms-Client-Principal-Id': '11111111-1111-1111-1111-111111111111',
        'X-Ms-Client-Principal-Name': 'Super Admin',
        'X-Ms-Client-Principal-Email': '<EMAIL>',
        'X-Ms-Client-Principal-Idp': 'aad',
        'X-Ms-Client-Principal-Role': 'SuperAdmin'
    }


auth_bp = Blueprint("auth", __name__, url_prefix="/api/auth")

@auth_bp.route("/logout", methods=["POST"])
async def logout():
    try:
        # Create a response using await
        response = await make_response(jsonify({"message": "Logged out successfully"}))

        # Set cookies to expire immediately
        response.set_cookie("session", "", expires=0)
        response.set_cookie(".AspNetCore.Cookies", "", expires=0)
        response.set_cookie("AppServiceAuthSession", "", expires=0)

        # Get Azure logout URL from environment variable with a fallback to a well-known Azure AD logout endpoint
        auth_provider = request.headers.get('X-Ms-Client-Principal-Idp', '').lower()

        # Default logout URL (if no specific provider is detected)
        logout_url = os.getenv("AUTH_LOGOUT_ENDPOINT", "/")

        # For Azure AD (aad), use the Azure AD logout endpoint
        if auth_provider == 'aad':
            post_logout_redirect = f"{request.host_url.rstrip('/')}"
            logout_url = f"https://login.microsoftonline.com/common/oauth2/v2.0/logout?post_logout_redirect_uri={post_logout_redirect}"
            logging.info(f"Using AAD logout URL: {logout_url}")
        # For Microsoft Account (microsoftaccount)
        elif auth_provider == 'microsoftaccount':
            logout_url = "https://login.live.com/oauth20_logout.srf"
            logging.info(f"Using Microsoft Account logout URL: {logout_url}")
        # If EasyAuth is configured, try the standard EasyAuth logout endpoint
        elif os.getenv("WEBSITE_AUTH_ENABLED") == "True":
            logout_url = "/.auth/logout"
            logging.info(f"Using EasyAuth logout URL: {logout_url}")

        logging.info(f"Final Logout URL: {logout_url}")

        # Return the logout URL to the client
        return jsonify({"logoutUrl": logout_url})

    except Exception as e:
        logging.error(f"Logout error: {str(e)}")
        # Add more detailed error logging
        logging.exception("Full logout error details:")
        return jsonify({"error": str(e), "fallbackUrl": "/"}, 500)
