"""
Entra ID Authentication Client for local development.

This module provides functions to authenticate with Entra ID using client credentials
from environment variables, allowing local development to use the same authentication
as the production environment.

It also supports delegated authentication flow, where the frontend passes a token
to the backend, which then uses it to access Microsoft Graph API.
"""

import os
import logging
import json
import time
from typing import Dict, Any, Optional
import requests
from fastapi import Request, HTTPException, status
from azure.identity import ClientSecretCredential
from backend.auth.mock_auth import get_mock_user
from backend.auth.token_utils import extract_token_from_request, get_user_info_from_graph

# Get environment variables
AZURE_CLIENT_ID = os.environ.get("AZURE_CLIENT_ID")
AZURE_TENANT_ID = os.environ.get("AZURE_TENANT_ID")
AZURE_APP_SECRET = os.environ.get("AZURE_APP_SECRET")

# Cache for access tokens to avoid unnecessary requests
_token_cache = {}
# Allow falling back to client credentials when no delegated token is supplied
ALLOW_CLIENT_CREDENTIALS_FALLBACK = os.environ.get("ALLOW_CLIENT_CREDENTIALS_FALLBACK", "false").lower() == "true"

def is_entra_auth_configured() -> bool:
    """Check if Entra ID authentication is configured with environment variables."""
    return all([AZURE_CLIENT_ID, AZURE_TENANT_ID, AZURE_APP_SECRET])

async def get_authenticated_user(request: Request) -> Dict[str, Any]:
    """
    Get the authenticated user information from the request.

    This function validates the token and retrieves user information from Microsoft Graph.
    """
    try:
        token = extract_token_from_request(request)
        if not token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="No authentication token provided"
            )

        user_info = await get_user_info_from_graph(token)
        if not user_info:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Failed to validate token"
            )

        return user_info
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error getting authenticated user: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to authenticate user"
        )

async def get_entra_token() -> Optional[str]:
    """Get an access token for Entra ID authentication."""
    if not is_entra_auth_configured():
        logging.warning("Entra ID authentication not configured. Missing environment variables.")
        return None

    # Check cache first
    cache_key = f"{AZURE_CLIENT_ID}:{AZURE_TENANT_ID}"
    if cache_key in _token_cache:
        token_data = _token_cache[cache_key]
        # Check if token is still valid (with 5 minute buffer)
        if token_data["expires_at"] > time.time() + 300:
            return token_data["access_token"]

    try:
        # Create a credential object
        credential = ClientSecretCredential(
            tenant_id=AZURE_TENANT_ID,
            client_id=AZURE_CLIENT_ID,
            client_secret=AZURE_APP_SECRET
        )

        # Get token for Microsoft Graph API
        token = credential.get_token("https://graph.microsoft.com/.default")

        # Cache the token
        _token_cache[cache_key] = {
            "access_token": token.token,
            "expires_at": time.time() + token.expires_on
        }

        return token.token
    except Exception as e:
        logging.error(f"Error getting Entra ID token: {e}")
        return None

async def get_user_info(token: str) -> Optional[Dict[str, Any]]:
    """Get user information from Microsoft Graph API."""
    if not token:
        return None

    try:
        # Call Microsoft Graph API to get user info
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }

        response = requests.get(
            "https://graph.microsoft.com/v1.0/me",
            headers=headers
        )

        if response.status_code == 200:
            user_data = response.json()
            return {
                "id": user_data.get("id"),
                "name": user_data.get("displayName"),
                "email": user_data.get("userPrincipalName")
                # Don't set a default role here - it will be set from the database
                # or defaulted to REGULAR_USER later if not found in the database
            }
        else:
            logging.error(f"Error getting user info: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        logging.error(f"Error calling Microsoft Graph API: {e}")
        return None

async def get_entra_user() -> Dict[str, Any]:
    """Get user information from Entra ID for local development.

    This function attempts to authenticate with Entra ID using client credentials
    from environment variables. If successful, it returns user information from
    Microsoft Graph API. If not, it falls back to the mock user.
    """
    if not is_entra_auth_configured():
        logging.info("Entra ID authentication not configured. Using mock user.")
        return await get_mock_user()

    token = await get_entra_token()
    if not token:
        logging.warning("Failed to get Entra ID token. Using mock user.")
        return await get_mock_user()

    user_info = await get_user_info(token)
    if not user_info:
        logging.warning("Failed to get user info from Microsoft Graph API. Using mock user.")
        return await get_mock_user()

    # Convert to the format expected by the application
    user_data = {
        "id": user_info["id"],
        "name": user_info["name"],
        "email": user_info["email"]
    }

    # Only set a role if one was provided
    if "role" in user_info:
        user_data["role"] = user_info["role"]
    else:
        # Default to REGULAR_USER if no role is provided
        user_data["role"] = "REGULAR_USER"

    return user_data

async def get_entra_user_with_delegated_token(request: Request) -> Dict[str, Any]:
    """Get user information from Entra ID using a delegated token from the request.

    This function extracts the token from the request headers and uses it to get
    user information from Microsoft Graph API. If the token is missing or invalid,
    it falls back to the client credentials flow or mock user.

    Args:
        request: The FastAPI request object

    Returns:
        User information
    """
    # Extract token from request
    token = extract_token_from_request(request)
    if not token:
        logging.warning("No delegated token found in request.")
        if ALLOW_CLIENT_CREDENTIALS_FALLBACK:
            logging.info("Falling back to client credentials.")
            return await get_entra_user()
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Authorization token required")

    # Use the token to get user info from Microsoft Graph API
    user_info = await get_user_info_from_graph(token)
    if not user_info:
        logging.warning("Failed to get user info with delegated token.")
        if ALLOW_CLIENT_CREDENTIALS_FALLBACK:
            logging.info("Falling back to client credentials.")
            return await get_entra_user()
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")

    # Convert to the format expected by the application
    # Ensure we have a default role if none was provided by the Graph API
    user_data = {
        "id": user_info["id"],
        "name": user_info["name"],
        "email": user_info["email"]
    }

    # Only set a default role if one wasn't provided by the Graph API
    # This allows the role to be set from the database in get_authenticated_user
    if "role" in user_info:
        user_data["role"] = user_info["role"]
    else:
        # Default to REGULAR_USER if no role is provided
        # This will be overridden by the database role in get_authenticated_user
        user_data["role"] = "REGULAR_USER"

    return user_data