import os
import logging
from datetime import datetime, timedelta

try:
    from azure.identity import DefaultAzureCredential
    from azure.mgmt.costmanagement import CostManagementClient
    from azure.mgmt.costmanagement.models import QueryDefinition, QueryTimePeriod, QueryDataset, QueryAggregation
except Exception:  # pragma: no cover - optional dependency
    DefaultAzureCredential = None
    CostManagementClient = None
    QueryDefinition = None
    QueryTimePeriod = None
    QueryDataset = None
    QueryAggregation = None

logger = logging.getLogger(__name__)


class CostManagementService:
    """Service to query Azure Cost Management API."""

    def __init__(self):
        self.subscription_id = os.environ.get("AZURE_SUBSCRIPTION_ID")
        if CostManagementClient and DefaultAzureCredential and self.subscription_id:
            credential = DefaultAzureCredential()
            self.client = CostManagementClient(credential, self.subscription_id)
        else:  # pragma: no cover - skip if SDK not available
            self.client = None

    def _get_time_period(self, time_range: str) -> QueryTimePeriod:
        """Compute time period based on time range string."""
        end_date = datetime.utcnow()
        if time_range == "week":
            start_date = end_date - timedelta(days=7)
        elif time_range == "month":
            start_date = end_date.replace(day=1)
        elif time_range == "quarter":
            month = ((end_date.month - 1) // 3) * 3 + 1
            start_date = end_date.replace(month=month, day=1)
        elif time_range == "year":
            start_date = end_date.replace(month=1, day=1)
        else:
            start_date = end_date - timedelta(days=30)
        if QueryTimePeriod:
            return QueryTimePeriod(from_property=start_date, to=end_date)
        return None

    async def query_cost_by_tag(self, time_range: str, tag_name: str, tag_value: str | None = None):
        """Query cost data filtered by tag."""
        if not self.client:
            logger.warning("CostManagementClient not configured; returning mock data")
            # Return mock data for development
            class MockResult:
                def __init__(self):
                    self.rows = [
                        ["project-1", 150.25],
                        ["project-2", 275.50],
                        ["project-3", 89.75]
                    ]
            return MockResult()

        time_period = self._get_time_period(time_range)

        tag_filter = None
        if tag_value:
            tag_filter = f"tags['{tag_name}'] eq '{tag_value}'"
        elif tag_name:

            tag_filter = f"tags['{tag_name}']"


        dataset = QueryDataset(
            granularity="None",
            aggregation={"totalCost": QueryAggregation(name="Cost", function="Sum")},

            filter=tag_filter,
        )

        query = QueryDefinition(
            type="ActualCost",
            timeframe="Custom",
            time_period=time_period,
            dataset=dataset,
        )

        scope = f"/subscriptions/{self.subscription_id}"
        return self.client.query.usage(scope=scope, parameters=query)

    async def query_cost_by_resource(self, time_range: str, resource_group: str | None = None, resource_type: str | None = None):

        """Query cost data grouped by resource."""
        if not self.client:
            logger.warning("CostManagementClient not configured; returning mock data")
            # Return mock data for development
            class MockResult:
                def __init__(self):
                    self.rows = [
                        ["storage-account-1", 45.50],
                        ["search-service-1", 125.00],
                        ["function-app-1", 32.75],
                        ["cosmosdb-1", 98.25]
                    ]
            return MockResult()

        time_period = self._get_time_period(time_range)


        filters: list[str] = []

        if resource_group:
            filters.append(f"ResourceGroup eq '{resource_group}'")
        if resource_type:
            filters.append(f"ResourceType eq '{resource_type}'")

        filter_str = " and ".join(filters) if filters else None

        dataset = QueryDataset(
            granularity="None",
            aggregation={"totalCost": QueryAggregation(name="Cost", function="Sum")},
            grouping=[{"type": "Dimension", "name": "ResourceId"}],
            filter=filter_str,
        )

        query = QueryDefinition(
            type="ActualCost",
            timeframe="Custom",
            time_period=time_period,
            dataset=dataset,
        )

        scope = f"/subscriptions/{self.subscription_id}"
        return self.client.query.usage(scope=scope, parameters=query)
