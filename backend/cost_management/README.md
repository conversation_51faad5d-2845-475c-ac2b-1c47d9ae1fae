# Cost management package

This package provides APIs and scheduled tasks for collecting and reporting Azure cost data.

## Scheduled Cost Collection

`CostCollectionTask` queries Azure Cost Management and stores the results in the `cost_data` container of your Cosmos DB account. The scheduler defined in `backend/cost_management/__init__.py` runs this task daily at 01:00 UTC using `aioschedule`.

### Required Environment Variables

- `AZURE_SUBSCRIPTION_ID` – subscription used for the Cost Management API
- `AZURE_STORAGE_CONNECTION_STRING` – connection string for storage metrics
- `AZURE_SEARCH_SERVICE_ENDPOINT` – search service endpoint
- `AZURE_SEARCH_API_KEY` – search service API key
- `COSMOSDB_ENDPOINT` – Cosmos DB account endpoint
- `COSMOSDB_KEY` – Cosmos DB account key
- `COSMOSDB_DATABASE` – Cosmos DB database name containing the `cost_data` container
