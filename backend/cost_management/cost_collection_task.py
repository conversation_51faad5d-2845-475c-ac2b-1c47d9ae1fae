import asyncio
import logging
from datetime import datetime
import os
from typing import Any

from azure.cosmos.aio import CosmosClient

from .cost_management_service import CostManagementService
from .cost_allocation_service import CostAllocationService

logger = logging.getLogger(__name__)


class CostCollectionTask:
    """Collect cost data from Azure and store it in Cosmos DB."""

    def __init__(self) -> None:
        # Initialize services
        self.cost_management_service = CostManagementService()
        self.cost_allocation_service = CostAllocationService()

        # Initialize Cosmos DB client
        cosmos_endpoint = os.environ.get("COSMOSDB_ENDPOINT")
        cosmos_key = os.environ.get("COSMOSDB_KEY")
        cosmos_database = os.environ.get("COSMOSDB_DATABASE")

        self.cosmos_client = CosmosClient(cosmos_endpoint, credential=cosmos_key)
        self.database = self.cosmos_client.get_database_client(cosmos_database)
        self.cost_container = self.database.get_container_client("cost_data")

    async def collect_and_store_cost_data(self) -> None:
        """Query costs and persist them to Cosmos DB."""
        try:
            logger.info("Starting cost data collection task")
            timestamp = datetime.utcnow().isoformat()

            # Query cost information using the management service
            costs = await self.cost_management_service.query_cost_by_tag(
                "month", "Project"
            )
            if hasattr(costs, "as_dict"):
                costs_data: Any = costs.as_dict()
            else:
                costs_data = costs

            # Assemble document
            doc = {
                "id": f"cost-{timestamp}",
                "timestamp": timestamp,
                "rawCosts": costs_data,
            }

            await self.cost_container.upsert_item(doc)
            logger.info("Cost data collection completed successfully")
        except Exception as exc:  # pragma: no cover - external dependency
            logger.error(f"Error in cost data collection task: {exc}")

    async def collect_resource_costs(self) -> list:
        """Placeholder for resource-level cost collection."""
        return []

    async def collect_container_costs(self, resource_costs: list) -> dict:
        """Placeholder for container cost allocation."""
        return {}

    async def collect_indexer_costs(self, resource_costs: list) -> dict:
        """Placeholder for indexer cost allocation."""
        return {}

    def aggregate_project_costs(
        self, resource_costs: list, container_costs: dict, indexer_costs: dict
    ) -> list:
        """Placeholder for project cost aggregation."""
        return []

    def aggregate_service_costs(self, resource_costs: list) -> list:
        """Placeholder for service cost aggregation."""
        return []

    def aggregate_region_costs(self, resource_costs: list) -> list:
        """Placeholder for region cost aggregation."""
        return []


async def run_cost_collection_task() -> None:
    """Execute the cost collection task."""
    task = CostCollectionTask()
    await task.collect_and_store_cost_data()


def schedule_cost_collection() -> None:
    """Schedule the daily cost collection task."""
    import aioschedule

    aioschedule.every().day.at("01:00").do(
        lambda: asyncio.create_task(run_cost_collection_task())
    )

    async def _runner() -> None:
        while True:
            await aioschedule.run_pending()
            await asyncio.sleep(60)

    asyncio.get_event_loop().create_task(_runner())
