# backend/aisearch/test_ai_search_status.py

import pytest
from httpx import AsyncClient
from fastapi.testclient import TestClient
from ai_search_status import app

@pytest.mark.asyncio
async def test_get_indexed_files():
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.get("/api/search/indexed-files/ai-scope-app1")
    assert response.status_code == 200
    assert isinstance(response.json(), list)

@pytest.mark.asyncio
async def test_get_indexer_status():
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.get("/api/search/indexer-status/test-indexer")
    assert response.status_code == 200

@pytest.mark.asyncio
async def test_get_index_stats():
    async with Async<PERSON>lient(app=app, base_url="http://test") as ac:
        response = await ac.get("/api/search/index-stats/test-index")
    assert response.status_code == 200