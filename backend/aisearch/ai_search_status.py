# backend/aisearch/ai_search_status.py

from fastapi import FastAPI, HTTPException, Body, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
import httpx
from pydantic import BaseModel
from typing import Dict, Any
import os
import logging
import asyncio
from collections import defaultdict
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ReindexRequest(BaseModel):
    """Request model for reindexing a file"""
    filename: str

class IndexerNotification:
    """Model for indexer notifications"""
    def __init__(self, file_name: str, status: str):
        self.file_name = file_name
        self.status = status
        self.timestamp = asyncio.get_event_loop().time()

    def to_json(self):
        return {
            "file_name": self.file_name,
            "status": self.status,
            "timestamp": self.timestamp
        }

class AzureAISearchService:
    """Azure AI Search service with simplified on-demand indexing"""

    def __init__(self, search_service_name=None, search_api_key=None):
        self.app = FastAPI()
        self.http_client = httpx.AsyncClient()
        self.shutdown_event = asyncio.Event()
        self.active_connections = []
        self.indexing_tasks = {}

        # Configuration from environment or parameter
        self.SEARCH_SERVICE_NAME = search_service_name or os.environ.get("AZURE_SEARCH_SERVICE", "ai-scope-ai-search")
        self.SEARCH_API_KEY = search_api_key or os.environ.get("AZURE_SEARCH_KEY", "****************************************************")
        self.API_VERSION = os.environ.get("AZURE_SEARCH_API_VERSION", "2021-04-30-Preview")
        self.BASE_URL = f"https://{self.SEARCH_SERVICE_NAME}.search.windows.net"
        self.DEFAULT_INDEXER_NAME = os.environ.get("AZURE_SEARCH_INDEXER", "ai-scope-app1-indexer")
        self.DEFAULT_INDEX_NAME = os.environ.get("AZURE_SEARCH_INDEX", "ai-scope-app1")

        # Configure CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=os.environ.get("ALLOWED_ORIGINS", "*").split(","),
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

        # Register endpoints
        self._register_routes()

    async def _make_search_request(self, index_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Helper to make requests to the Azure Search API."""
        url = f"{self.BASE_URL}/indexes/{index_name}/docs"
        headers = {"Content-Type": "application/json", "api-key": self.SEARCH_API_KEY}
        params["api-version"] = self.API_VERSION

        # Log request details but mask the API key
        masked_headers = {k: (v[:5] + "..." if k.lower() == "api-key" and v else v) for k, v in headers.items()}
        logger.info(f"HTTP Request: GET {url} with params: {params}")
        logger.info(f"Using headers: {masked_headers}")

        try:
            # Check if we have a valid API key before making the request
            if not self.SEARCH_API_KEY or len(self.SEARCH_API_KEY.strip()) < 10:
                logger.error(f"Invalid or missing API key for search service {self.SEARCH_SERVICE_NAME}")
                raise HTTPException(
                    status_code=401,
                    detail=f"Invalid or missing API key for search service {self.SEARCH_SERVICE_NAME}"
                )

            response = await self.http_client.get(url, params=params, headers=headers)
            logger.info(f"HTTP Request: GET {url} {response.status_code} {response.reason_phrase}")

            # Handle common error codes with more specific messages
            if response.status_code == 404:
                logger.error(f"Index {index_name} not found on search service {self.SEARCH_SERVICE_NAME}")
                raise HTTPException(
                    status_code=404,
                    detail=f"Index {index_name} not found on search service {self.SEARCH_SERVICE_NAME}"
                )
            elif response.status_code == 401 or response.status_code == 403:
                logger.error(f"Authentication error accessing index {index_name}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Authentication error accessing index {index_name}. Check your API key."
                )

            # Raise for other status codes
            response.raise_for_status()

            # Try to parse the JSON response with better error handling
            try:
                return response.json()
            except json.JSONDecodeError as json_err:
                logger.error(f"JSON parsing error: {json_err}. Response content: {response.text[:200]}...")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to parse search response as JSON: {str(json_err)}"
                )

        except httpx.HTTPStatusError as e:
            logger.error(f"Search API request failed: {e.response.status_code} - {e.response.text}")
            raise HTTPException(status_code=e.response.status_code, detail=f"Search API error: {e.response.text}")
        except HTTPException:
            # Re-raise HTTPExceptions as they already have status codes and details
            raise
        except Exception as e:
            logger.error(f"Error during search request: {type(e).__name__}: {e}")
            raise HTTPException(status_code=500, detail=f"Internal error during search request: {str(e)}")

    async def _list_all_files(self, index_name: str) -> Dict[str, Any]:
        """Lists all document keys (assumed to be filenames or derivable) from an index."""
        try:
            # Log the search service and index being used
            logger.info(f"Listing files from index {index_name} on search service {self.SEARCH_SERVICE_NAME}")

            # Don't specify a select field to avoid field name issues
            # Just get all fields and we'll extract the filename from whatever is available
            params = {"search": "*", "$top": 1000} # Adjust $top if needed

            try:
                data = await self._make_search_request(index_name, params)
            except HTTPException as http_ex:
                # Handle common HTTP errors with more specific messages
                if http_ex.status_code == 404:
                    logger.error(f"Index {index_name} not found on search service {self.SEARCH_SERVICE_NAME}")
                    return {"files": [], "count": 0, "error": f"Index {index_name} not found"}
                elif http_ex.status_code == 401 or http_ex.status_code == 403:
                    logger.error(f"Authentication error accessing index {index_name}")
                    return {"files": [], "count": 0, "error": "Authentication error accessing search index"}
                else:
                    # Re-raise other HTTP exceptions
                    raise

            # Validate the response structure
            if not isinstance(data, dict):
                logger.error(f"Unexpected response type from search API: {type(data)}")
                return {"files": [], "count": 0, "error": "Invalid response format from search service"}

            # Extract the value list
            value_list = data.get("value", [])
            if not isinstance(value_list, list):
                logger.error(f"'value' field is not a list in search response: {type(value_list)}")
                return {"files": [], "count": 0, "error": "Invalid 'value' field in search response"}

            # Extract document keys from metadata_storage_name or filename field if available
            # Otherwise, try to use the first field that looks like a filename
            # Use a set to store unique filenames
            unique_files = set()
            total_docs = len(value_list)

            logger.info(f"Processing {total_docs} documents from index {index_name} to extract unique titles")

            for doc in value_list:
                # Try different possible field names for the filename
                filename = None
                # Check title field first, then other fields
                for field_name in ["title", "metadata_storage_name", "filename", "name", "id", "filepath"]:
                    if field_name in doc and doc[field_name]:
                        filename = doc[field_name]
                        # If it's a path, extract just the filename
                        if "/" in filename:
                            filename = filename.split("/")[-1]
                        break

                # If we found a filename, add it to our set of unique files
                if filename:
                    unique_files.add(filename)
                else:
                    # Last resort: log the document and try to find any field that might be a filename
                    logger.debug(f"Document without clear filename field: {doc}")
                    for key, value in doc.items():
                        if isinstance(value, str) and ("." in value or len(value) > 5):
                            unique_files.add(value)
                            logger.debug(f"Using field '{key}' with value '{value}' as filename")
                            break

            # Convert set to list for JSON serialization
            files_list = list(unique_files)

            logger.info(f"Successfully extracted {len(files_list)} unique document titles from {total_docs} total documents in index {index_name}")

            # Return both the unique files and the total document count for debugging
            return {
                "files": files_list,
                "count": len(files_list),
                "total_docs": total_docs
            }

        except HTTPException:
            # Let HTTPExceptions propagate as they already have status codes and details
            raise
        except Exception as e:
            logger.error(f"Unexpected error in _list_all_files: {type(e).__name__}: {e}")
            # Return a more user-friendly error response instead of raising an exception
            return {
                "files": [],
                "count": 0,
                "error": f"Failed to list files from index: {str(e)}"
            }

    async def _check_single_file(self, index_name: str, filename: str) -> Dict[str, Any]:
        """Checks if a single file exists in the index by its key/filename."""
        # Don't specify a select field to avoid field name issues
        # Using search to find the filename
        params = {"search": f'"{filename}"', "$top": 10}

        try:
            data = await self._make_search_request(index_name, params)

            # Check if any document contains the filename in any field
            file_found = False
            for doc in data.get("value", []):
                for field_name, field_value in doc.items():
                    if isinstance(field_value, str) and filename in field_value:
                        file_found = True
                        break
                if file_found:
                    break

            return {"filename": filename, "exists": file_found}
        except Exception as e:
            logger.error(f"Error checking file existence: {e}")
            return {"filename": filename, "exists": False, "error": str(e)}

    def _register_routes(self):
        @self.app.get("/api/search/list-indexed-files/{index_name}")
        async def list_indexed_files(index_name: str, check_file: str = None, projectId: str = None):
            # Log the projectId parameter if provided
            if projectId:
                logger.info(f"Request for index {index_name} with projectId: {projectId}")

            # Proceed with the request regardless of projectId
            # The projectId parameter is just for logging and doesn't affect functionality
            if check_file:
                return await self._check_single_file(index_name, check_file)
            else:
                return await self._list_all_files(index_name)

        @self.app.post("/api/search/reindex-file/{indexer_name}")
        async def reindex_file(indexer_name: str, request: ReindexRequest = Body(...), projectId: str = None):
            # Log the projectId parameter if provided
            if projectId:
                logger.info(f"Reindex request for indexer {indexer_name} with projectId: {projectId}")

            result = await self._reindex_file(indexer_name, request)
            # No need to monitor the indexer for now
            return result

        @self.app.get("/api/search/config")
        async def get_search_config():
            return await self._get_search_config()

        @self.app.websocket("/api/search/ws")
        async def websocket_endpoint(websocket: WebSocket):
            await self._handle_websocket_connection(websocket)

    async def _handle_websocket_connection(self, websocket: WebSocket):
        logger.info("WebSocket connection attempt received")
        try:
            await websocket.accept()
            self.active_connections.append(websocket)
            logger.info(f"Active WebSocket connections: {len(self.active_connections)}")

            await websocket.send_text(json.dumps({
                "type": "connection_established",
                "message": "WebSocket connection established successfully"
            }))

            while not self.shutdown_event.is_set():
                try:
                    data = await asyncio.wait_for(websocket.receive_text(), timeout=1.0)
                    if data == "ping":
                        await websocket.send_text("pong")
                except asyncio.TimeoutError:
                    continue
        except (WebSocketDisconnect, Exception) as e:
            logger.error(f"WebSocket error: {e}")
            if websocket in self.active_connections:
                self.active_connections.remove(websocket)

    async def close(self):
        """Clean up all resources"""
        self.shutdown_event.set()
        await self.http_client.aclose()
        # Cancel monitoring tasks if any are running (assuming self.indexing_tasks holds them)
        for task in list(self.indexing_tasks.values()): # Iterate over a copy
             if task and not task.done():
                 task.cancel()
        # Close active WebSocket connections
        for connection in list(self.active_connections): # Iterate over a copy
             try:
                 await connection.close(code=1001)
             except Exception as e:
                 logger.error(f"Error closing WebSocket connection {id(connection)}: {e}")
        self.active_connections.clear()
        self.indexing_tasks.clear()
        logger.info("AzureAISearchService resources cleaned up.")

    # Removed _list_indexed_files as logic moved into the route handler directly

    async def _reindex_file(self, indexer_name: str, request: ReindexRequest):
        """Trigger reindexing of a specific file"""
        try:
            data = {
                "parameters": {
                    "resetCache": True,
                    "dataSourceFilter": {"name": request.filename}
                }
            }

            url = f"{self.BASE_URL}/indexers/{indexer_name}/run"
            logger.info(f"Reindexing file {request.filename} using indexer {indexer_name} on service {self.SEARCH_SERVICE_NAME}")
            logger.info(f"HTTP Request: POST {url}")

            response = await self.http_client.post(
                url,
                headers={"Content-Type": "application/json", "api-key": self.SEARCH_API_KEY},
                params={"api-version": self.API_VERSION},
                json=data
            )

            logger.info(f"HTTP Request: POST {url} {response.status_code} {response.reason_phrase}")

            if response.status_code == 202:
                return {"message": f"Reindexing of {request.filename} started successfully"}
            raise HTTPException(status_code=response.status_code, detail=response.text)

        except Exception as e:
            logger.error(f"Error reindexing file: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def _get_search_config(self):
        """Return the current search service configuration"""
        logger.info(f"Returning search config for service: {self.SEARCH_SERVICE_NAME}")
        return {
            "searchServiceName": self.SEARCH_SERVICE_NAME,
            "indexName": self.DEFAULT_INDEX_NAME,
            "indexerName": self.DEFAULT_INDEXER_NAME,
            "apiVersion": self.API_VERSION,
            "baseUrl": self.BASE_URL
        }

    def get_app(self):
        return self.app

if __name__ == "__main__":
    import uvicorn
    search_service = AzureAISearchService()
    uvicorn.run(search_service.get_app(), host="0.0.0.0", port=8000)
