"""
Centralized logging configuration for the application.
This module provides functions to set up logging with different handlers and levels.
"""

import os
import logging
import sys
from logging.handlers import RotatingFileHand<PERSON>

def ensure_logs_directory():
    """Create the logs directory in the root if it doesn't exist"""
    # Get the root directory (3 levels up from this file)
    root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    logs_dir = os.path.join(root_dir, "logs")
    # Create the logs directory if it doesn't exist
    os.makedirs(logs_dir, exist_ok=True)
    return logs_dir

def configure_logger(logger_name, log_to_console=True, log_to_file=True, 
                    console_level=logging.INFO, file_level=logging.DEBUG,
                    log_file_name=None, max_file_size_mb=10, backup_count=5):
    """
    Configure a logger with console and/or file handlers.
    
    Args:
        logger_name: Name of the logger to configure
        log_to_console: Whether to log to console
        log_to_file: Whether to log to file
        console_level: Logging level for console handler
        file_level: Logging level for file handler
        log_file_name: Name of the log file (if None, uses logger_name.log)
        max_file_size_mb: Maximum size of log file in MB before rotation
        backup_count: Number of backup log files to keep
        
    Returns:
        The configured logger
    """
    logger = logging.getLogger(logger_name)
    logger.setLevel(min(console_level, file_level))  # Set to the most verbose level
    
    # Remove existing handlers to avoid duplicates
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    # Add console handler if requested
    if log_to_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(console_level)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # Add file handler if requested
    if log_to_file:
        logs_dir = ensure_logs_directory()
        if log_file_name is None:
            log_file_name = f"{logger_name}.log"
        log_file_path = os.path.join(logs_dir, log_file_name)
        
        # Use rotating file handler to prevent log files from growing too large
        file_handler = RotatingFileHandler(
            log_file_path, 
            maxBytes=max_file_size_mb * 1024 * 1024,  # Convert MB to bytes
            backupCount=backup_count
        )
        file_handler.setLevel(file_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger

def configure_deployment_logger(project_id, console_level=logging.INFO, file_level=logging.DEBUG):
    """
    Configure a logger specifically for deployment tracking.
    
    Args:
        project_id: ID of the project being deployed
        console_level: Logging level for console output
        file_level: Logging level for file output
        
    Returns:
        The configured logger
    """
    logger_name = f"deployment_{project_id}"
    log_file_name = f"deployment_{project_id}.log"
    
    return configure_logger(
        logger_name=logger_name,
        log_to_console=True,
        log_to_file=True,
        console_level=console_level,
        file_level=file_level,
        log_file_name=log_file_name
    )

def configure_project_retrieval_logger(console_level=logging.INFO, file_level=logging.DEBUG):
    """
    Configure a logger specifically for project retrieval operations.
    
    Args:
        console_level: Logging level for console output
        file_level: Logging level for file output
        
    Returns:
        The configured logger
    """
    return configure_logger(
        logger_name="project_retrieval",
        log_to_console=True,
        log_to_file=True,
        console_level=console_level,
        file_level=file_level,
        log_file_name="project_retrieval.log"
    )

def configure_cosmos_logger(console_level=logging.WARNING, file_level=logging.DEBUG):
    """
    Configure a logger specifically for CosmosDB operations.
    
    Args:
        console_level: Logging level for console output
        file_level: Logging level for file output
        
    Returns:
        The configured logger
    """
    return configure_logger(
        logger_name="cosmos_db",
        log_to_console=True,
        log_to_file=True,
        console_level=console_level,
        file_level=file_level,
        log_file_name="cosmos_db.log"
    )

def configure_api_logger(console_level=logging.INFO, file_level=logging.DEBUG):
    """
    Configure a logger specifically for API requests and responses.
    
    Args:
        console_level: Logging level for console output
        file_level: Logging level for file output
        
    Returns:
        The configured logger
    """
    return configure_logger(
        logger_name="api",
        log_to_console=True,
        log_to_file=True,
        console_level=console_level,
        file_level=file_level,
        log_file_name="api.log"
    )
