"""
Utility functions for standardized project document retrieval with fallbacks.
This module provides a consistent way to retrieve project documents from the database
with multiple fallback mechanisms to handle various edge cases.
"""

import logging
from typing import Dict, Any, Optional, List
import importlib

logger = logging.getLogger(__name__)

# Import RBAC client dynamically to avoid circular imports
def get_rbac_client():
    """Get the RBAC client from the rbac_routes module."""
    try:
        rbac_routes = importlib.import_module('backend.rbac.rbac_routes')
        return rbac_routes.rbac_client
    except (ImportError, AttributeError) as e:
        logger.warning(f"Could not import RBAC client: {e}")
        return None

async def get_rbac_project(rbac_client, project_id, user_id=None):
    """
    Get a project from the RBAC client with fallback to query if region is unknown.

    Args:
        rbac_client: The RBAC client instance
        project_id: The ID of the project to retrieve
        user_id: Optional user ID (not used for direct retrieval but kept for compatibility)

    Returns:
        The project document if found, None otherwise
    """
    if not rbac_client:
        return None

    try:
        # First try to find the project's region using a query
        query = "SELECT * FROM c WHERE c.id = @projectId AND c.type = 'project'"
        parameters = [{"name": "@projectId", "value": project_id}]

        projects = []
        async for item in rbac_client.projects_container.query_items(
            query=query,
            parameters=parameters
        ):
            projects.append(item)

        if projects:
            project = projects[0]
            region = project.get('region')

            # Now get the full project using the region as partition key
            return await rbac_client.get_project(project_id, region)

        return None
    except Exception as e:
        logger.error(f"Error getting project {project_id} from RBAC client: {e}")
        return None

async def get_project_with_fallbacks(
    cosmos_client,
    project_id: str,
    user_id: str,
    using_azure_cli: bool = False
) -> Optional[Dict[str, Any]]:
    """
    Retrieve a project document with multiple fallback mechanisms.

    Args:
        cosmos_client: The CosmosDB client instance
        project_id: The ID of the project to retrieve
        user_id: The ID of the user making the request
        using_azure_cli: Whether the request is using Azure CLI credentials

    Returns:
        The project document if found, None otherwise
    """
    if not cosmos_client:
        logger.error("CosmosDB client not available for project retrieval")
        return None

    # Step 1: Try to get the project with the provided user_id
    try:
        logger.info(f"Attempting to get project {project_id} for user {user_id}")
        project_doc = await cosmos_client.get_project(user_id, project_id)
        if project_doc:
            logger.info(f"Found project {project_id} for user {user_id}")
            return project_doc
    except Exception as e:
        logger.error(f"Error getting project {project_id} for user {user_id}: {e}")

    # If not using Azure CLI credentials, we're done - no fallbacks
    if not using_azure_cli:
        logger.warning(f"Project {project_id} not found for user {user_id} and not using Azure CLI credentials")
        return None

    # Step 2: Try to use get_project_by_id method if it exists
    try:
        logger.info(f"Trying to get project {project_id} using get_project_by_id method")
        if hasattr(cosmos_client, 'get_project_by_id'):
            project_doc = await cosmos_client.get_project_by_id(project_id)
            if project_doc:
                logger.info(f"Found project {project_id} using get_project_by_id method")
                return project_doc
    except Exception as e:
        logger.error(f"Error getting project {project_id} using get_project_by_id method: {e}")

    # Step 3: Try with 'anonymous' user
    try:
        logger.info(f"Trying to get project {project_id} for 'anonymous' user")
        project_doc = await cosmos_client.get_project('anonymous', project_id)
        if project_doc:
            logger.info(f"Found project {project_id} for 'anonymous' user")
            return project_doc
    except Exception as e:
        logger.error(f"Error getting project {project_id} for 'anonymous' user: {e}")

    # Step 4: Try to get all projects for the user and filter by ID
    try:
        logger.info(f"Trying to get all projects for user {user_id} and filter by ID {project_id}")
        all_projects = await cosmos_client.get_projects(user_id)
        project_doc = next((p for p in all_projects if p.get('id') == project_id), None)
        if project_doc:
            logger.info(f"Found project {project_id} by filtering all projects for user {user_id}")
            return project_doc
    except Exception as e:
        logger.error(f"Error getting all projects for user {user_id}: {e}")

    # Step 5: Try to get all projects for 'anonymous' user and filter by ID
    try:
        logger.info(f"Trying to get all projects for 'anonymous' user and filter by ID {project_id}")
        all_projects = await cosmos_client.get_projects('anonymous')
        project_doc = next((p for p in all_projects if p.get('id') == project_id), None)
        if project_doc:
            logger.info(f"Found project {project_id} by filtering all projects for 'anonymous' user")
            return project_doc
    except Exception as e:
        logger.error(f"Error getting all projects for 'anonymous' user: {e}")

    # Step 6: Last resort - direct query to find the project by ID regardless of user
    try:
        logger.info(f"Trying direct query to find project {project_id} regardless of user")
        parameters = [{'name': '@projectId', 'value': project_id}]
        query = "SELECT * FROM c WHERE c.id = @projectId AND c.type = 'project'"
        projects = []

        # Use the conversations_project_container if available
        container = cosmos_client.conversations_project_container if hasattr(cosmos_client, 'conversations_project_container') else cosmos_client.container_client

        async for item in container.query_items(
            query=query,
            parameters=parameters
        ):
            projects.append(item)

        if projects:
            project_doc = projects[0]
            logger.info(f"Found project {project_id} using direct query")
            return project_doc
    except Exception as e:
        logger.error(f"Error executing direct query for project {project_id}: {e}")

    # Step 7: Try to get the project from the RBAC client
    try:
        logger.info(f"Trying to get project {project_id} from RBAC client")
        rbac_client = get_rbac_client()
        if rbac_client:
            # Use our wrapper function to get the project
            project_doc = await get_rbac_project(rbac_client, project_id, user_id)

            if project_doc:
                logger.info(f"Found project {project_id} in RBAC client")

                # Convert RBAC project to conversation project format
                conversation_project = {
                    'id': project_doc.get('id'),
                    'type': 'project',
                    'userId': project_doc.get('owner', user_id),
                    'name': project_doc.get('name'),
                    'description': project_doc.get('description', ''),
                    'storage_container_uploads': project_doc.get('storage_container_uploads'),
                    'storage_container_input': project_doc.get('storage_container_input'),
                    'storage_container_output': project_doc.get('storage_container_output'),
                    'search_index_name': project_doc.get('search_index_name'),
                    'search_datasource_name': project_doc.get('search_datasource_name'),
                    'createdAt': project_doc.get('created_at')
                }

                # Try to save the project to the conversation client for future use
                try:
                    # Use the conversations_project_container if available
                    if hasattr(cosmos_client, 'conversations_project_container'):
                        await cosmos_client.conversations_project_container.create_item(conversation_project)
                    else:
                        await cosmos_client.container_client.create_item(conversation_project)
                    logger.info(f"Saved RBAC project {project_id} to conversation container for future use")
                except Exception as save_error:
                    logger.warning(f"Could not save RBAC project to conversation container: {save_error}")

                return conversation_project
    except Exception as e:
        logger.error(f"Error getting project {project_id} from RBAC client: {e}")

    # If we get here, we couldn't find the project
    logger.warning(f"Project {project_id} not found after trying all fallback mechanisms")
    return None

async def get_projects_for_user(
    cosmos_client,
    user_id: str
) -> List[Dict[str, Any]]:
    """
    Retrieve all projects for a user.

    Args:
        cosmos_client: The CosmosDB client instance
        user_id: The ID of the user making the request

    Returns:
        A list of project documents
    """
    if not cosmos_client:
        logger.error("CosmosDB client not available for project retrieval")
        return []

    projects = []

    # Step 1: Try to get projects from the conversation client
    try:
        logger.info(f"Getting projects for user {user_id} from conversation client")
        conversation_projects = await cosmos_client.get_projects(user_id)

        # Ensure we always have a list
        if not isinstance(conversation_projects, list):
            logger.warning(f"get_projects returned non-list value: {type(conversation_projects)}, using empty list")
            conversation_projects = []

        logger.info(f"Found {len(conversation_projects)} projects for user {user_id} in conversation client")
        projects.extend(conversation_projects)
    except Exception as e:
        logger.error(f"Error getting projects for user {user_id} from conversation client: {e}")

    # Step 2: Try to get projects from the RBAC client
    try:
        logger.info(f"Getting projects for user {user_id} from RBAC client")
        rbac_client = get_rbac_client()
        if rbac_client:
            # Get all projects from RBAC client
            rbac_projects = await rbac_client.get_accessible_projects(user_id)

            if rbac_projects:
                logger.info(f"Found {len(rbac_projects)} projects for user {user_id} in RBAC client")

                # Convert RBAC projects to conversation project format
                for rbac_project in rbac_projects:
                    # Check if this project is already in the list (from conversation client)
                    if not any(p.get('id') == rbac_project.get('id') for p in projects):
                        conversation_project = {
                            'id': rbac_project.get('id'),
                            'type': 'project',
                            'userId': rbac_project.get('owner', user_id),
                            'name': rbac_project.get('name'),
                            'description': rbac_project.get('description', ''),
                            'storage_container_uploads': rbac_project.get('storage_container_uploads'),
                            'storage_container_input': rbac_project.get('storage_container_input'),
                            'storage_container_output': rbac_project.get('storage_container_output'),
                            'search_index_name': rbac_project.get('search_index_name'),
                            'search_datasource_name': rbac_project.get('search_datasource_name'),
                            'createdAt': rbac_project.get('created_at')
                        }
                        projects.append(conversation_project)

                        # Try to save the project to the conversation client for future use
                        try:
                            # Use the conversations_project_container if available
                            if hasattr(cosmos_client, 'conversations_project_container'):
                                await cosmos_client.conversations_project_container.create_item(conversation_project)
                            else:
                                await cosmos_client.container_client.create_item(conversation_project)
                            logger.info(f"Saved RBAC project {rbac_project.get('id')} to conversation container for future use")
                        except Exception as save_error:
                            logger.warning(f"Could not save RBAC project to conversation container: {save_error}")
    except Exception as e:
        logger.error(f"Error getting projects for user {user_id} from RBAC client: {e}")

    logger.info(f"Returning {len(projects)} total projects for user {user_id}")
    return projects
