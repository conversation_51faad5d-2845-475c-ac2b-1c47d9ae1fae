@description('Azure AI Search service name')
param searchServiceName string

@description('Location for the search service')
param location string = resourceGroup().location

@description('Search index name')
param searchIndexName string

@description('Search indexer name')
param searchIndexerName string

@description('Search datasource name')
param searchDatasourceName string

// Removed unused parameter: storageAccountName

@description('Storage connection string')
@secure()
param storageConnectionString string

@description('Uploads container name')
param uploadsContainerName string

@description('Resource tags')
param tags object = {}

// Azure AI Search Service
resource searchService 'Microsoft.Search/searchServices@2022-09-01' = {
  name: searchServiceName
  location: location
  tags: tags
  sku: {
    name: 'basic'
  }
  properties: {
    replicaCount: 1
    partitionCount: 1
    hostingMode: 'default'
  }
}

// Note: The actual index, indexer, and datasource creation requires REST API calls
// Bicep doesn't directly support these resources, so we'll use a deployment script
// to create them after the search service is deployed

// Deployment script to create index, indexer, and datasource
resource searchResourcesDeploymentScript 'Microsoft.Resources/deploymentScripts@2020-10-01' = {
  name: 'search-resources-deployment-script-${uniqueString(searchService.id)}'
  location: location
  tags: tags
  kind: 'AzureCLI'
  properties: {
    azCliVersion: '2.40.0'
    timeout: 'PT30M'
    retentionInterval: 'P1D'
    forceUpdateTag: uniqueString(deployment().name) // Force the script to run every time
    environmentVariables: [
      {
        name: 'SEARCH_SERVICE_NAME'
        value: searchService.name
      }
      {
        name: 'SEARCH_API_KEY'
        value: searchService.listAdminKeys().primaryKey
      }
      {
        name: 'SEARCH_INDEX_NAME'
        value: searchIndexName
      }
      {
        name: 'SEARCH_INDEXER_NAME'
        value: searchIndexerName
      }
      {
        name: 'SEARCH_DATASOURCE_NAME'
        value: searchDatasourceName
      }
      {
        name: 'STORAGE_CONNECTION_STRING'
        secureValue: storageConnectionString
      }
      {
        name: 'STORAGE_CONTAINER_NAME'
        value: uploadsContainerName
      }
    ]
    scriptContent: '''
      #!/bin/bash
      set -e

      echo "Starting search resources deployment script"
      echo "Search Service Name: $SEARCH_SERVICE_NAME"
      echo "Search Index Name: $SEARCH_INDEX_NAME"
      echo "Search Indexer Name: $SEARCH_INDEXER_NAME"
      echo "Search Datasource Name: $SEARCH_DATASOURCE_NAME"
      echo "Storage Container Name: $STORAGE_CONTAINER_NAME"

      # Verify search service is available
      echo "Verifying search service is available..."
      az rest --method get \
        --uri "https://$SEARCH_SERVICE_NAME.search.windows.net/indexes?api-version=2020-06-30" \
        --headers "api-key=$SEARCH_API_KEY" || {
          echo "Error: Cannot connect to search service $SEARCH_SERVICE_NAME"
          exit 1
        }

      # Create the datasource
      echo "Creating datasource: $SEARCH_DATASOURCE_NAME"
      DATASOURCE_RESULT=$(az rest --method put \
        --uri "https://$SEARCH_SERVICE_NAME.search.windows.net/datasources/$SEARCH_DATASOURCE_NAME?api-version=2020-06-30" \
        --headers "Content-Type=application/json" "api-key=$SEARCH_API_KEY" \
        --body "{
          \"name\": \"$SEARCH_DATASOURCE_NAME\",
          \"type\": \"azureblob\",
          \"credentials\": {
            \"connectionString\": \"$STORAGE_CONNECTION_STRING\"
          },
          \"container\": {
            \"name\": \"$STORAGE_CONTAINER_NAME\"
          }
        }")

      echo "Datasource creation result: $DATASOURCE_RESULT"

      # Create the index
      echo "Creating index: $SEARCH_INDEX_NAME"
      INDEX_RESULT=$(az rest --method put \
        --uri "https://$SEARCH_SERVICE_NAME.search.windows.net/indexes/$SEARCH_INDEX_NAME?api-version=2020-06-30" \
        --headers "Content-Type=application/json" "api-key=$SEARCH_API_KEY" \
        --body "{
          \"name\": \"$SEARCH_INDEX_NAME\",
          \"fields\": [
            {
              \"name\": \"id\",
              \"type\": \"Edm.String\",
              \"key\": true,
              \"searchable\": true,
              \"filterable\": true,
              \"sortable\": true,
              \"facetable\": false
            },
            {
              \"name\": \"content\",
              \"type\": \"Edm.String\",
              \"searchable\": true,
              \"filterable\": false,
              \"sortable\": false,
              \"facetable\": false
            },
            {
              \"name\": \"metadata_storage_name\",
              \"type\": \"Edm.String\",
              \"searchable\": true,
              \"filterable\": true,
              \"sortable\": true,
              \"facetable\": true
            },
            {
              \"name\": \"metadata_storage_path\",
              \"type\": \"Edm.String\",
              \"searchable\": false,
              \"filterable\": true,
              \"sortable\": true,
              \"facetable\": false
            },
            {
              \"name\": \"metadata_content_type\",
              \"type\": \"Edm.String\",
              \"searchable\": true,
              \"filterable\": true,
              \"sortable\": true,
              \"facetable\": true
            }
          ]
        }")

      echo "Index creation result: $INDEX_RESULT"

      # Create the indexer
      echo "Creating indexer: $SEARCH_INDEXER_NAME"
      INDEXER_RESULT=$(az rest --method put \
        --uri "https://$SEARCH_SERVICE_NAME.search.windows.net/indexers/$SEARCH_INDEXER_NAME?api-version=2020-06-30" \
        --headers "Content-Type=application/json" "api-key=$SEARCH_API_KEY" \
        --body "{
          \"name\": \"$SEARCH_INDEXER_NAME\",
          \"dataSourceName\": \"$SEARCH_DATASOURCE_NAME\",
          \"targetIndexName\": \"$SEARCH_INDEX_NAME\",
          \"parameters\": {
            \"configuration\": {
              \"parsingMode\": \"default\",
              \"indexStorageMetadataOnlyForOversizedDocuments\": true
            }
          },
          \"schedule\": {
            \"interval\": \"PT5M\"
          }
        }")

      echo "Indexer creation result: $INDEXER_RESULT"

      # Verify resources were created
      echo "Verifying resources were created..."

      # Check datasource
      DATASOURCE_CHECK=$(az rest --method get \
        --uri "https://$SEARCH_SERVICE_NAME.search.windows.net/datasources/$SEARCH_DATASOURCE_NAME?api-version=2020-06-30" \
        --headers "api-key=$SEARCH_API_KEY")

      if [[ "$DATASOURCE_CHECK" == *"$SEARCH_DATASOURCE_NAME"* ]]; then
        echo "✅ Datasource $SEARCH_DATASOURCE_NAME created successfully"
      else
        echo "❌ Failed to create datasource $SEARCH_DATASOURCE_NAME"
        echo "$DATASOURCE_CHECK"
      fi

      # Check index
      INDEX_CHECK=$(az rest --method get \
        --uri "https://$SEARCH_SERVICE_NAME.search.windows.net/indexes/$SEARCH_INDEX_NAME?api-version=2020-06-30" \
        --headers "api-key=$SEARCH_API_KEY")

      if [[ "$INDEX_CHECK" == *"$SEARCH_INDEX_NAME"* ]]; then
        echo "✅ Index $SEARCH_INDEX_NAME created successfully"
      else
        echo "❌ Failed to create index $SEARCH_INDEX_NAME"
        echo "$INDEX_CHECK"
      fi

      # Check indexer
      INDEXER_CHECK=$(az rest --method get \
        --uri "https://$SEARCH_SERVICE_NAME.search.windows.net/indexers/$SEARCH_INDEXER_NAME?api-version=2020-06-30" \
        --headers "api-key=$SEARCH_API_KEY")

      if [[ "$INDEXER_CHECK" == *"$SEARCH_INDEXER_NAME"* ]]; then
        echo "✅ Indexer $SEARCH_INDEXER_NAME created successfully"
      else
        echo "❌ Failed to create indexer $SEARCH_INDEXER_NAME"
        echo "$INDEXER_CHECK"
      fi

      echo "Search resources deployment completed"
    '''
  }
}

// Outputs
output searchServiceName string = searchService.name
output searchServiceId string = searchService.id
output searchApiKey string = searchService.listAdminKeys().primaryKey
output searchIndexName string = searchIndexName
output searchIndexerName string = searchIndexerName
output searchDatasourceName string = searchDatasourceName
