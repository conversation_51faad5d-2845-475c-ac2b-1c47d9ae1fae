#!/bin/bash

# Project details from the deployment summary
PROJECT_ID="6683bca1-4bc6-4b78-a6b4-28e009478c70"
REGION_ID="25aed46f-1196-8502-3d250-251252253254255256"

# Make the script executable
chmod +x test_rbac_update_project.py

# Run the test script
echo "Running RBAC update test for project $PROJECT_ID in region $REGION_ID"
python test_rbac_update_project.py "$PROJECT_ID" "$REGION_ID"

# Check the result
if [ $? -eq 0 ]; then
    echo "Test completed successfully!"
else
    echo "Test failed!"
    exit 1
fi
