azure-identity==1.15.0
azure-mgmt-storage>=21.0.0 # Added for StorageManagementClient
azure-mgmt-web>=6.0.0 # Added for WebManagementClient
azure-mgmt-eventgrid>=10.2.0 # Added for EventGridManagementClient
azure-mgmt-costmanagement>=4.0.0 # Added for CostManagementClient
quart==0.20.0   # Example newer version compatible with Flask 3.x
Flask>=3.0,<4.0
openai==1.6.1
azure-search-documents==11.4.0b6
azure-mgmt-search==9.2.0b3  # Required for SearchManagementClient
azure-storage-blob==12.17.0
python-dotenv==1.0.0
azure-cosmos==4.5.1
uvicorn==0.24.0
aiohttp==3.9.2
gunicorn==20.1.0
pydantic-settings==2.2.1
httpx==0.25.2
fastapi
pydantic>=2.0.0
websocket
starlette>=0.27.0  # TestClient is included in starlette directly
hypercorn>=0.14.4  # Better WebSocket support for Quart
websockets>=11.0.3  # Improved WebSocket client/server
pandas>=2.0.0  # For PriorityPlot Excel processing
openpyxl>=3.1.0  # For Excel file support in pandas
flask-cors>=4.0.0  # For CORS support in Flask
msal>=1.20.0  # Microsoft Authentication Library for Python
quart-session>=0.8.0 # For server-side session management in Quart

