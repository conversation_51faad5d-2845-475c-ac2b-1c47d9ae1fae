#!/usr/bin/env python3
"""
Test script to validate the authentication flow using a token.
This script allows you to manually enter a token and test the API endpoints.
"""

import os
import sys
import json
import logging
import requests
import argparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def test_api_with_token(token, base_url="http://localhost:50508"):
    """
    Test the API endpoints using the provided token.
    
    Args:
        token: The access token to use for authentication
        base_url: The base URL of the API
    
    Returns:
        bool: True if all tests pass, False otherwise
    """
    if not token:
        logger.error("No token provided")
        return False
    
    logger.info(f"Using token: {token[:10]}...{token[-10:]}")
    
    # Test endpoints
    endpoints = [
        # RBAC endpoints
        {"url": f"{base_url}/api/rbac/regions", "auth_required": True, "name": "Regions"},
        {"url": f"{base_url}/api/rbac/users", "auth_required": True, "name": "Users"},
        {"url": f"{base_url}/api/rbac/projects", "auth_required": True, "name": "Projects"},
        
        # User info endpoint
        {"url": f"{base_url}/api/rbac/me", "auth_required": True, "name": "Current User"},
    ]
    
    all_passed = True
    
    for endpoint in endpoints:
        try:
            logger.info(f"Testing endpoint: {endpoint['name']} ({endpoint['url']})")
            
            headers = {}
            if endpoint["auth_required"]:
                headers["Authorization"] = f"Bearer {token}"
            
            response = requests.get(endpoint["url"], headers=headers)
            
            if response.status_code == 200:
                logger.info(f"Successfully accessed {endpoint['name']}")
                try:
                    response_data = response.json()
                    logger.info(f"Response: {json.dumps(response_data, indent=2)[:200]}...")
                except:
                    logger.info(f"Response: {response.text[:200]}...")
            else:
                logger.error(f"Failed to access {endpoint['name']}: {response.status_code}")
                logger.error(f"Response: {response.text}")
                if endpoint["auth_required"]:
                    all_passed = False
        except Exception as e:
            logger.error(f"Error testing endpoint {endpoint['name']}: {e}")
            if endpoint["auth_required"]:
                all_passed = False
    
    return all_passed

def main():
    """
    Main function to run the test.
    """
    parser = argparse.ArgumentParser(description='Test API endpoints with a token')
    parser.add_argument('--token', help='The access token to use for authentication')
    parser.add_argument('--url', default="http://localhost:50508", help='The base URL of the API')
    
    args = parser.parse_args()
    
    # If token is not provided as an argument, prompt for it
    token = args.token
    if not token:
        token = input("Enter your access token: ")
    
    # Test the API endpoints
    success = test_api_with_token(token, args.url)
    
    if success:
        logger.info("API test completed successfully!")
        return 0
    else:
        logger.error("API test failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
