# Azure Cost Management Scripts

This directory contains scripts for interacting with the Azure Cost Management API to retrieve and analyze cost data for your Azure resources.

## Prerequisites

- Python 3.6 or higher
- Azure subscription
- Appropriate permissions to access cost data (at least Reader role on the subscription)
- Required Python packages:
  - azure-identity
  - azure-mgmt-costmanagement

## Installation

Install the required packages:

```bash
pip install azure-identity azure-mgmt-costmanagement
```

## Authentication

These scripts use the DefaultAzureCredential from the azure-identity package, which tries several authentication methods in the following order:

1. Environment variables (AZURE_CLIENT_ID, AZURE_CLIENT_SECRET, AZURE_TENANT_ID)
2. Managed Identity
3. Azure CLI credentials
4. Azure PowerShell credentials
5. Visual Studio Code credentials

For local development, the easiest way to authenticate is to use the Azure CLI:

```bash
az login
```

## Available Scripts

### 1. azure_cost_management.py

A comprehensive script that provides various options for querying cost data.

#### Usage

```bash
python azure_cost_management.py --subscription-id <SUBSCRIPTION_ID> [options]
```

#### Options

- `--subscription-id TEXT`: Azure Subscription ID [required]
- `--resource-group TEXT`: Filter by resource group name
- `--timeframe TEXT`: Timeframe for cost data: MonthToDate, BillingMonthToDate, TheLastMonth, TheLastBillingMonth, WeekToDate, Custom [default: MonthToDate]
- `--start-date TEXT`: Start date for custom timeframe (format: YYYY-MM-DD)
- `--end-date TEXT`: End date for custom timeframe (format: YYYY-MM-DD)
- `--group-by TEXT`: Group results by dimension (ServiceName, ResourceGroup, ResourceType, etc.). Multiple values can be comma-separated [default: ServiceName]
- `--tag-name TEXT`: Filter by tag name (e.g., Project, Region)
- `--tag-value TEXT`: Filter by tag value (e.g., ProjectA, WestEurope)
- `--output TEXT`: Output format: table, json [default: table]
- `--debug`: Enable debug logging

#### Examples

Get costs grouped by service for the current month:

```bash
python azure_cost_management.py --subscription-id 00000000-0000-0000-0000-000000000000
```

Get costs for a specific project:

```bash
python azure_cost_management.py --subscription-id 00000000-0000-0000-0000-000000000000 --tag-name Project --tag-value ProjectA
```

Get costs grouped by resource group and service name:

```bash
python azure_cost_management.py --subscription-id 00000000-0000-0000-0000-000000000000 --group-by ResourceGroup,ServiceName
```

Get costs for a custom date range:

```bash
python azure_cost_management.py --subscription-id 00000000-0000-0000-0000-000000000000 --timeframe Custom --start-date 2023-01-01 --end-date 2023-01-31
```

Get costs for a specific resource group:

```bash
python azure_cost_management.py --subscription-id 00000000-0000-0000-0000-000000000000 --resource-group rg-internal-ai
```

Get costs for a specific resource group and group by service name:

```bash
python azure_cost_management.py --subscription-id 00000000-0000-0000-0000-000000000000 --resource-group rg-internal-ai --group-by ServiceName
```

### 2. cost_management_example.py

A simpler script that demonstrates how to filter costs by Project and Region tags.

#### Usage

```bash
python cost_management_example.py --subscription-id <SUBSCRIPTION_ID> [options]
```

#### Options

- `--subscription-id TEXT`: Azure Subscription ID [required]
- `--project TEXT`: Project tag value to filter by (e.g., ProjectA)
- `--region TEXT`: Region tag value to filter by (e.g., WestEurope)
- `--timeframe TEXT`: Timeframe for cost data [default: MonthToDate]

#### Examples

Get costs for all services:

```bash
python cost_management_example.py --subscription-id 00000000-0000-0000-0000-000000000000
```

Get costs for a specific project:

```bash
python cost_management_example.py --subscription-id 00000000-0000-0000-0000-000000000000 --project ProjectA
```

Get costs for a specific region:

```bash
python cost_management_example.py --subscription-id 00000000-0000-0000-0000-000000000000 --region WestEurope
```

Get costs for a specific project and region:

```bash
python cost_management_example.py --subscription-id 00000000-0000-0000-0000-000000000000 --project ProjectA --region WestEurope
```

## Integrating with the Application

To integrate these cost management scripts with the application's cost analytics dashboard:

1. Create a backend API endpoint that calls these scripts
2. Update the frontend's `costService.ts` to fetch data from the API instead of using mock data
3. Ensure proper authentication and authorization for accessing cost data

## Troubleshooting

If you encounter authentication issues:

1. Verify that you have the necessary permissions on the Azure subscription
2. Check that you're logged in with the Azure CLI (`az login`)
3. Run the script with the `--debug` flag to see detailed authentication logs

If you don't see any cost data:

1. Verify that your subscription has resources with costs
2. Check that the timeframe is appropriate (some resources might not have costs in the current month)
3. Ensure that the tag names and values match exactly what's in your Azure resources
