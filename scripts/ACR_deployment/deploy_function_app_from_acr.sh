#!/bin/bash
# Don't exit immediately on error - we'll handle errors ourselves
# set -e

# Initialize error tracking
LAST_ERROR_CODE=0
DEPLOYMENT_SUCCESS=false

# Create a log file
LOG_DIR="logs"
mkdir -p $LOG_DIR
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
LOG_FILE="${LOG_DIR}/acr_deployment_${TIMESTAMP}.log"
echo "Starting ACR deployment script at $(date)" > $LOG_FILE

# Function to log messages to both console and log file
log() {
    echo -e "$1" | tee -a $LOG_FILE
}

# Function to log errors
log_error() {
    echo -e "${RED}ERROR: $1${NC}" | tee -a $LOG_FILE
    LAST_ERROR_CODE=1
}

# Function to log warnings
log_warning() {
    echo -e "${YELLOW}WARNING: $1${NC}" | tee -a $LOG_FILE
}

# Function to log success
log_success() {
    echo -e "${GREEN}SUCCESS: $1${NC}" | tee -a $LOG_FILE
}

# Function to handle errors with retry
retry_command() {
    local max_attempts=$1
    local command=$2
    local attempt=1
    local output=""
    local status=0

    log "Executing command with up to $max_attempts attempts: $command"

    while [ $attempt -le $max_attempts ]; do
        log "Attempt $attempt of $max_attempts..."

        # Execute the command and capture output and status
        output=$(eval $command 2>&1)
        status=$?

        if [ $status -eq 0 ]; then
            log_success "Command succeeded on attempt $attempt"
            echo "$output"
            return 0
        else
            log_warning "Command failed on attempt $attempt with status $status"
            log_warning "Output: $output"

            if [ $attempt -lt $max_attempts ]; then
                local wait_time=$((attempt * 5))
                log "Waiting $wait_time seconds before retrying..."
                sleep $wait_time
            fi

            attempt=$((attempt + 1))
        fi
    done

    log_error "Command failed after $max_attempts attempts"
    echo "$output"
    return $status
}

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
# Use environment variables or default values
RESOURCE_GROUP="${RESOURCE_GROUP:-rg-internal-ai}"
LOCATION="${TARGET_LOCATION:-westeurope}"
PARAMETERS_FILE="${PARAMETERS_FILE:-scripts/ACR_deployment/parameters.json}"

# Print header
echo -e "${BLUE}============================================================${NC}"
echo -e "${BLUE}          Function App Deployment from ACR Script          ${NC}"
echo -e "${BLUE}============================================================${NC}"
echo -e "${YELLOW}This script will deploy a Function App from ACR to Azure.${NC}"
echo -e "${YELLOW}It will run the following steps:${NC}"
echo -e "${YELLOW}1. Deploy Function App using the image from ACR${NC}"
echo -e "${YELLOW}2. Fix container configuration${NC}"
echo -e "${YELLOW}3. Set required app settings from parameters.json${NC}"
echo -e "${YELLOW}4. Restart Function App and wait for initialization${NC}"
echo -e "${YELLOW}5. Verify Function App configuration${NC}"
echo -e "${BLUE}============================================================${NC}"
echo -e "${YELLOW}Using resource group: ${GREEN}$RESOURCE_GROUP${NC}"
echo -e "${YELLOW}Using location: ${GREEN}$LOCATION${NC}"
echo -e "${BLUE}============================================================${NC}"

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Set a longer timeout for Azure CLI commands
export AZURE_CLI_OPERATION_TIMEOUT_SECONDS=600

# Step 1: Login to Azure (if not already logged in)
log "\n${BLUE}Step 1: Checking Azure login status...${NC}"
az account show > /dev/null 2>&1 || {
    log "${YELLOW}Logging in to Azure...${NC}"
    az login --use-device-code # More robust for automated environments
}
log_success "Azure login verified."

# Read parameters from the JSON file prepared by the Python script
log "${YELLOW}Reading parameters from $PARAMETERS_FILE...${NC}"
if [ ! -f "$PARAMETERS_FILE" ]; then
    log_error "Parameters file $PARAMETERS_FILE not found. This file should be generated by the Python orchestrator."
    exit 1
fi

# Extract necessary values that the Bicep template itself might not directly use for naming or pre-checks
# The Bicep template will use most parameters directly.
PROJECT_ID=$(jq -r '.parameters.projectId.value' "$PARAMETERS_FILE")
FUNCTION_APP_NAME=$(jq -r '.parameters.functionAppName.value' "$PARAMETERS_FILE") # This is now generated by Python
ACR_NAME=$(jq -r '.parameters.acrName.value' "$PARAMETERS_FILE")
CONTAINER_IMAGE_NAME=$(jq -r '.parameters.containerImageName.value' "$PARAMETERS_FILE")
CONTAINER_IMAGE_TAG=$(jq -r '.parameters.containerImageTag.value' "$PARAMETERS_FILE")

# Validate essential parameters were read
if [ -z "$PROJECT_ID" ] || [ -z "$FUNCTION_APP_NAME" ] || [ -z "$ACR_NAME" ] || [ -z "$CONTAINER_IMAGE_NAME" ] || [ -z "$CONTAINER_IMAGE_TAG" ]; then
    log_error "Error: Missing essential parameters (projectId, functionAppName, acrName, containerImageName, containerImageTag) in $PARAMETERS_FILE."
    exit 1
fi
log_success "Successfully read essential parameters from $PARAMETERS_FILE."
log "Function App Name to be deployed: $FUNCTION_APP_NAME"

# Save the function app name to a file for later use by other scripts/tests
# This path should be relative to the main project directory if deploy_project_resources.py is in the root
# Assuming this script is in scripts/ACR_deployment/
# The Python script is in the root, so it creates scripts/ACR_deployment/parameters_{project_id}.json
# The function_app_name.txt should also be in scripts/ACR_deployment/
output_dir=$(dirname "$0")
echo "$FUNCTION_APP_NAME" > "${output_dir}/function_app_name.txt"
log_success "Saved function app name to ${output_dir}/function_app_name.txt"


# Step 2: Check if resource group exists (optional, Bicep can create if configured, but good for pre-check)
log "\n${BLUE}Step 2: Checking if resource group $RESOURCE_GROUP exists...${NC}"
if ! az group show --name "$RESOURCE_GROUP" > /dev/null 2>&1; then
    log_warning "Resource group $RESOURCE_GROUP does not exist. The Bicep deployment might create it if properly configured, or fail."
    # Depending on policy, you might want to exit or let Bicep handle it.
    # For now, we'll let Bicep try.
else
    log_success "Resource group $RESOURCE_GROUP exists."
fi

# Step 3: Check if ACR exists and verify image (optional, Bicep can reference existing ACR)
log "\n${BLUE}Step 3: Checking ACR $ACR_NAME and verifying image ${CONTAINER_IMAGE_NAME}:${CONTAINER_IMAGE_TAG}...${NC}"
if ! az acr show --name "$ACR_NAME" --resource-group "$RESOURCE_GROUP" > /dev/null 2>&1; then # Assuming ACR is in the same RG for this check
    log_error "Error: ACR $ACR_NAME does not exist or is not accessible in resource group $RESOURCE_GROUP. Please create/configure it first."
    # Note: If ACR is in a different RG, this check needs adjustment or rely on Bicep's ability to find it via subscription.
    # exit 1 # Exiting because Bicep deployment will likely fail.
    log_warning "ACR $ACR_NAME not found in $RESOURCE_GROUP. Bicep deployment might fail if it cannot access it."
else
    log_success "ACR $ACR_NAME exists/accessible in $RESOURCE_GROUP."
    log "${YELLOW}Verifying image exists in ACR...${NC}"
    if ! az acr repository show --name "$ACR_NAME" --image "${CONTAINER_IMAGE_NAME}:${CONTAINER_IMAGE_TAG}" > /dev/null 2>&1; then
        log_error "Error: Image ${CONTAINER_IMAGE_NAME}:${CONTAINER_IMAGE_TAG} not found in ACR $ACR_NAME."
        log "${YELLOW}Available images in repository ${CONTAINER_IMAGE_NAME}:${NC}"
        az acr repository show-tags --name "$ACR_NAME" --repository "$CONTAINER_IMAGE_NAME" --output table
        exit 1
    fi
    log_success "Image ${CONTAINER_IMAGE_NAME}:${CONTAINER_IMAGE_TAG} found in ACR $ACR_NAME."
fi


# Step 4: Deploy Function App using Bicep
log "\n${BLUE}Step 4: Deploying Function App $FUNCTION_APP_NAME using Bicep...${NC}"
log "${YELLOW}Bicep template: scripts/ACR_deployment/function_app_deployment.bicep${NC}"
log "${YELLOW}Parameters file: $PARAMETERS_FILE${NC}"

# Validate the Bicep template first (optional but recommended)
log "${YELLOW}Validating Bicep template...${NC}"
VALIDATE_CMD="az deployment group validate \
  --resource-group \"$RESOURCE_GROUP\" \
  --template-file scripts/ACR_deployment/function_app_deployment.bicep \
  --parameters @\"$PARAMETERS_FILE\"" # All params are now in the file

VALIDATION_RESULT=$(retry_command 3 "$VALIDATE_CMD")
VALIDATION_STATUS=$?

if [ $VALIDATION_STATUS -ne 0 ]; then
    log_error "Bicep template validation failed. Please check the template and parameters file ($PARAMETERS_FILE)."
    log_error "Validation output: $VALIDATION_RESULT"
    log_warning "Will attempt deployment anyway, as validation sometimes fails even when deployment would succeed."
else
    log_success "Bicep template validation successful."
fi

# Deploy the Bicep template
log "${YELLOW}Executing Bicep deployment for Function App...${NC}"
DEPLOY_CMD="az deployment group create \
  --resource-group \"$RESOURCE_GROUP\" \
  --template-file scripts/ACR_deployment/function_app_deployment.bicep \
  --parameters @\"$PARAMETERS_FILE\" \
  --name \"deploy-func-${PROJECT_ID}-${TIMESTAMP}\" \
  --no-wait" # Using --no-wait and then monitoring

DEPLOYMENT_INITIATED_RESULT=$(eval $DEPLOY_CMD 2>&1)
DEPLOYMENT_INITIATED_STATUS=$?

if [ $DEPLOYMENT_INITIATED_STATUS -ne 0 ]; then
    log_error "Failed to initiate Bicep deployment for Function App."
    log_error "Initiation output: $DEPLOYMENT_INITIATED_RESULT"
    exit 1
else
    log_success "Bicep deployment for Function App initiated successfully. Monitoring progress..."
    # Monitor the deployment
    DEPLOYMENT_NAME="deploy-func-${PROJECT_ID}-${TIMESTAMP}"
    max_monitor_attempts=60 # 60 attempts * 30s = 30 minutes
    monitor_attempt=0
    deployment_state=""

    while [ $monitor_attempt -lt $max_monitor_attempts ]; do
        monitor_attempt=$((monitor_attempt + 1))
        deployment_state=$(az deployment group show --resource-group "$RESOURCE_GROUP" --name "$DEPLOYMENT_NAME" --query "properties.provisioningState" -o tsv 2>/dev/null)
        log "Deployment state ($monitor_attempt/$max_monitor_attempts): $deployment_state"
        if [ "$deployment_state" == "Succeeded" ]; then
            log_success "Bicep deployment for Function App Succeeded."
            break
        elif [ "$deployment_state" == "Failed" ] || [ "$deployment_state" == "Canceled" ]; then
            log_error "Bicep deployment for Function App $deployment_state."
            ERROR_DETAILS=$(az deployment group show --resource-group "$RESOURCE_GROUP" --name "$DEPLOYMENT_NAME" --query "properties.error" -o json)
            log_error "Error details: $ERROR_DETAILS"
            exit 1
        fi
        sleep 30
    done

    if [ "$deployment_state" != "Succeeded" ]; then
        log_error "Bicep deployment for Function App did not succeed within the timeout period. Last state: $deployment_state"
        exit 1
    fi
fi

# Step 5: (REMOVED) Fix container configuration - Bicep now handles this.
# Step 6: (REMOVED) Set required app settings from parameters.json - Bicep now handles this.

# Step 7: Restart Function App and wait for initialization (Still important after Bicep deployment)
echo -e "\n${BLUE}Step 7: Restarting Function App and waiting for initialization...${NC}"
echo -e "${YELLOW}Restarting Function App: $FUNCTION_APP_NAME...${NC}"
az functionapp restart --name $FUNCTION_APP_NAME --resource-group $RESOURCE_GROUP

echo -e "${YELLOW}Waiting for Function App to initialize (3 minutes)...${NC}"
sleep 180  # Wait 3 minutes
echo -e "${GREEN}Wait completed. Function App should now be fully initialized.${NC}"

# Step 8: Verify Function App configuration
echo -e "\n${BLUE}Step 8: Verifying Function App configuration...${NC}"
echo -e "${YELLOW}Checking Function App status...${NC}"
STATUS=$(az functionapp show --name $FUNCTION_APP_NAME --resource-group $RESOURCE_GROUP --query state -o tsv)
echo -e "${GREEN}Function App status: $STATUS${NC}"

# Check container configuration
echo -e "${YELLOW}Checking container configuration...${NC}"
CONTAINER_CONFIG=$(az functionapp config container show --name $FUNCTION_APP_NAME --resource-group $RESOURCE_GROUP)
echo -e "${GREEN}Container configuration:${NC}"
echo "$CONTAINER_CONFIG" | jq

# Get the Function App URL
echo -e "${YELLOW}Getting Function App URL...${NC}"
FUNCTION_APP_URL=$(az functionapp show --name $FUNCTION_APP_NAME --resource-group $RESOURCE_GROUP --query defaultHostName -o tsv)

if [ -z "$FUNCTION_APP_URL" ]; then
    echo -e "${RED}Warning: Could not retrieve Function App URL. The deployment might have failed or is still in progress.${NC}"
else
    echo -e "${GREEN}Function App URL: https://$FUNCTION_APP_URL${NC}"
fi

# Check if Function App exists as a final verification
log "${YELLOW}Performing final verification of Function App...${NC}"
if az functionapp show --name $FUNCTION_APP_NAME --resource-group $RESOURCE_GROUP --query name -o tsv > /dev/null 2>&1; then
    log_success "Function App $FUNCTION_APP_NAME exists and is accessible."
    DEPLOYMENT_SUCCESS=true
else
    log_error "Function App $FUNCTION_APP_NAME does not exist or is not accessible."
    DEPLOYMENT_SUCCESS=false
fi

# Print summary
log "\n${BLUE}============================================================${NC}"
if [ "$DEPLOYMENT_SUCCESS" = true ]; then
    log_success "Function App Deployment from ACR completed successfully!"
else
    log_warning "Function App Deployment from ACR completed with issues."
fi
log "${BLUE}============================================================${NC}"
log "${YELLOW}Summary:${NC}"
log "${YELLOW}1. Deployed Function App using the image from ACR${NC}"
log "${YELLOW}2. Fixed container configuration${NC}"
log "${YELLOW}3. Set required app settings from parameters.json${NC}"
log "${YELLOW}4. Restarted Function App and waited for initialization${NC}"
log "${YELLOW}5. Verified Function App configuration${NC}"
log "${BLUE}============================================================${NC}"
log "${YELLOW}Function App Name: ${GREEN}$FUNCTION_APP_NAME${NC}"
log "${YELLOW}Function App URL: ${GREEN}https://$FUNCTION_APP_URL${NC}"
log "${YELLOW}To check the Function App logs, run:${NC}"
log "az functionapp log tail --name $FUNCTION_APP_NAME --resource-group $RESOURCE_GROUP"
log "${BLUE}============================================================${NC}"
log "Detailed logs available at: $LOG_FILE"

# Return appropriate exit code
if [ "$DEPLOYMENT_SUCCESS" = true ]; then
    log_success "Exiting with success code 0"
    exit 0
else
    log_error "Exiting with error code 1"
    exit 1
fi
